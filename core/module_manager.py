"""
模块管理器 - 处理功能模块的动态加载和权限控制（重构版本）
"""
from PyQt5.QtCore import QObject, pyqtSignal
from typing import Dict, List, Callable, Optional
import importlib
import sys
import os
import logging
from modules.communication_manager import get_communication_manager, get_message_bus
from modules.base_module import BaseModule, CommonModule, TeacherModule

class ModuleInfo:
    """模块信息类"""
    def __init__(self, name: str, display_name: str, module_path: str, 
                 class_name: str, is_common: bool = True, 
                 requires_auth: bool = False, tooltip: str = ""):
        self.name = name
        self.display_name = display_name
        self.module_path = module_path
        self.class_name = class_name
        self.is_common = is_common  # 是否为通用功能
        self.requires_auth = requires_auth  # 是否需要认证
        self.tooltip = tooltip
        self.instance = None  # 模块实例
        self.is_loaded = False  # 是否已加载

class ModuleManager(QObject):
    """重构后的模块管理器"""
    
    # 信号定义
    module_loaded = pyqtSignal(str)  # 模块加载成功信号
    module_failed = pyqtSignal(str, str)  # 模块加载失败信号
    module_initialized = pyqtSignal(str)  # 模块初始化成功信号
    module_cleanup = pyqtSignal(str)  # 模块清理信号
    
    def __init__(self):
        super().__init__()
        self.modules: Dict[str, ModuleInfo] = {}
        self.active_modules: Dict[str, BaseModule] = {}  # 活跃的模块实例
        self.is_authenticated = False
        self.user_role = 'guest'
        self.auth_manager = None
        
        # 通信管理器
        self.comm_manager = get_communication_manager()
        self.message_bus = get_message_bus()
        
        # 日志记录器
        self.logger = logging.getLogger("module_manager")
        
        # 初始化模块配置
        self._init_module_config()
        
        # 设置通信管理器的消息处理
        self._setup_communication()
    
    def _init_module_config(self):
        """初始化模块配置"""
        # 通用功能模块（重构版本优先）
        common_modules = [
            ModuleInfo(
                name="udp_discovery",
                display_name="设备发现",
                module_path="modules.udp_discovery",
                class_name="UDPDiscoveryModule",
                is_common=True,
                tooltip="UDP设备发现功能"
            ),
            ModuleInfo(
                name="whiteboard",
                display_name="白板",
                module_path="modules.whiteboard_refactored",
                class_name="WhiteboardModule",
                is_common=True,
                tooltip="电子白板工具"
            ),
            ModuleInfo(
                name="file_sharing",
                display_name="文件传输",
                module_path="modules.file_sharing",
                class_name="FileSharingApp",
                is_common=True,
                tooltip="设备间文件传输"
            ),
            ModuleInfo(
                name="screen_broadcast",
                display_name="屏幕广播",
                module_path="modules.screen_broadcast_refactored",
                class_name="ScreenBroadcastModule",
                is_common=True,
                tooltip="屏幕广播功能"
            ),
            ModuleInfo(
                name="screen_dlan",
                display_name="投屏",
                module_path="modules.screen_dlan_mod",
                class_name="ScreenDLAN",
                is_common=True,
                tooltip="本机投屏功能"
            ),
        ]
        
        # 教师专用功能模块（重构版本优先）
        teacher_modules = [
            ModuleInfo(
                name="device_dashboard",
                display_name="设备看板",
                module_path="modules.device_dashboard",
                class_name="DeviceDashboardModule",
                is_common=False,
                requires_auth=True,
                tooltip="设备状态监控和管理"
            ),
            ModuleInfo(
                name="random_picker",
                display_name="随机点名",
                module_path="modules.random_picker",
                class_name="RandomPickerModule",
                is_common=False,
                requires_auth=True,
                tooltip="随机点名功能"
            ),
            ModuleInfo(
                name="whiteboard_collaboration",
                display_name="白板协作",
                module_path="modules.whiteboard_collaboration",
                class_name="WhiteboardCollaborationModule",
                is_common=False,
                requires_auth=True,
                tooltip="白板协作功能"
            ),
            ModuleInfo(
                name="device_control",
                display_name="设备控制",
                module_path="modules.device_control",
                class_name="DeviceControlModule",
                is_common=False,
                requires_auth=True,
                tooltip="设备开关机控制"
            ),
            ModuleInfo(
                name="dual_screen",
                display_name="主副屏模式",
                module_path="modules.dual_screen",
                class_name="DualScreenModule",
                is_common=False,
                requires_auth=True,
                tooltip="主副屏模式功能"
            ),
            ModuleInfo(
                name="virtual_keyboard",
                display_name="虚拟键盘",
                module_path="modules.virtual_keyboard",
                class_name="VirtualKeyboardModule",
                is_common=False,
                requires_auth=True,
                tooltip="虚拟键盘功能"
            ),
            ModuleInfo(
                name="group_screen_monitor",
                display_name="小组屏幕看板",
                module_path="modules.group_screen_monitor",
                class_name="GroupScreenMonitorModule",
                is_common=False,
                requires_auth=True,
                tooltip="小组屏幕看板功能"
            ),
            ModuleInfo(
                name="danmaku",
                display_name="弹幕",
                module_path="modules.danmaku_refactored",
                class_name="DanmakuModule",
                is_common=False,
                requires_auth=True,
                tooltip="弹幕系统"
            ),
            ModuleInfo(
                name="group_screen",
                display_name="互动",
                module_path="modules.group_screen",
                class_name="Groupscreen",
                is_common=False,
                requires_auth=True,
                tooltip="多屏互动管理"
            ),
            # ModuleInfo(
            #     name="control_pc",
            #     display_name="控制",
            #     module_path="modules.control_pc",
            #     class_name="ControlPCAPP",
            #     is_common=False,
            #     requires_auth=True,
            #     tooltip="设备控制"
            # ),
            ModuleInfo(
                name="open_platform",
                display_name="平台",
                module_path="modules.open_platform",
                class_name="OpenPlatformApp",
                is_common=False,
                requires_auth=True,
                tooltip="资源平台"
            ),
        ]
        
        # 注册所有模块
        for module in common_modules + teacher_modules:
            self.modules[module.name] = module
    
    def set_auth_status(self, is_authenticated: bool, user_role: str = 'guest'):
        """设置认证状态"""
        self.is_authenticated = is_authenticated
        self.user_role = user_role
        
        # 更新所有活跃模块的认证状态
        for module in self.active_modules.values():
            if isinstance(module, TeacherModule):
                module.set_auth_manager(self.auth_manager)
    
    def set_auth_manager(self, auth_manager):
        """设置认证管理器"""
        self.auth_manager = auth_manager
        
        # 更新所有活跃的教师模块
        for module in self.active_modules.values():
            if isinstance(module, TeacherModule):
                module.set_auth_manager(auth_manager)
    
    def _setup_communication(self):
        """设置通信管理器"""
        # 注册模块管理器的消息处理器
        self.comm_manager.register_message_handler("module_request", self._handle_module_request)
        self.comm_manager.register_message_handler("module_status", self._handle_module_status)
        
        # 连接信号
        self.comm_manager.message_sent.connect(self._on_message_sent)
        self.comm_manager.message_broadcast.connect(self._on_message_broadcast)
    
    def get_available_modules(self) -> List[ModuleInfo]:
        """获取当前可用的模块列表"""
        available = []
        
        for module in self.modules.values():
            # 通用功能始终可用
            if module.is_common:
                available.append(module)
            # 教师专用功能需要认证
            elif module.requires_auth and self.is_authenticated and self.user_role == 'teacher':
                available.append(module)
        
        return available
    
    def can_access_module(self, module_name: str) -> bool:
        """检查是否可以访问指定模块"""
        module = self.modules.get(module_name)
        if not module:
            return False
        
        # 通用功能始终可访问
        if module.is_common:
            return True
        
        # 教师专用功能需要认证
        if module.requires_auth:
            return self.is_authenticated and self.user_role == 'teacher'
        
        return False
    
    def load_module(self, module_name: str) -> Optional[BaseModule]:
        """加载指定模块"""
        module_info = self.modules.get(module_name)
        if not module_info:
            self.module_failed.emit(module_name, "模块不存在")
            return None
        
        # 检查访问权限
        if self.auth_manager:
            self.is_authenticated = self.auth_manager.is_authenticated()
            self.user_role = self.auth_manager.get_user_role()

        if not self.can_access_module(module_name):
            self.module_failed.emit(module_name, "没有访问权限")
            return None
        
        # 如果已经加载，返回现有实例
        if module_info.is_loaded and module_info.instance:
            return module_info.instance
        
        try:
            # 动态导入模块
            module = importlib.import_module(module_info.module_path)
            
            # 获取类并创建实例
            module_class = getattr(module, module_info.class_name)
            instance = module_class()
            
            # 如果是新架构的模块，进行额外设置
            if isinstance(instance, BaseModule):
                # 设置通信管理器
                instance.set_communication_manager(self.comm_manager)
                
                # 如果是教师模块，设置认证管理器
                if isinstance(instance, TeacherModule) and self.auth_manager:
                    instance.set_auth_manager(self.auth_manager)
                
                # 初始化模块
                if not instance.initialize():
                    error_msg = f"模块初始化失败: {module_name}"
                    self.module_failed.emit(module_name, error_msg)
                    return None
                
                # 连接模块信号
                instance.module_ready.connect(lambda: self.module_initialized.emit(module_name))
                instance.module_error.connect(lambda msg: self.module_failed.emit(module_name, msg))
            
            # 保存实例
            module_info.instance = instance
            module_info.is_loaded = True
            self.active_modules[module_name] = instance
            
            self.module_loaded.emit(module_name)
            self.logger.info(f"模块加载成功: {module_name}")
            return instance
            
        except Exception as e:
            error_msg = f"加载模块失败: {str(e)}"
            self.logger.error(error_msg)
            self.module_failed.emit(module_name, error_msg)
            return None
    
    def unload_module(self, module_name: str):
        """卸载指定模块"""
        module_info = self.modules.get(module_name)
        if not module_info:
            return
        
        # 关闭模块实例
        if module_info.instance:
            try:
                # 如果是新架构的模块，调用cleanup方法
                if isinstance(module_info.instance, BaseModule):
                    module_info.instance.cleanup()
                elif hasattr(module_info.instance, 'close'):
                    module_info.instance.close()
                    
                self.module_cleanup.emit(module_name)
                self.logger.info(f"模块卸载成功: {module_name}")
                
            except Exception as e:
                self.logger.error(f"关闭模块 {module_name} 时出错: {e}")
        
        # 清理引用
        module_info.instance = None
        module_info.is_loaded = False
        if module_name in self.active_modules:
            del self.active_modules[module_name]
    
    def get_module_instance(self, module_name: str) -> Optional[object]:
        """获取模块实例"""
        module_info = self.modules.get(module_name)
        if module_info and module_info.is_loaded:
            return module_info.instance
        return None
    
    def is_module_active(self, module_name: str) -> bool:
        """检查模块是否处于活跃状态"""
        return module_name in self.active_modules
    
    def close_all_modules(self):
        """关闭所有活跃模块"""
        for module_name in list(self.active_modules.keys()):
            self.unload_module(module_name)
    
    def reload_module(self, module_name: str) -> Optional[object]:
        """重新加载模块"""
        # 先卸载
        self.unload_module(module_name)
        
        # 重新导入模块（用于开发调试）
        module_info = self.modules.get(module_name)
        if module_info:
            try:
                if module_info.module_path in sys.modules:
                    importlib.reload(sys.modules[module_info.module_path])
            except Exception as e:
                print(f"重新导入模块 {module_name} 失败: {e}")
        
        # 重新加载
        return self.load_module(module_name)
    
    def get_module_info(self, module_name: str) -> Optional[ModuleInfo]:
        """获取模块信息"""
        return self.modules.get(module_name)
    
    def add_custom_module(self, module_info: ModuleInfo):
        """添加自定义模块"""
        self.modules[module_info.name] = module_info
    
    def remove_module(self, module_name: str):
        """移除模块"""
        if module_name in self.modules:
            self.unload_module(module_name)
            del self.modules[module_name]
    
    def send_message_to_module(self, target_module: str, message_type: str, data: Dict):
        """发送消息到指定模块"""
        return self.comm_manager.send_message("module_manager", target_module, message_type, data)
    
    def broadcast_message_to_modules(self, message_type: str, data: Dict):
        """广播消息到所有模块"""
        return self.comm_manager.broadcast_message("module_manager", message_type, data)
    
    def get_module_communication_stats(self):
        """获取模块通信统计"""
        return self.comm_manager.get_message_stats()
    
    def get_registered_modules_info(self):
        """获取已注册模块的详细信息"""
        return self.comm_manager.get_all_modules_info()
    
    def _handle_module_request(self, data: Dict):
        """处理模块请求"""
        request_type = data.get("type")
        module_name = data.get("module")
        
        if request_type == "load" and module_name:
            self.load_module(module_name)
        elif request_type == "unload" and module_name:
            self.unload_module(module_name)
        elif request_type == "reload" and module_name:
            self.reload_module(module_name)
    
    def _handle_module_status(self, data: Dict):
        """处理模块状态消息"""
        module_name = data.get("module")
        status = data.get("status")
        
        if module_name and status:
            self.logger.info(f"模块 {module_name} 状态更新: {status}")
    
    def _on_message_sent(self, sender: str, target: str, message_type: str, data: Dict):
        """消息发送事件处理"""
        self.logger.debug(f"消息已发送: {sender} -> {target} ({message_type})")
    
    def _on_message_broadcast(self, sender: str, message_type: str, data: Dict):
        """消息广播事件处理"""
        self.logger.debug(f"消息已广播: {sender} ({message_type})")
    
    def get_communication_manager(self):
        """获取通信管理器实例"""
        return self.comm_manager
    
    def get_message_bus(self):
        """获取消息总线实例"""
        return self.message_bus