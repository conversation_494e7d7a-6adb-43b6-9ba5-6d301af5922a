#!/usr/bin/env python3
"""
Core教学工具启动脚本
"""
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """主函数"""
    try:
        # 导入主应用程序
        from main_app import main as app_main
        
        print("正在启动智慧课堂教学工具...")
        print("版本: 2.0.0")
        print("支持功能:")
        print("  - 通用功能: 白板、文件传输、屏幕广播、投屏")
        print("  - 教师专用功能: 互动管理、弹幕系统、设备控制、资源平台")
        print("  - 认证系统: 支持教师登录和游客模式")
        print("  - 模块化架构: 动态加载功能模块")
        print("-" * 50)
        
        # 启动应用程序
        return app_main()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保所有依赖库已正确安装")
        print("运行: pip install -r requirements.txt")
        return 1
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())