"""
登录对话框 - 教师登录界面
"""
import sys
import os
import pickle
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *

class LoginDialog(QDialog):
    """教师登录对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("教师登录")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                border-radius: 8px;
            }
        """)
        self.setup_ui()
        self.result_data = None


    def setup_ui(self):
        """设置用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 30, 40, 30)

        # 标题
        title_label = QLabel("智慧课堂教学工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont("Noto Sans CJK SC", 18, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        main_layout.addWidget(title_label)

        # 副标题
        subtitle_label = QLabel("教师登录")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_font = QFont("Noto Sans CJK SC", 12)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet("color: #7f8c8d; margin-bottom: 20px;")
        main_layout.addWidget(subtitle_label)

        # 用户名
        username_label = QLabel("用户名:")
        username_label.setFont(QFont("Noto Sans CJK SC", 11))
        username_label.setStyleSheet("color: #333;")

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入您的用户名")
        self.username_input.setStyleSheet(self.get_input_style())
        self.username_input.returnPressed.connect(self.login)


        # 密码
        password_label = QLabel("密  码:")
        password_label.setFont(QFont("Noto Sans CJK SC", 11))
        password_label.setStyleSheet("color: #333;")

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入您的密码")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(self.get_input_style())
        self.password_input.returnPressed.connect(self.login)

        # 布局
        form_layout = QGridLayout()
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(self.username_input, 0, 1)
        form_layout.addWidget(password_label, 1, 0)
        form_layout.addWidget(self.password_input, 1, 1)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 10)

        main_layout.addLayout(form_layout)

        # 记住密码
        self.remember_checkbox = QCheckBox("记住密码")
        self.remember_checkbox.setFont(QFont("Noto Sans CJK SC", 10))
        self.remember_checkbox.setStyleSheet("QCheckBox { color: #555; spacing: 5px; }")
        main_layout.addWidget(self.remember_checkbox, alignment=Qt.AlignLeft)
        main_layout.addSpacing(10)


        # 按钮
        button_layout = QHBoxLayout()
        self.login_btn = QPushButton("登 录")
        self.login_btn.setFont(QFont("Noto Sans CJK SC", 11, QFont.Bold))
        self.login_btn.setCursor(Qt.PointingHandCursor)
        self.login_btn.setStyleSheet(self.get_button_style("#4CAF50", "#45a049"))
        self.login_btn.clicked.connect(self.login)

        self.cancel_btn = QPushButton("取 消")
        self.cancel_btn.setFont(QFont("Noto Sans CJK SC", 11))
        self.cancel_btn.setCursor(Qt.PointingHandCursor)
        self.cancel_btn.setStyleSheet(self.get_button_style("#f44336", "#e53935"))
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.login_btn)
        button_layout.addWidget(self.cancel_btn)
        main_layout.addLayout(button_layout)
        main_layout.addSpacing(10)

        self.guest_btn = QPushButton("访客登录")
        self.guest_btn.setFont(QFont("Noto Sans CJK SC", 10))
        self.guest_btn.setCursor(Qt.PointingHandCursor)
        self.guest_btn.setStyleSheet(self.get_button_style("#2196F3", "#1e88e5", flat=True))
        self.guest_btn.clicked.connect(self.on_guest_clicked)
        main_layout.addWidget(self.guest_btn, alignment=Qt.AlignCenter)


        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setFont(QFont("Noto Sans CJK SC", 10))
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #d32f2f; padding: 5px;")
        self.status_label.setMinimumHeight(25)
        main_layout.addWidget(self.status_label)

        # 加载凭据
        self.load_credentials()
        self.username_input.setFocus()


    def get_input_style(self):
        return """
            QLineEdit {
                background-color: #fff;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #2196F3;
            }
        """

    def get_button_style(self, bg, hover, color="#fff", flat=False):
        if flat:
            return f"""
                QPushButton {{
                    color: {bg};
                    background-color: transparent;
                    border: none;
                    text-decoration: underline;
                    font-size: 13px;
                }}
                QPushButton:hover {{
                    color: {hover};
                }}
            """
        return f"""
            QPushButton {{
                background-color: {bg};
                color: {color};
                border: none;
                border-radius: 4px;
                padding: 10px;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {hover};
            }}
        """

    def load_credentials(self):
        try:
            if os.path.exists("registration.pkl"):
                with open("registration.pkl", "rb") as f:
                    data = pickle.load(f)
                    if data.get("remember_me"):
                        self.username_input.setText(data.get("username", ""))
                        self.password_input.setText(data.get("password", ""))
                        self.remember_checkbox.setChecked(True)
        except (FileNotFoundError, pickle.UnpicklingError):
            pass # 首次运行或文件损坏

    def save_credentials(self):
        data = {
            "username": self.username_input.text(),
            "password": self.password_input.text(),
            "remember_me": self.remember_checkbox.isChecked()
        }
        try:
            if self.remember_checkbox.isChecked():
                with open("registration.pkl", "wb") as f:
                    pickle.dump(data, f)
            else:
                # 如果不记住密码，则清除保存的凭据
                if os.path.exists("registration.pkl"):
                    os.remove("registration.pkl")
        except (IOError, pickle.PicklingError) as e:
            self.status_label.setText(f"无法保存凭据: {e}")


    def login(self):
        self.save_credentials()
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if not username:
            self.status_label.setText("请输入用户名")
            return
        if not password:
            self.status_label.setText("请输入密码")
            return

        # 打包数据并接受对话框
        self.result_data = {
            "mode": "login",
            "username": username,
            "password": password,
            "role": "teacher"
        }
        self.accept()

    def on_guest_clicked(self):
        self.result_data = {"mode": "guest", "username": "guest", "role": "guest"}
        self.accept()

    def get_result(self):
        return self.result_data