"""
认证管理器 - 处理教师登录认证和JWT令牌验证
"""
import jwt
import json
import hashlib
import requests
from datetime import datetime, timedelta
from PyQt5.QtCore import QObject, pyqtSignal
from typing import Optional, Dict, Any

class AuthManager(QObject):
    """认证管理器类"""
    
    # 信号定义
    login_success = pyqtSignal(dict)  # 登录成功信号，传递用户信息
    login_failed = pyqtSignal(str)    # 登录失败信号，传递错误信息
    logout_success = pyqtSignal()     # 登出成功信号
    
    def __init__(self):
        super().__init__()
        self.jwt_secret = "smart_classroom_secret_key"  # JWT密钥
        self.token_file = "./auth_token.json"  # 令牌存储文件
        self.current_user = None  # 当前登录用户信息
        self.current_token = None  # 当前JWT令牌
        self.backend_url = "https://192.168.0.11:5000"  # 后端API地址
        
    def load_cached_token(self) -> bool:
        """加载缓存的令牌"""
        try:
            with open(self.token_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                token = data.get('token')
                if token and self.verify_token(token):
                    self.current_token = token
                    self.current_user = data.get('user_info')
                    return True
        except (FileNotFoundError, json.JSONDecodeError, KeyError):
            pass
        return False
    
    def save_token(self, token: str, user_info: dict):
        """保存令牌到本地"""
        try:
            data = {
                'token': token,
                'user_info': user_info,
                'timestamp': datetime.now().isoformat()
            }
            with open(self.token_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存令牌失败: {e}")
    
    def clear_token(self):
        """清除本地令牌"""
        try:
            import os
            if os.path.exists(self.token_file):
                os.remove(self.token_file)
        except Exception as e:
            print(f"清除令牌失败: {e}")
    
    def verify_token(self, token: str) -> bool:
        """验证JWT令牌"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            # 检查令牌是否过期
            exp_timestamp = payload.get('exp')
            if exp_timestamp:
                # 处理时间戳格式
                if isinstance(exp_timestamp, (int, float)):
                    exp_time = datetime.fromtimestamp(exp_timestamp)
                else:
                    exp_time = datetime.fromisoformat(str(exp_timestamp))
                
                if exp_time < datetime.now():
                    return False
            return True
        except (jwt.InvalidTokenError, ValueError, TypeError) as e:
            print(f"令牌验证失败: {e}")
            return False
    
    def create_token(self, user_info: dict) -> str:
        """创建JWT令牌"""
        now = datetime.now()
        payload = {
            'user_id': user_info.get('id'),
            'username': user_info.get('username'),
            'role': user_info.get('role', 'teacher'),
            'exp': int((now + timedelta(hours=24)).timestamp()),  # 24小时过期，转换为时间戳
            'iat': int(now.timestamp())  # 转换为时间戳
        }
        return jwt.encode(payload, self.jwt_secret, algorithm='HS256')
    
    def login_with_credentials(self, username: str, password: str) -> bool:
        """使用用户名密码登录"""
        try:
            # 发送登录请求到后端
            response = requests.post(
                f"{self.backend_url}/api/auth/login",
                json={
                    'email': username,
                    'password': password
                },
                timeout=10,
                verify=False # 禁用SSL证书验证（因为使用自签名证书）
            )
            
            if response.status_code == 200:
                data = response.json()
                user_info = data.get('user')
                
                # 创建本地JWT令牌
                token = self.create_token(user_info)
                
                # 保存用户信息和令牌
                self.current_user = user_info
                self.current_token = token
                self.save_token(token, user_info)
                
                # 发送登录成功信号
                self.login_success.emit(user_info)
                return True
            else:
                error_msg = response.json().get('message', '登录失败')
                self.login_failed.emit(error_msg)
                return False
                
        except requests.RequestException as e:
            # 网络错误，尝试离线验证（如果有缓存的用户信息）
            if self._offline_login(username, password):
                return True
            self.login_failed.emit(f"网络连接失败: {str(e)}")
            return False
        except Exception as e:
            self.login_failed.emit(f"登录异常: {str(e)}")
            return False
    
    def _offline_login(self, username: str, password: str) -> bool:
        """离线登录验证"""
        # 这里可以实现基于本地缓存的离线登录逻辑
        # 暂时返回False，表示不支持离线登录
        return False
    
    def logout(self):
        """登出"""
        self.current_user = None
        self.current_token = None
        self.clear_token()
        self.logout_success.emit()
    
    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        return self.current_user is not None and self.current_token is not None
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        return self.current_user
    
    def get_user_role(self) -> str:
        """获取用户角色"""
        if self.current_user:
            return self.current_user.get('role', 'guest')
        return 'guest'
    
    def is_teacher(self) -> bool:
        """检查是否为教师"""
        return self.get_user_role() == 'teacher'
    
    def get_current_courses(self) -> list:
        """获取当前用户的课程列表"""
        if not self.is_authenticated():
            return []
        
        try:
            headers = {'Authorization': f'Bearer {self.current_token}'}
            response = requests.get(
                f"{self.backend_url}/api/courses/my-courses",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json().get('courses', [])
        except Exception as e:
            print(f"获取课程列表失败: {e}")
        
        return []
    
    def refresh_token(self) -> bool:
        """刷新令牌"""
        if not self.current_token:
            return False
        
        try:
            headers = {'Authorization': f'Bearer {self.current_token}'}
            response = requests.post(
                f"{self.backend_url}/api/auth/refresh",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                new_token = data.get('token')
                if new_token:
                    self.current_token = new_token
                    self.save_token(new_token, self.current_user)
                    return True
        except Exception as e:
            print(f"刷新令牌失败: {e}")
        
        return False