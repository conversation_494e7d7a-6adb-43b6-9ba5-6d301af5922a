# 智慧课堂模块化架构文档

## 概述

本文档描述了智慧课堂系统的新模块化架构，该架构将现有功能重构为通用功能模块和教师专用功能模块，并实现了模块间通信机制。

## 架构组件

### 1. 基础模块类 (BaseModule)

所有功能模块的基类，提供以下核心功能：

- **模块生命周期管理**：初始化、清理、状态管理
- **通信能力**：模块间消息发送和接收
- **配置管理**：模块配置的设置和获取
- **日志记录**：统一的日志记录机制

#### 主要方法

```python
class BaseModule(QObject):
    def initialize() -> bool          # 初始化模块
    def cleanup()                     # 清理模块资源
    def is_ready() -> bool           # 检查模块是否就绪
    def send_message(target, type, data)  # 发送消息
    def broadcast_message(type, data)     # 广播消息
    def handle_message(sender, type, data) # 处理消息
```

### 2. 通用功能模块 (CommonModule)

继承自BaseModule，用于实现不需要认证的通用功能：

- 白板模块 (WhiteboardModule)
- 文件传输模块
- 屏幕广播模块
- 投屏模块

#### 特点

- 无需认证即可使用
- 所有用户角色都可访问
- 提供基础教学功能

### 3. 教师专用模块 (TeacherModule)

继承自BaseModule，用于实现需要教师认证的专用功能：

- 弹幕系统模块 (DanmakuModule)
- 互动管理模块
- 设备控制模块
- 资源平台模块

#### 特点

- 需要教师认证才能使用
- 提供高级教学管理功能
- 支持学生互动管理

### 4. 通信管理器 (CommunicationManager)

负责模块间的消息传递和通信协调：

#### 功能

- **模块注册管理**：注册和注销模块
- **消息路由**：直接消息发送和广播
- **消息历史**：记录消息传递历史
- **统计信息**：提供通信统计数据

#### 使用示例

```python
# 发送消息到指定模块
comm_manager.send_message("sender", "target", "message_type", data)

# 广播消息到所有模块
comm_manager.broadcast_message("sender", "message_type", data)
```

### 5. 消息总线 (MessageBus)

提供基于主题的发布-订阅消息传递：

#### 功能

- **主题订阅**：模块可订阅感兴趣的主题
- **消息发布**：向主题发布消息
- **订阅管理**：管理主题和订阅者

#### 使用示例

```python
# 订阅主题
message_bus.subscribe("module_name", "topic_name")

# 发布消息到主题
message_bus.publish("sender", "topic_name", data)
```

### 6. 模块管理器 (ModuleManager)

统一管理所有功能模块的加载、卸载和生命周期：

#### 功能

- **模块加载**：动态加载和初始化模块
- **权限控制**：基于用户角色的访问控制
- **生命周期管理**：管理模块的创建和销毁
- **通信集成**：集成通信管理器

## 模块通信机制

### 1. 直接消息传递

模块可以直接向其他模块发送消息：

```python
# 在模块中发送消息
self.send_message("target_module", "message_type", {
    "action": "do_something",
    "data": "some_data"
})
```

### 2. 广播消息

模块可以向所有其他模块广播消息：

```python
# 广播消息
self.broadcast_message("event_occurred", {
    "event": "user_action",
    "timestamp": datetime.now().isoformat()
})
```

### 3. 主题订阅

模块可以订阅特定主题并接收相关消息：

```python
# 订阅主题
message_bus.subscribe(self.module_name, "user_events")

# 发布到主题
message_bus.publish(self.module_name, "user_events", data)
```

## 模块示例

### 白板模块 (WhiteboardModule)

```python
class WhiteboardModule(CommonModule):
    def __init__(self):
        super().__init__("whiteboard", "电子白板", "2.0.0")
    
    def _initialize_module(self) -> bool:
        # 初始化白板窗口和相关资源
        self.whiteboard_window = WhiteboardWindow(self)
        return True
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        if message_type == "clear_board":
            self.clear_board()
        elif message_type == "save_board":
            self.save_board(data.get("filename"))
```

### 弹幕模块 (DanmakuModule)

```python
class DanmakuModule(TeacherModule):
    def __init__(self):
        super().__init__("danmaku", "弹幕系统", "2.0.0")
    
    def _initialize_module(self) -> bool:
        if not self.is_authenticated():
            return False
        
        # 初始化弹幕窗口和Socket.IO连接
        self.danmaku_window = DanmakuWindow(self)
        self.init_socketio_client()
        return True
```

## 配置和扩展

### 添加新模块

1. 继承适当的基类 (CommonModule 或 TeacherModule)
2. 实现必要的抽象方法
3. 在ModuleManager中注册模块信息
4. 实现模块特定的消息处理逻辑

### 模块配置

模块可以通过配置系统进行定制：

```python
# 设置模块配置
module.set_config({
    "server_url": "http://localhost:3000",
    "auto_connect": True,
    "max_connections": 100
})

# 获取配置值
server_url = module.get_config("server_url", "http://default:3000")
```

## 测试

架构包含完整的单元测试套件：

```bash
# 运行所有测试
python test_module_architecture.py
```

测试覆盖：
- 基础模块功能
- 通信管理器
- 消息总线
- 模块管理器
- 模块加载和卸载

## 优势

1. **模块化设计**：功能模块独立，易于维护和扩展
2. **权限控制**：基于角色的访问控制，安全可靠
3. **通信机制**：灵活的模块间通信，支持直接消息和主题订阅
4. **生命周期管理**：统一的模块生命周期管理
5. **可测试性**：完整的单元测试覆盖
6. **可扩展性**：易于添加新功能模块

## 迁移指南

### 从旧架构迁移

1. **保持兼容性**：新架构保持与现有代码的兼容性
2. **逐步迁移**：可以逐个模块进行迁移
3. **测试验证**：每个迁移的模块都有对应的测试

### 最佳实践

1. **模块职责单一**：每个模块专注于特定功能
2. **消息设计**：设计清晰的消息接口
3. **错误处理**：实现完善的错误处理机制
4. **日志记录**：使用统一的日志记录
5. **配置管理**：通过配置系统管理模块参数

## 总结

新的模块化架构提供了一个灵活、可扩展、易维护的系统基础，支持智慧课堂系统的持续发展和功能扩展。通过清晰的模块分离、强大的通信机制和完善的测试覆盖，确保了系统的稳定性和可靠性。