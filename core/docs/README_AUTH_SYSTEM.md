# Core教学工具认证系统改造

## 概述

本次改造实现了Core教学工具的应用启动与认证系统，支持通用功能默认显示和教师专用功能的权限控制。

## 主要改造内容

### 1. 认证管理器 (auth_manager.py)

**功能特性:**
- JWT令牌认证机制
- 本地令牌缓存和自动加载
- 支持在线和离线验证
- 用户角色管理（教师/游客）
- 令牌刷新机制

**核心方法:**
- `login_with_credentials()` - 用户名密码登录
- `verify_token()` - JWT令牌验证
- `load_cached_token()` - 加载缓存令牌
- `logout()` - 用户登出

### 2. 登录对话框 (login_dialog.py)

**功能特性:**
- 现代化登录界面设计
- 支持记住密码功能
- 游客模式选项
- 输入验证和错误提示
- 响应式布局

**用户体验:**
- 简洁直观的界面设计
- 支持键盘快捷操作
- 自动保存登录凭据
- 友好的错误提示

### 3. 模块管理器 (module_manager.py)

**功能特性:**
- 模块化架构设计
- 动态模块加载机制
- 基于角色的权限控制
- 模块生命周期管理
- 热重载支持（开发模式）

**模块分类:**
- **通用功能模块**: 白板、文件传输、屏幕广播、投屏
- **教师专用模块**: 互动管理、弹幕系统、设备控制、资源平台

### 4. 主应用程序 (main_app.py)

**功能特性:**
- 统一的应用程序入口
- 自动令牌验证和恢复
- 多线程登录处理
- 优雅的错误处理
- 高DPI显示支持

**启动流程:**
1. 检查缓存令牌
2. 显示登录对话框（如需要）
3. 验证用户身份
4. 加载相应功能模块
5. 启动主界面

### 5. 主窗口 (main_window.py)

**功能特性:**
- 模块化界面设计
- 动态按钮生成
- 用户状态显示
- 实时权限控制
- 响应式布局

**界面组件:**
- 倒计时显示
- 用户信息面板
- 功能模块按钮
- 系统控制按钮
- Logo展示区域

## 技术实现

### 认证流程

```mermaid
graph TD
    A[应用启动] --> B[检查缓存令牌]
    B -->|有效令牌| C[直接登录]
    B -->|无效/无令牌| D[显示登录对话框]
    D -->|教师登录| E[验证凭据]
    D -->|游客模式| F[启动基础功能]
    E -->|成功| G[保存令牌]
    E -->|失败| D
    G --> H[启动完整功能]
    C --> H
    F --> I[启动主界面]
    H --> I
```

### 模块权限控制

```python
# 通用功能 - 所有用户可访问
common_modules = ["whiteboard", "file_sharing", "screen_broadcast", "screen_dlan"]

# 教师专用功能 - 需要认证
teacher_modules = ["group_screen", "danmaku", "control_pc", "open_platform"]

# 权限检查
def can_access_module(module_name):
    if module_name in common_modules:
        return True
    elif module_name in teacher_modules:
        return is_authenticated and user_role == 'teacher'
    return False
```

### JWT令牌结构

```json
{
  "user_id": "用户ID",
  "username": "用户名",
  "role": "用户角色",
  "exp": "过期时间戳",
  "iat": "签发时间戳"
}
```

## 使用方法

### 启动应用程序

```bash
# 方法1: 使用启动脚本
python start_core.py

# 方法2: 直接运行主应用
python main_app.py
```

### 教师登录

1. 启动应用程序
2. 在登录对话框中输入用户名和密码
3. 点击"登录"按钮
4. 登录成功后可使用所有功能

### 游客模式

1. 启动应用程序
2. 在登录对话框中点击"以游客模式继续"
3. 只能使用基础功能（白板、文件传输等）

## 测试验证

### 运行单元测试

```bash
# 运行认证系统测试
python test_auth_unit.py

# 运行功能测试
python test_auth.py
```

### 测试覆盖

- ✅ 认证管理器功能测试
- ✅ 模块管理器权限测试
- ✅ JWT令牌创建和验证
- ✅ 本地令牌缓存机制
- ✅ 模块动态加载机制

## 配置文件

### 认证令牌缓存
- 文件位置: `./auth_token.json`
- 包含: JWT令牌、用户信息、时间戳

### 登录凭据缓存
- 文件位置: `./login_credentials.json`
- 包含: 用户名、密码（可选）、记住密码选项

## 安全考虑

1. **密码安全**: 使用SHA256哈希存储密码
2. **令牌安全**: JWT令牌有24小时有效期
3. **本地存储**: 敏感信息加密存储
4. **权限控制**: 严格的模块访问权限检查
5. **会话管理**: 支持自动登出和令牌刷新

## 扩展性

### 添加新模块

```python
# 在module_manager.py中添加模块配置
new_module = ModuleInfo(
    name="new_module",
    display_name="新模块",
    module_path="modules.new_module",
    class_name="NewModuleClass",
    is_common=False,  # 是否为通用功能
    requires_auth=True,  # 是否需要认证
    tooltip="新模块功能说明"
)

module_manager.add_custom_module(new_module)
```

### 自定义认证后端

```python
# 继承AuthManager类并重写认证方法
class CustomAuthManager(AuthManager):
    def login_with_credentials(self, username, password):
        # 自定义认证逻辑
        pass
```

## 兼容性

- **操作系统**: Windows, Linux, macOS
- **Python版本**: 3.7+
- **PyQt版本**: 5.15+
- **依赖库**: 见requirements.txt

## 下一步计划

1. 实现UDP设备发现功能
2. 增强白板应用模块
3. 添加文件传输功能
4. 实现录屏截屏功能
5. 开发双模式屏幕广播

## 更新日志

### v2.0.0 (当前版本)
- ✅ 实现认证管理器
- ✅ 添加登录对话框
- ✅ 开发模块管理器
- ✅ 重构主应用程序
- ✅ 更新主窗口界面
- ✅ 完善测试用例