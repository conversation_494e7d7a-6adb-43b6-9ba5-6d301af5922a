"""
随机点名模块 - 教师专用功能
集成后端API获取当前课程学生列表，实现随机点名算法和界面
"""
import sys
import json
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QListWidget, QListWidgetItem,
                            QGroupBox, QTextEdit, QCheckBox, QSpinBox,
                            QMessageBox, QProgressBar, QFrame, QGridLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import QTimer, pyqtSignal, Qt, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPixmap, QPainter
from .base_module import TeacherModule
from .api_client import api_client
import logging

class Student:
    """学生信息类"""
    
    def __init__(self, student_id: str, name: str, username: str, group_id: Optional[str] = None):
        self.student_id = student_id
        self.name = name
        self.username = username
        self.group_id = group_id
        self.pick_count = 0  # 被点名次数
        self.last_picked = None  # 最后被点名时间
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "student_id": self.student_id,
            "name": self.name,
            "username": self.username,
            "group_id": self.group_id,
            "pick_count": self.pick_count,
            "last_picked": self.last_picked.isoformat() if self.last_picked else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Student':
        """从字典创建学生对象"""
        student = cls(
            data["student_id"],
            data["name"],
            data["username"],
            data.get("group_id")
        )
        student.pick_count = data.get("pick_count", 0)
        if data.get("last_picked"):
            student.last_picked = datetime.fromisoformat(data["last_picked"])
        return student

class Group:
    """小组信息类"""
    
    def __init__(self, group_id: str, name: str, members: List[Student]):
        self.group_id = group_id
        self.name = name
        self.members = members
        self.pick_count = 0  # 被点名次数
        self.last_picked = None  # 最后被点名时间
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "group_id": self.group_id,
            "name": self.name,
            "members": [member.to_dict() for member in self.members],
            "pick_count": self.pick_count,
            "last_picked": self.last_picked.isoformat() if self.last_picked else None
        }

class PickRecord:
    """点名记录类"""
    
    def __init__(self, pick_type: str, target_id: str, target_name: str, 
                 course_id: str, timestamp: datetime = None):
        self.pick_type = pick_type  # 'student' or 'group'
        self.target_id = target_id
        self.target_name = target_name
        self.course_id = course_id
        self.timestamp = timestamp or datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "pick_type": self.pick_type,
            "target_id": self.target_id,
            "target_name": self.target_name,
            "course_id": self.course_id,
            "timestamp": self.timestamp.isoformat()
        }

class RandomPickerModule(TeacherModule):
    """随机点名模块"""
    
    # 模块信号
    student_picked = pyqtSignal(object)  # Student
    group_picked = pyqtSignal(object)    # Group
    data_loaded = pyqtSignal()           # 数据加载完成
    
    def __init__(self):
        super().__init__(
            module_name="random_picker",
            display_name="随机点名",
            version="1.0.0"
        )
        
        # 数据
        self.courses: List[Dict[str, Any]] = []
        self.current_course_id: Optional[str] = None
        self.students: Dict[str, Student] = {}
        self.groups: Dict[str, Group] = {}
        self.pick_records: List[PickRecord] = []
        
        # 界面
        self.picker_window = None
        
        # API客户端
        self.api_client = api_client
        
        # 配置
        self.config.update({
            "exclude_recent_picks": True,  # 排除最近被点名的
            "recent_pick_hours": 1,        # 最近点名的时间范围（小时）
            "weight_by_frequency": True,   # 根据点名频率加权
            "max_records": 1000,           # 最大记录数
            "auto_save": True              # 自动保存记录
        })
    
    def get_widget(self):
        return self.picker_window

    def _initialize_module(self) -> bool:
        """初始化随机点名模块"""
        try:
            # 检查认证状态
            if not self.is_authenticated():
                self.logger.error("随机点名模块需要教师认证")
                return False
            
            # 创建界面
            self.picker_window = RandomPickerWindow(self)
            
            # 加载课程数据
            self.load_courses()
            
            # 加载点名记录
            self.load_pick_records()
            
            self.logger.info("随机点名模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"随机点名模块初始化失败: {str(e)}")
            return False
    
    def _cleanup_module(self):
        """清理随机点名模块资源"""
        try:
            # 保存点名记录
            if self.get_config("auto_save", True):
                self.save_pick_records()
            
            # 关闭界面
            if self.picker_window:
                self.picker_window.close()
                self.picker_window = None
            
            # 清理数据
            self.courses.clear()
            self.students.clear()
            self.groups.clear()
            self.pick_records.clear()
            
            self.logger.info("随机点名模块资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理随机点名模块资源时出错: {str(e)}")
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "pick_student":
            course_id = data.get("course_id")
            if course_id:
                self.set_current_course(course_id)
                result = self.pick_random_student()
                self.send_message(sender, "student_picked", result)
        
        elif message_type == "pick_group":
            course_id = data.get("course_id")
            if course_id:
                self.set_current_course(course_id)
                result = self.pick_random_group()
                self.send_message(sender, "group_picked", result)
        
        elif message_type == "get_pick_stats":
            stats = self.get_pick_statistics()
            self.send_message(sender, "pick_stats", stats)
        
        elif message_type == "show_picker":
            self.show()
    
    def show(self):
        """显示随机点名窗口"""
        if self.picker_window:
            self.picker_window.show()
            self.picker_window.raise_()
            self.picker_window.activateWindow()
    
    def hide(self):
        """隐藏随机点名窗口"""
        if self.picker_window:
            self.picker_window.hide()
    
    def close(self):
        """关闭随机点名模块"""
        self.cleanup()
    
    def load_courses(self):
        """加载课程列表"""
        try:
            # 获取当前用户信息
            user_info = self.get_current_user()
            if not user_info:
                self.logger.error("无法获取当前用户信息")
                return
            
            # 通过API获取教师的课程列表
            response = self.api_client.get('/api/courses/teacher')
            if response and response.get('success'):
                self.courses = response.get('courses', [])
                self.logger.info(f"加载了 {len(self.courses)} 个课程")
                self.data_loaded.emit()
            else:
                self.logger.error("加载课程列表失败")
                
        except Exception as e:
            self.logger.error(f"加载课程列表时出错: {str(e)}")
    
    def set_current_course(self, course_id: str):
        """设置当前课程"""
        if course_id != self.current_course_id:
            self.current_course_id = course_id
            self.load_course_data(course_id)
    
    def load_course_data(self, course_id: str):
        """加载课程数据（学生和小组）"""
        try:
            # 获取课程学生列表
            response = self.api_client.get(f'/api/courses/{course_id}/students')
            if response and response.get('success'):
                students_data = response.get('students', [])
                self.students.clear()
                
                for student_data in students_data:
                    student = Student(
                        student_data['id'],
                        student_data['name'],
                        student_data['username'],
                        student_data.get('group_id')
                    )
                    self.students[student.student_id] = student
                
                self.logger.info(f"加载了 {len(self.students)} 个学生")
            
            # 获取课程小组列表
            response = self.api_client.get(f'/api/courses/{course_id}/groups')
            if response and response.get('success'):
                groups_data = response.get('groups', [])
                self.groups.clear()
                
                for group_data in groups_data:
                    # 获取小组成员
                    members = []
                    for member_id in group_data.get('member_ids', []):
                        if member_id in self.students:
                            members.append(self.students[member_id])
                    
                    group = Group(
                        group_data['id'],
                        group_data['name'],
                        members
                    )
                    self.groups[group.group_id] = group
                
                self.logger.info(f"加载了 {len(self.groups)} 个小组")
            
            # 更新点名统计
            self.update_pick_statistics()
            
        except Exception as e:
            self.logger.error(f"加载课程数据时出错: {str(e)}")
    
    def pick_random_student(self) -> Optional[Student]:
        """随机选择学生"""
        if not self.students:
            return None
        
        # 获取可选择的学生列表
        available_students = self.get_available_students()
        if not available_students:
            available_students = list(self.students.values())
        
        # 根据配置进行加权选择
        if self.get_config("weight_by_frequency", True):
            selected_student = self.weighted_random_choice(available_students)
        else:
            selected_student = random.choice(available_students)
        
        # 记录点名
        self.record_pick("student", selected_student.student_id, selected_student.name)
        
        # 更新学生统计
        selected_student.pick_count += 1
        selected_student.last_picked = datetime.now()
        
        # 发送信号
        self.student_picked.emit(selected_student)
        
        self.logger.info(f"随机选择学生: {selected_student.name}")
        return selected_student
    
    def pick_random_group(self) -> Optional[Group]:
        """随机选择小组"""
        if not self.groups:
            return None
        
        # 获取可选择的小组列表
        available_groups = self.get_available_groups()
        if not available_groups:
            available_groups = list(self.groups.values())
        
        # 根据配置进行加权选择
        if self.get_config("weight_by_frequency", True):
            selected_group = self.weighted_random_choice(available_groups)
        else:
            selected_group = random.choice(available_groups)
        
        # 记录点名
        self.record_pick("group", selected_group.group_id, selected_group.name)
        
        # 更新小组统计
        selected_group.pick_count += 1
        selected_group.last_picked = datetime.now()
        
        # 发送信号
        self.group_picked.emit(selected_group)
        
        self.logger.info(f"随机选择小组: {selected_group.name}")
        return selected_group

    def get_available_students(self) -> List[Student]:
        """获取可选择的学生列表"""
        if not self.get_config("exclude_recent_picks", True):
            return list(self.students.values())

        recent_hours = self.get_config("recent_pick_hours", 1)
        cutoff_time = datetime.now() - timedelta(hours=recent_hours)

        available = []
        for student in self.students.values():
            if not student.last_picked or student.last_picked < cutoff_time:
                available.append(student)

        return available

    def get_available_groups(self) -> List[Group]:
        """获取可选择的小组列表"""
        if not self.get_config("exclude_recent_picks", True):
            return list(self.groups.values())

        recent_hours = self.get_config("recent_pick_hours", 1)
        cutoff_time = datetime.now() - timedelta(hours=recent_hours)

        available = []
        for group in self.groups.values():
            if not group.last_picked or group.last_picked < cutoff_time:
                available.append(group)

        return available

    def weighted_random_choice(self, items: List) -> Any:
        """根据点名频率进行加权随机选择"""
        if not items:
            return None

        # 计算权重（点名次数越少，权重越高）
        max_count = max(item.pick_count for item in items) if items else 0
        weights = []

        for item in items:
            # 权重 = (最大点名次数 - 当前点名次数 + 1)
            weight = max_count - item.pick_count + 1
            weights.append(weight)

        # 加权随机选择
        return random.choices(items, weights=weights)[0]

    def record_pick(self, pick_type: str, target_id: str, target_name: str):
        """记录点名"""
        if not self.current_course_id:
            return

        record = PickRecord(pick_type, target_id, target_name, self.current_course_id)
        self.pick_records.append(record)

        # 限制记录数量
        max_records = self.get_config("max_records", 1000)
        if len(self.pick_records) > max_records:
            self.pick_records = self.pick_records[-max_records:]

        # 自动保存
        if self.get_config("auto_save", True):
            self.save_pick_records()

    def update_pick_statistics(self):
        """更新点名统计"""
        if not self.current_course_id:
            return

        # 统计当前课程的点名记录
        course_records = [r for r in self.pick_records if r.course_id == self.current_course_id]

        # 更新学生统计
        for student in self.students.values():
            student_records = [r for r in course_records
                             if r.pick_type == "student" and r.target_id == student.student_id]
            student.pick_count = len(student_records)
            if student_records:
                student.last_picked = max(r.timestamp for r in student_records)

        # 更新小组统计
        for group in self.groups.values():
            group_records = [r for r in course_records
                           if r.pick_type == "group" and r.target_id == group.group_id]
            group.pick_count = len(group_records)
            if group_records:
                group.last_picked = max(r.timestamp for r in group_records)

    def get_pick_statistics(self) -> Dict[str, Any]:
        """获取点名统计信息"""
        if not self.current_course_id:
            return {}

        course_records = [r for r in self.pick_records if r.course_id == self.current_course_id]

        stats = {
            "total_picks": len(course_records),
            "student_picks": len([r for r in course_records if r.pick_type == "student"]),
            "group_picks": len([r for r in course_records if r.pick_type == "group"]),
            "students_picked": len(set(r.target_id for r in course_records if r.pick_type == "student")),
            "groups_picked": len(set(r.target_id for r in course_records if r.pick_type == "group")),
            "total_students": len(self.students),
            "total_groups": len(self.groups)
        }

        # 计算覆盖率
        if stats["total_students"] > 0:
            stats["student_coverage"] = stats["students_picked"] / stats["total_students"]
        else:
            stats["student_coverage"] = 0

        if stats["total_groups"] > 0:
            stats["group_coverage"] = stats["groups_picked"] / stats["total_groups"]
        else:
            stats["group_coverage"] = 0

        return stats

    def save_pick_records(self):
        """保存点名记录"""
        try:
            records_data = [record.to_dict() for record in self.pick_records]

            # 保存到本地文件
            import os
            records_file = os.path.join(os.path.dirname(__file__), "..", "database", "pick_records.json")
            os.makedirs(os.path.dirname(records_file), exist_ok=True)

            with open(records_file, 'w', encoding='utf-8') as f:
                json.dump(records_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"保存了 {len(self.pick_records)} 条点名记录")

        except Exception as e:
            self.logger.error(f"保存点名记录失败: {str(e)}")

    def load_pick_records(self):
        """加载点名记录"""
        try:
            import os
            records_file = os.path.join(os.path.dirname(__file__), "..", "database", "pick_records.json")

            if os.path.exists(records_file):
                with open(records_file, 'r', encoding='utf-8') as f:
                    records_data = json.load(f)

                self.pick_records.clear()
                for record_data in records_data:
                    record = PickRecord(
                        record_data["pick_type"],
                        record_data["target_id"],
                        record_data["target_name"],
                        record_data["course_id"],
                        datetime.fromisoformat(record_data["timestamp"])
                    )
                    self.pick_records.append(record)

                self.logger.info(f"加载了 {len(self.pick_records)} 条点名记录")

        except Exception as e:
            self.logger.error(f"加载点名记录失败: {str(e)}")

    def clear_pick_records(self, course_id: Optional[str] = None):
        """清空点名记录"""
        if course_id:
            # 只清空指定课程的记录
            self.pick_records = [r for r in self.pick_records if r.course_id != course_id]
        else:
            # 清空所有记录
            self.pick_records.clear()

        # 重新更新统计
        self.update_pick_statistics()

        # 保存
        if self.get_config("auto_save", True):
            self.save_pick_records()

class RandomPickerWindow(QWidget):
    """随机点名窗口"""

    def __init__(self, module: RandomPickerModule):
        super().__init__()
        self.module = module
        self.init_ui()

        # 连接模块信号
        self.module.student_picked.connect(self.on_student_picked)
        self.module.group_picked.connect(self.on_group_picked)
        self.module.data_loaded.connect(self.on_data_loaded)

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("随机点名 - 智慧课堂教学工具")
        self.setGeometry(100, 100, 1200, 800)

        # 主布局
        main_layout = QVBoxLayout()

        # 标题栏
        title_layout = QHBoxLayout()
        title_label = QLabel("随机点名")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        main_layout.addLayout(title_layout)

        # 课程选择区域
        course_group = QGroupBox("课程选择")
        course_layout = QHBoxLayout()

        course_layout.addWidget(QLabel("选择课程:"))
        self.course_combo = QComboBox()
        self.course_combo.currentTextChanged.connect(self.on_course_changed)
        course_layout.addWidget(self.course_combo)

        self.refresh_btn = QPushButton("刷新课程")
        self.refresh_btn.clicked.connect(self.refresh_courses)
        course_layout.addWidget(self.refresh_btn)

        course_layout.addStretch()
        course_group.setLayout(course_layout)
        main_layout.addWidget(course_group)

        # 主要内容区域
        content_layout = QHBoxLayout()

        # 左侧：点名控制区域
        left_panel = self.create_control_panel()
        content_layout.addWidget(left_panel, 1)

        # 右侧：统计和历史区域
        right_panel = self.create_stats_panel()
        content_layout.addWidget(right_panel, 1)

        main_layout.addLayout(content_layout)

        self.setLayout(main_layout)

        # 设置样式
        self.setStyleSheet("""
            QWidget {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #c0c0c0;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QComboBox {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 3px;
            }
        """)

    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QWidget()
        layout = QVBoxLayout()

        # 点名按钮区域
        pick_group = QGroupBox("随机点名")
        pick_layout = QVBoxLayout()

        # 学生点名
        student_layout = QHBoxLayout()
        self.pick_student_btn = QPushButton("随机选择学生")
        self.pick_student_btn.setFont(QFont("Arial", 12, QFont.Bold))
        self.pick_student_btn.setMinimumHeight(50)
        self.pick_student_btn.clicked.connect(self.pick_student)
        self.pick_student_btn.setStyleSheet("QPushButton { background-color: #28a745; }")
        student_layout.addWidget(self.pick_student_btn)
        pick_layout.addLayout(student_layout)

        # 小组点名
        group_layout = QHBoxLayout()
        self.pick_group_btn = QPushButton("随机选择小组")
        self.pick_group_btn.setFont(QFont("Arial", 12, QFont.Bold))
        self.pick_group_btn.setMinimumHeight(50)
        self.pick_group_btn.clicked.connect(self.pick_group)
        self.pick_group_btn.setStyleSheet("QPushButton { background-color: #fd7e14; }")
        group_layout.addWidget(self.pick_group_btn)
        pick_layout.addLayout(group_layout)

        pick_group.setLayout(pick_layout)
        layout.addWidget(pick_group)

        # 结果显示区域
        result_group = QGroupBox("点名结果")
        result_layout = QVBoxLayout()

        self.result_label = QLabel("点击按钮开始随机点名")
        self.result_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setStyleSheet("""
            QLabel {
                background-color: white;
                border: 2px solid #007acc;
                border-radius: 8px;
                padding: 20px;
                color: #007acc;
            }
        """)
        self.result_label.setMinimumHeight(100)
        result_layout.addWidget(self.result_label)

        result_group.setLayout(result_layout)
        layout.addWidget(result_group)

        # 设置选项
        settings_group = QGroupBox("设置选项")
        settings_layout = QVBoxLayout()

        # 排除最近点名选项
        self.exclude_recent_cb = QCheckBox("排除最近被点名的")
        self.exclude_recent_cb.setChecked(True)
        self.exclude_recent_cb.toggled.connect(self.update_settings)
        settings_layout.addWidget(self.exclude_recent_cb)

        # 最近点名时间范围
        recent_layout = QHBoxLayout()
        recent_layout.addWidget(QLabel("最近点名时间范围:"))
        self.recent_hours_spin = QSpinBox()
        self.recent_hours_spin.setRange(1, 24)
        self.recent_hours_spin.setValue(1)
        self.recent_hours_spin.setSuffix(" 小时")
        self.recent_hours_spin.valueChanged.connect(self.update_settings)
        recent_layout.addWidget(self.recent_hours_spin)
        recent_layout.addStretch()
        settings_layout.addLayout(recent_layout)

        # 频率加权选项
        self.weight_frequency_cb = QCheckBox("根据点名频率加权")
        self.weight_frequency_cb.setChecked(True)
        self.weight_frequency_cb.toggled.connect(self.update_settings)
        settings_layout.addWidget(self.weight_frequency_cb)

        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)

        # 操作按钮
        action_layout = QHBoxLayout()

        self.clear_records_btn = QPushButton("清空记录")
        self.clear_records_btn.clicked.connect(self.clear_records)
        self.clear_records_btn.setStyleSheet("QPushButton { background-color: #dc3545; }")
        action_layout.addWidget(self.clear_records_btn)

        self.export_btn = QPushButton("导出记录")
        self.export_btn.clicked.connect(self.export_records)
        action_layout.addWidget(self.export_btn)

        layout.addLayout(action_layout)

        layout.addStretch()
        panel.setLayout(layout)
        return panel

    def create_stats_panel(self) -> QWidget:
        """创建统计面板"""
        panel = QWidget()
        layout = QVBoxLayout()

        # 统计信息
        stats_group = QGroupBox("统计信息")
        stats_layout = QGridLayout()

        # 统计标签
        self.total_picks_label = QLabel("总点名次数: 0")
        stats_layout.addWidget(self.total_picks_label, 0, 0)

        self.student_picks_label = QLabel("学生点名: 0")
        stats_layout.addWidget(self.student_picks_label, 0, 1)

        self.group_picks_label = QLabel("小组点名: 0")
        stats_layout.addWidget(self.group_picks_label, 1, 0)

        self.coverage_label = QLabel("覆盖率: 0%")
        stats_layout.addWidget(self.coverage_label, 1, 1)

        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)

        # 学生列表
        students_group = QGroupBox("学生列表")
        students_layout = QVBoxLayout()

        self.students_table = QTableWidget()
        self.students_table.setColumnCount(4)
        self.students_table.setHorizontalHeaderLabels(["姓名", "用户名", "点名次数", "最后点名"])

        header = self.students_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeToContents)

        students_layout.addWidget(self.students_table)
        students_group.setLayout(students_layout)
        layout.addWidget(students_group)

        # 点名历史
        history_group = QGroupBox("点名历史")
        history_layout = QVBoxLayout()

        self.history_list = QListWidget()
        history_layout.addWidget(self.history_list)

        history_group.setLayout(history_layout)
        layout.addWidget(history_group)

        panel.setLayout(layout)
        return panel

    def refresh_courses(self):
        """刷新课程列表"""
        self.module.load_courses()

    def on_data_loaded(self):
        """数据加载完成处理"""
        # 更新课程下拉框
        self.course_combo.clear()
        self.course_combo.addItem("请选择课程", None)

        for course in self.module.courses:
            self.course_combo.addItem(course['name'], course['id'])

        # 如果有当前课程，选中它
        if self.module.current_course_id:
            for i in range(self.course_combo.count()):
                if self.course_combo.itemData(i) == self.module.current_course_id:
                    self.course_combo.setCurrentIndex(i)
                    break

    def on_course_changed(self):
        """课程选择改变处理"""
        course_id = self.course_combo.currentData()
        if course_id:
            self.module.set_current_course(course_id)
            self.update_display()
            self.enable_controls(True)
        else:
            self.enable_controls(False)

    def enable_controls(self, enabled: bool):
        """启用/禁用控件"""
        self.pick_student_btn.setEnabled(enabled)
        self.pick_group_btn.setEnabled(enabled)
        self.clear_records_btn.setEnabled(enabled)
        self.export_btn.setEnabled(enabled)

    def pick_student(self):
        """随机选择学生"""
        if not self.module.current_course_id:
            QMessageBox.warning(self, "警告", "请先选择课程")
            return

        if not self.module.students:
            QMessageBox.warning(self, "警告", "当前课程没有学生")
            return

        # 执行随机选择
        selected_student = self.module.pick_random_student()
        if selected_student:
            self.show_pick_result(f"学生: {selected_student.name}", "#28a745")
        else:
            QMessageBox.information(self, "提示", "没有可选择的学生")

    def pick_group(self):
        """随机选择小组"""
        if not self.module.current_course_id:
            QMessageBox.warning(self, "警告", "请先选择课程")
            return

        if not self.module.groups:
            QMessageBox.warning(self, "警告", "当前课程没有小组")
            return

        # 执行随机选择
        selected_group = self.module.pick_random_group()
        if selected_group:
            members_text = ", ".join([member.name for member in selected_group.members])
            result_text = f"小组: {selected_group.name}\n成员: {members_text}"
            self.show_pick_result(result_text, "#fd7e14")
        else:
            QMessageBox.information(self, "提示", "没有可选择的小组")

    def show_pick_result(self, text: str, color: str):
        """显示点名结果"""
        self.result_label.setText(text)
        self.result_label.setStyleSheet(f"""
            QLabel {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 8px;
                padding: 20px;
                color: {color};
            }}
        """)

        # 更新显示
        self.update_display()

    def update_settings(self):
        """更新设置"""
        self.module.config["exclude_recent_picks"] = self.exclude_recent_cb.isChecked()
        self.module.config["recent_pick_hours"] = self.recent_hours_spin.value()
        self.module.config["weight_by_frequency"] = self.weight_frequency_cb.isChecked()

    def clear_records(self):
        """清空记录"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空当前课程的点名记录吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.module.clear_pick_records(self.module.current_course_id)
            self.update_display()
            QMessageBox.information(self, "提示", "记录已清空")

    def export_records(self):
        """导出记录"""
        if not self.module.current_course_id:
            QMessageBox.warning(self, "警告", "请先选择课程")
            return

        # 这里可以实现导出功能
        QMessageBox.information(self, "提示", "导出功能正在开发中...")

    def update_display(self):
        """更新显示"""
        if not self.module.current_course_id:
            return

        # 更新统计信息
        stats = self.module.get_pick_statistics()
        self.total_picks_label.setText(f"总点名次数: {stats.get('total_picks', 0)}")
        self.student_picks_label.setText(f"学生点名: {stats.get('student_picks', 0)}")
        self.group_picks_label.setText(f"小组点名: {stats.get('group_picks', 0)}")

        student_coverage = stats.get('student_coverage', 0) * 100
        self.coverage_label.setText(f"学生覆盖率: {student_coverage:.1f}%")

        # 更新学生表格
        self.update_students_table()

        # 更新历史记录
        self.update_history_list()

    def update_students_table(self):
        """更新学生表格"""
        students = list(self.module.students.values())
        self.students_table.setRowCount(len(students))

        for row, student in enumerate(students):
            self.students_table.setItem(row, 0, QTableWidgetItem(student.name))
            self.students_table.setItem(row, 1, QTableWidgetItem(student.username))
            self.students_table.setItem(row, 2, QTableWidgetItem(str(student.pick_count)))

            last_picked = "从未" if not student.last_picked else student.last_picked.strftime("%m-%d %H:%M")
            self.students_table.setItem(row, 3, QTableWidgetItem(last_picked))

    def update_history_list(self):
        """更新历史记录列表"""
        self.history_list.clear()

        # 获取当前课程的记录
        course_records = [r for r in self.module.pick_records
                         if r.course_id == self.module.current_course_id]

        # 按时间倒序排列
        course_records.sort(key=lambda x: x.timestamp, reverse=True)

        # 只显示最近的50条记录
        for record in course_records[:50]:
            time_str = record.timestamp.strftime("%m-%d %H:%M")
            type_str = "学生" if record.pick_type == "student" else "小组"
            text = f"[{time_str}] {type_str}: {record.target_name}"
            self.history_list.addItem(text)

    def on_student_picked(self, student: Student):
        """学生被选中事件处理"""
        self.update_display()

    def on_group_picked(self, group: Group):
        """小组被选中事件处理"""
        self.update_display()
