import sys
from PyQt5.QtWidgets import (
    QA<PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
    QComboBox, QTableWidget, QTableWidgetItem, QHeaderView, QLabel,
    QDialog, QLineEdit, QFormLayout, QDialogButtonBox, QMessageBox
)
from PyQt5.QtCore import Qt
from api_client import ApiClient

class AttendanceWindow(QWidget):
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.setWindowTitle('课堂点名')
        self.setGeometry(200, 200, 500, 400)
        self.courses = []
        self.students = []
        self.init_ui()
        self.load_courses()

    def init_ui(self):
        layout = QVBoxLayout()

        # 课程选择
        course_layout = QHBoxLayout()
        course_layout.addWidget(QLabel("选择课程:"))
        self.course_combo = QComboBox()
        self.course_combo.currentIndexChanged.connect(self.load_students_for_course)
        course_layout.addWidget(self.course_combo)
        layout.addLayout(course_layout)

        # 学生列表
        self.student_table = QTableWidget()
        self.student_table.setColumnCount(3)
        self.student_table.setHorizontalHeaderLabels(['学号', '姓名', '状态'])
        self.student_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.student_table)

        # 提交按钮
        self.submit_button = QPushButton('提交点名结果')
        self.submit_button.clicked.connect(self.submit_attendance)
        layout.addWidget(self.submit_button)

        self.setLayout(layout)

    def load_courses(self):
        """加载教师的课程列表"""
        try:
            response = self.api_client.get('/course/')
            if response:
                self.courses = response.get('courses', [])
                self.course_combo.clear()
                for course in self.courses:
                    self.course_combo.addItem(course['name'], userData=course['id'])
            else:
                QMessageBox.warning(self, "加载失败", "未能加载课程或课程列表为空")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载课程失败: {e}")

    def load_students_for_course(self, index):
        """加载并显示所选课程的学生"""
        course_id = self.course_combo.itemData(index)
        if not course_id:
            return

        try:
            response = self.api_client.get(f'/group/api/groups/{course_id}')
            self.students = []
            if response and 'ungrouped_students' in response:
                self.students.extend(response['ungrouped_students'])
            if response and 'groups' in response:
                for group in response['groups']:
                    self.students.extend(group['members'])
            
            self.populate_student_table()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载学生列表失败: {e}")

    def populate_student_table(self):
        """填充学生信息到表格中"""
        self.student_table.setRowCount(len(self.students))
        for i, student in enumerate(self.students):
            self.student_table.setItem(i, 0, QTableWidgetItem(str(student.get('id', ''))))
            self.student_table.setItem(i, 1, QTableWidgetItem(student.get('name', '')))
            
            status_combo = QComboBox()
            status_combo.addItems(['到课', '缺勤', '迟到', '早退'])
            self.student_table.setCellWidget(i, 2, status_combo)

    def submit_attendance(self):
        """提交考勤数据"""
        course_id = self.course_combo.currentData()
        if not course_id:
            QMessageBox.warning(self, "提示", "请先选择课程")
            return

        attendance_data = {'attendance': []}
        for i in range(self.student_table.rowCount()):
            student_id = self.student_table.item(i, 0).text()
            status_widget = self.student_table.cellWidget(i, 2)
            status_text = status_widget.currentText()
            
            status_map = {
                '到课': 'present',
                '缺勤': 'absent',
                '迟到': 'late',
                '早退': 'left_early'
            }
            
            attendance_data['attendance'].append({
                'student_id': int(student_id),
                'status': status_map.get(status_text, 'absent')
            })

        try:
            response = self.api_client.post(f'/group/api/attendance/{course_id}', json=attendance_data)
            if response:
                QMessageBox.information(self, "成功", "点名结果提交成功！")
            else:
                QMessageBox.warning(self, "失败", "点名结果提交失败。")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"提交点名结果时出错: {e}")

class LoginDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("登录")
        layout = QFormLayout(self)

        self.email_input = QLineEdit(self)
        self.password_input = QLineEdit(self)
        self.password_input.setEchoMode(QLineEdit.Password)

        layout.addRow("邮箱:", self.email_input)
        layout.addRow("密码:", self.password_input)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, Qt.Horizontal, self)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def getCredentials(self):
        return self.email_input.text(), self.password_input.text()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    login_dialog = LoginDialog()
    if login_dialog.exec() == QDialog.Accepted:
        email, password = login_dialog.getCredentials()
        # 创建一个ApiClient实例用于登录
        api_client_instance = ApiClient(base_url="https://192.168.0.11:5000")
        logged_in, user_info = api_client_instance.login(email, password)
        
        if logged_in:
            # 将登录成功的api_client实例传递给窗口
            window = AttendanceWindow(api_client=api_client_instance)
            window.show()
            sys.exit(app.exec_())
        else:
            QMessageBox.critical(None, "登录失败", "无法连接到服务器或凭据无效。")
            sys.exit(1)
    else:
        sys.exit(0)
