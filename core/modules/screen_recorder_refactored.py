"""
重构后的录屏模块 - 基于新的模块化架构
支持多种录制格式和质量设置
"""
import os
import sys
import subprocess
import time
import platform
import json
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
from .base_module import CommonModule

class ScreenRecorderModule(CommonModule):
    """重构后的录屏模块"""
    
    def __init__(self):
        super().__init__(
            module_name="screen_recorder",
            display_name="屏幕录制",
            version="2.0.0"
        )
        
        # 录制相关属性
        self.recorder_window = None
        self.recording_process = None
        self.is_recording = False
        self.is_paused = False
        self.start_time = None
        self.pause_time = 0
        self.elapsed_time = 0
        self.output_file = None
        
        # 录制设置
        self.output_directory = "record"
        self.video_format = "mp4"  # mp4, flv, avi, mkv
        self.video_quality = "high"  # low, medium, high, ultra
        self.frame_rate = 30
        self.audio_enabled = True
        self.cursor_enabled = True
        
        # 质量预设
        self.quality_presets = {
            "low": {"bitrate": "1M", "preset": "ultrafast", "crf": "28"},
            "medium": {"bitrate": "2M", "preset": "fast", "crf": "23"},
            "high": {"bitrate": "4M", "preset": "medium", "crf": "20"},
            "ultra": {"bitrate": "8M", "preset": "slow", "crf": "18"}
        }
        
        # 录制历史
        self.recording_history = []
        
        # 定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_timer)
        
        # 进度回调
        self.progress_callbacks = []
    
    def _initialize_module(self) -> bool:
        """初始化录屏模块"""
        try:
            # 创建输出目录
            if not os.path.exists(self.output_directory):
                os.makedirs(self.output_directory)
            
            # 检查FFmpeg是否可用
            if not self._check_ffmpeg():
                self.logger.warning("FFmpeg不可用，某些功能可能受限")
            
            # 创建录屏窗口
            self.recorder_window = ScreenRecorderWindow(self)
            
            # 设置模块配置
            self.config.update({
                "output_directory": "record",
                "default_format": "mp4",
                "default_quality": "high",
                "default_framerate": 30,
                "enable_audio": True,
                "enable_cursor": True,
                "auto_save_settings": True,
                "max_recording_time": 3600  # 1小时
            })
            
            # 加载录制历史
            self._load_recording_history()
            
            # 加载用户设置
            self._load_user_settings()
            
            self.logger.info("录屏模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"录屏模块初始化失败: {str(e)}")
            return False
    
    def _cleanup_module(self):
        """清理录屏模块资源"""
        try:
            # 停止录制
            if self.is_recording:
                self.stop_recording()
            
            # 停止定时器
            if self.timer.isActive():
                self.timer.stop()
            
            # 保存录制历史
            self._save_recording_history()
            
            # 保存用户设置
            self._save_user_settings()
            
            # 关闭录屏窗口
            if self.recorder_window:
                self.recorder_window.close()
                self.recorder_window = None
            
            self.logger.info("录屏模块资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理录屏模块资源时出错: {str(e)}")
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "start_recording":
            self.start_recording(data.get("settings", {}))
        elif message_type == "stop_recording":
            self.stop_recording()
        elif message_type == "pause_recording":
            self.pause_recording()
        elif message_type == "resume_recording":
            self.resume_recording()
        elif message_type == "get_recording_status":
            self._send_recording_status(sender)
    
    def show(self):
        """显示录屏窗口"""
        if self.recorder_window:
            self.recorder_window.show()
            self.recorder_window.raise_()
            self.recorder_window.activateWindow()
    
    def hide(self):
        """隐藏录屏窗口"""
        if self.recorder_window:
            self.recorder_window.hide()
    
    def close(self):
        """关闭录屏模块"""
        self.cleanup()
    
    def _check_ffmpeg(self) -> bool:
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(
                ["ffmpeg", "-version"], 
                capture_output=True, 
                text=True, 
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False
    
    def start_recording(self, settings: Dict[str, Any] = None) -> bool:
        """开始录制"""
        if self.is_recording:
            self.logger.warning("录制已在进行中")
            return False
        
        try:
            # 应用设置
            if settings:
                self._apply_settings(settings)
            
            # 生成输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screen_record_{timestamp}.{self.video_format}"
            self.output_file = os.path.join(self.output_directory, filename)
            
            # 构建FFmpeg命令
            command = self._build_ffmpeg_command()
            
            if not command:
                self.logger.error("无法构建FFmpeg命令")
                return False
            
            # 启动录制进程
            kwargs = {}
            if platform.system() == "Windows":
                kwargs["creationflags"] = subprocess.CREATE_NO_WINDOW
            
            self.recording_process = subprocess.Popen(
                command,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                **kwargs
            )
            
            # 更新状态
            self.is_recording = True
            self.is_paused = False
            self.start_time = time.time()
            self.pause_time = 0
            self.elapsed_time = 0
            
            # 启动定时器
            self.timer.start(1000)
            
            # 添加到历史记录
            self._add_to_history("started")
            
            # 通知其他模块
            self.broadcast_message("recording_started", {
                "output_file": self.output_file,
                "settings": self._get_current_settings(),
                "timestamp": datetime.now().isoformat()
            })
            
            self.logger.info(f"录制已开始: {self.output_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动录制失败: {str(e)}")
            return False
    
    def stop_recording(self) -> bool:
        """停止录制"""
        if not self.is_recording:
            self.logger.warning("当前没有进行录制")
            return False
        
        try:
            # 停止录制进程
            if self.recording_process:
                self.recording_process.terminate()
                self.recording_process.wait(timeout=5)
                self.recording_process = None
            
            # 更新状态
            self.is_recording = False
            self.is_paused = False
            
            # 停止定时器
            self.timer.stop()
            
            # 计算最终录制时间
            if self.start_time:
                self.elapsed_time = int(time.time() - self.start_time - self.pause_time)
            
            # 添加到历史记录
            self._add_to_history("completed")
            
            # 通知其他模块
            self.broadcast_message("recording_stopped", {
                "output_file": self.output_file,
                "duration": self.elapsed_time,
                "timestamp": datetime.now().isoformat()
            })
            
            self.logger.info(f"录制已停止: {self.output_file}, 时长: {self.elapsed_time}秒")
            return True
            
        except Exception as e:
            self.logger.error(f"停止录制失败: {str(e)}")
            return False
    
    def pause_recording(self) -> bool:
        """暂停录制"""
        if not self.is_recording or self.is_paused:
            return False
        
        try:
            # FFmpeg暂停功能有限，这里记录暂停时间
            self.is_paused = True
            self.pause_start_time = time.time()
            
            # 通知其他模块
            self.broadcast_message("recording_paused", {
                "timestamp": datetime.now().isoformat()
            })
            
            self.logger.info("录制已暂停")
            return True
            
        except Exception as e:
            self.logger.error(f"暂停录制失败: {str(e)}")
            return False
    
    def resume_recording(self) -> bool:
        """恢复录制"""
        if not self.is_recording or not self.is_paused:
            return False
        
        try:
            # 计算暂停时间
            if hasattr(self, 'pause_start_time'):
                self.pause_time += time.time() - self.pause_start_time
                delattr(self, 'pause_start_time')
            
            self.is_paused = False
            
            # 通知其他模块
            self.broadcast_message("recording_resumed", {
                "timestamp": datetime.now().isoformat()
            })
            
            self.logger.info("录制已恢复")
            return True
            
        except Exception as e:
            self.logger.error(f"恢复录制失败: {str(e)}")
            return False
    
    def _build_ffmpeg_command(self) -> List[str]:
        """构建FFmpeg命令"""
        try:
            command = ["ffmpeg"]
            
            # 输入设置
            if platform.system() == "Windows":
                command.extend(["-f", "gdigrab", "-i", "desktop"])
            else:  # Linux
                command.extend(["-f", "x11grab", "-i", ":0.0"])
            
            # 帧率设置
            command.extend(["-framerate", str(self.frame_rate)])
            
            # 视频编码设置
            quality = self.quality_presets.get(self.video_quality, self.quality_presets["high"])
            command.extend([
                "-c:v", "libx264",
                "-preset", quality["preset"],
                "-crf", quality["crf"],
                "-b:v", quality["bitrate"]
            ])
            
            # 像素格式
            command.extend(["-pix_fmt", "yuv420p"])
            
            # 音频设置
            if self.audio_enabled:
                if platform.system() == "Windows":
                    command.extend(["-f", "dshow", "-i", "audio=virtual-audio-capturer"])
                else:
                    command.extend(["-f", "pulse", "-i", "default"])
                command.extend(["-c:a", "aac", "-b:a", "128k"])
            else:
                command.extend(["-an"])  # 禁用音频
            
            # 鼠标光标
            if self.cursor_enabled and platform.system() == "Windows":
                command.extend(["-draw_mouse", "1"])
            
            # 输出文件
            command.append(self.output_file)
            
            return command
            
        except Exception as e:
            self.logger.error(f"构建FFmpeg命令失败: {str(e)}")
            return []
    
    def _apply_settings(self, settings: Dict[str, Any]):
        """应用录制设置"""
        if "format" in settings:
            self.video_format = settings["format"]
        if "quality" in settings:
            self.video_quality = settings["quality"]
        if "framerate" in settings:
            self.frame_rate = settings["framerate"]
        if "audio" in settings:
            self.audio_enabled = settings["audio"]
        if "cursor" in settings:
            self.cursor_enabled = settings["cursor"]
        if "output_dir" in settings:
            self.output_directory = settings["output_dir"]
    
    def _get_current_settings(self) -> Dict[str, Any]:
        """获取当前录制设置"""
        return {
            "format": self.video_format,
            "quality": self.video_quality,
            "framerate": self.frame_rate,
            "audio": self.audio_enabled,
            "cursor": self.cursor_enabled,
            "output_dir": self.output_directory
        }
    
    def _update_timer(self):
        """更新计时器"""
        if self.is_recording and not self.is_paused and self.start_time:
            self.elapsed_time = int(time.time() - self.start_time - self.pause_time)
            
            # 通知窗口更新时间显示
            if self.recorder_window:
                self.recorder_window.update_time_display(self.elapsed_time)
            
            # 检查最大录制时间
            max_time = self.config.get("max_recording_time", 3600)
            if self.elapsed_time >= max_time:
                self.logger.warning(f"达到最大录制时间 {max_time} 秒，自动停止录制")
                self.stop_recording()
    
    def _add_to_history(self, status: str):
        """添加到录制历史"""
        record = {
            "file_path": self.output_file,
            "file_name": os.path.basename(self.output_file) if self.output_file else "",
            "status": status,
            "start_time": datetime.fromtimestamp(self.start_time).isoformat() if self.start_time else "",
            "duration": self.elapsed_time,
            "settings": self._get_current_settings(),
            "timestamp": datetime.now().isoformat()
        }
        
        self.recording_history.append(record)
        
        # 限制历史记录数量
        if len(self.recording_history) > 100:
            self.recording_history = self.recording_history[-100:]
    
    def get_recording_history(self) -> List[Dict[str, Any]]:
        """获取录制历史"""
        return self.recording_history.copy()
    
    def get_recording_status(self) -> Dict[str, Any]:
        """获取录制状态"""
        return {
            "is_recording": self.is_recording,
            "is_paused": self.is_paused,
            "elapsed_time": self.elapsed_time,
            "output_file": self.output_file,
            "settings": self._get_current_settings()
        }
    
    def _send_recording_status(self, requester: str):
        """发送录制状态给请求者"""
        status = self.get_recording_status()
        self.send_message(requester, "recording_status", status)
    
    def _load_recording_history(self):
        """加载录制历史"""
        try:
            history_file = os.path.join(self.output_directory, "recording_history.json")
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    self.recording_history = json.load(f)
        except Exception as e:
            self.logger.error(f"加载录制历史失败: {str(e)}")
            self.recording_history = []
    
    def _save_recording_history(self):
        """保存录制历史"""
        try:
            history_file = os.path.join(self.output_directory, "recording_history.json")
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(self.recording_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存录制历史失败: {str(e)}")
    
    def _load_user_settings(self):
        """加载用户设置"""
        try:
            settings_file = os.path.join(self.output_directory, "recorder_settings.json")
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self._apply_settings(settings)
        except Exception as e:
            self.logger.error(f"加载用户设置失败: {str(e)}")
    
    def _save_user_settings(self):
        """保存用户设置"""
        try:
            if self.config.get("auto_save_settings", True):
                settings_file = os.path.join(self.output_directory, "recorder_settings.json")
                settings = self._get_current_settings()
                with open(settings_file, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存用户设置失败: {str(e)}")

class ScreenRecorderWindow(QMainWindow):
    """录屏窗口"""

    def __init__(self, module: ScreenRecorderModule):
        super().__init__()
        self.module = module

        # 窗口设置
        self.setWindowTitle("屏幕录制工具")
        self.setGeometry(100, 100, 500, 400)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)

        # 创建UI
        self.setup_ui()

        # 连接信号
        self.setup_signals()

    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 录制状态区域
        status_group = QGroupBox("录制状态")
        status_layout = QVBoxLayout(status_group)

        # 时间显示
        self.time_label = QLabel("00:00:00")
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2E86AB;
                background-color: #F5F5F5;
                border: 2px solid #2E86AB;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        status_layout.addWidget(self.time_label)

        # 状态指示器
        self.status_label = QLabel("准备就绪")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("font-size: 14px; color: #666;")
        status_layout.addWidget(self.status_label)

        main_layout.addWidget(status_group)

        # 录制控制区域
        control_group = QGroupBox("录制控制")
        control_layout = QHBoxLayout(control_group)

        # 开始/暂停按钮
        self.record_btn = QPushButton("开始录制")
        self.record_btn.setStyleSheet(self._get_button_style("#4CAF50"))
        self.record_btn.setMinimumHeight(40)
        control_layout.addWidget(self.record_btn)

        # 暂停按钮
        self.pause_btn = QPushButton("暂停")
        self.pause_btn.setStyleSheet(self._get_button_style("#FF9800"))
        self.pause_btn.setMinimumHeight(40)
        self.pause_btn.setEnabled(False)
        control_layout.addWidget(self.pause_btn)

        # 停止按钮
        self.stop_btn = QPushButton("停止")
        self.stop_btn.setStyleSheet(self._get_button_style("#F44336"))
        self.stop_btn.setMinimumHeight(40)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)

        main_layout.addWidget(control_group)

        # 录制设置区域
        settings_group = QGroupBox("录制设置")
        settings_layout = QFormLayout(settings_group)

        # 视频格式
        self.format_combo = QComboBox()
        self.format_combo.addItems(["mp4", "flv", "avi", "mkv"])
        self.format_combo.setCurrentText(self.module.video_format)
        settings_layout.addRow("视频格式:", self.format_combo)

        # 视频质量
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["low", "medium", "high", "ultra"])
        self.quality_combo.setCurrentText(self.module.video_quality)
        settings_layout.addRow("视频质量:", self.quality_combo)

        # 帧率
        self.framerate_combo = QComboBox()
        self.framerate_combo.addItems(["15", "24", "30", "60"])
        self.framerate_combo.setCurrentText(str(self.module.frame_rate))
        settings_layout.addRow("帧率:", self.framerate_combo)

        # 音频录制
        self.audio_cb = QCheckBox("录制音频")
        self.audio_cb.setChecked(self.module.audio_enabled)
        settings_layout.addRow("", self.audio_cb)

        # 鼠标光标
        self.cursor_cb = QCheckBox("显示鼠标光标")
        self.cursor_cb.setChecked(self.module.cursor_enabled)
        settings_layout.addRow("", self.cursor_cb)

        # 输出目录
        output_layout = QHBoxLayout()
        self.output_edit = QLineEdit(self.module.output_directory)
        self.browse_btn = QPushButton("浏览")
        output_layout.addWidget(self.output_edit)
        output_layout.addWidget(self.browse_btn)
        settings_layout.addRow("输出目录:", output_layout)

        main_layout.addWidget(settings_group)

        # 录制历史区域
        history_group = QGroupBox("录制历史")
        history_layout = QVBoxLayout(history_group)

        # 历史列表
        self.history_list = QListWidget()
        self.history_list.setMaximumHeight(100)
        history_layout.addWidget(self.history_list)

        # 历史操作按钮
        history_buttons = QHBoxLayout()
        self.refresh_history_btn = QPushButton("刷新")
        self.open_folder_btn = QPushButton("打开文件夹")
        self.clear_history_btn = QPushButton("清空历史")

        history_buttons.addWidget(self.refresh_history_btn)
        history_buttons.addWidget(self.open_folder_btn)
        history_buttons.addWidget(self.clear_history_btn)
        history_buttons.addStretch()

        history_layout.addLayout(history_buttons)
        main_layout.addWidget(history_group)

        # 刷新历史列表
        self.refresh_history()

    def setup_signals(self):
        """设置信号连接"""
        # 录制控制信号
        self.record_btn.clicked.connect(self.toggle_recording)
        self.pause_btn.clicked.connect(self.toggle_pause)
        self.stop_btn.clicked.connect(self.stop_recording)

        # 设置变更信号
        self.format_combo.currentTextChanged.connect(self.update_settings)
        self.quality_combo.currentTextChanged.connect(self.update_settings)
        self.framerate_combo.currentTextChanged.connect(self.update_settings)
        self.audio_cb.toggled.connect(self.update_settings)
        self.cursor_cb.toggled.connect(self.update_settings)
        self.output_edit.textChanged.connect(self.update_settings)

        # 其他信号
        self.browse_btn.clicked.connect(self.browse_output_directory)
        self.refresh_history_btn.clicked.connect(self.refresh_history)
        self.open_folder_btn.clicked.connect(self.open_output_folder)
        self.clear_history_btn.clicked.connect(self.clear_history)

    def _get_button_style(self, color: str) -> str:
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background-color: {color}DD;
            }}
            QPushButton:pressed {{
                background-color: {color}BB;
            }}
            QPushButton:disabled {{
                background-color: #CCCCCC;
                color: #666666;
            }}
        """

    def toggle_recording(self):
        """切换录制状态"""
        if not self.module.is_recording:
            self.start_recording()
        else:
            self.toggle_pause()

    def start_recording(self):
        """开始录制"""
        # 更新设置
        self.update_settings()

        if self.module.start_recording():
            # 更新UI状态
            self.record_btn.setText("暂停录制")
            self.record_btn.setStyleSheet(self._get_button_style("#FF9800"))
            self.pause_btn.setEnabled(True)
            self.stop_btn.setEnabled(True)
            self.status_label.setText("正在录制...")

            # 禁用设置控件
            self._set_settings_enabled(False)
        else:
            QMessageBox.critical(self, "错误", "启动录制失败，请检查设置和FFmpeg是否正确安装")

    def toggle_pause(self):
        """切换暂停状态"""
        if self.module.is_paused:
            if self.module.resume_recording():
                self.record_btn.setText("暂停录制")
                self.record_btn.setStyleSheet(self._get_button_style("#FF9800"))
                self.status_label.setText("正在录制...")
        else:
            if self.module.pause_recording():
                self.record_btn.setText("继续录制")
                self.record_btn.setStyleSheet(self._get_button_style("#4CAF50"))
                self.status_label.setText("录制已暂停")

    def stop_recording(self):
        """停止录制"""
        if self.module.stop_recording():
            # 更新UI状态
            self.record_btn.setText("开始录制")
            self.record_btn.setStyleSheet(self._get_button_style("#4CAF50"))
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)
            self.status_label.setText("录制已完成")

            # 启用设置控件
            self._set_settings_enabled(True)

            # 刷新历史
            self.refresh_history()

            # 显示完成消息
            if self.module.output_file:
                QMessageBox.information(
                    self, "录制完成",
                    f"录制已完成！\n文件保存为: {self.module.output_file}"
                )

    def update_settings(self):
        """更新录制设置"""
        try:
            self.module.video_format = self.format_combo.currentText()
            self.module.video_quality = self.quality_combo.currentText()
            self.module.frame_rate = int(self.framerate_combo.currentText())
            self.module.audio_enabled = self.audio_cb.isChecked()
            self.module.cursor_enabled = self.cursor_cb.isChecked()
            self.module.output_directory = self.output_edit.text()

            # 确保输出目录存在
            if not os.path.exists(self.module.output_directory):
                os.makedirs(self.module.output_directory)

        except ValueError:
            pass  # 忽略无效的帧率值

    def _set_settings_enabled(self, enabled: bool):
        """设置录制设置控件的启用状态"""
        self.format_combo.setEnabled(enabled)
        self.quality_combo.setEnabled(enabled)
        self.framerate_combo.setEnabled(enabled)
        self.audio_cb.setEnabled(enabled)
        self.cursor_cb.setEnabled(enabled)
        self.output_edit.setEnabled(enabled)
        self.browse_btn.setEnabled(enabled)

    def browse_output_directory(self):
        """浏览输出目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择输出目录", self.output_edit.text()
        )
        if directory:
            self.output_edit.setText(directory)
            self.update_settings()

    def refresh_history(self):
        """刷新录制历史"""
        self.history_list.clear()
        history = self.module.get_recording_history()

        for record in reversed(history[-10:]):  # 显示最近10条记录
            file_name = record.get("file_name", "未知文件")
            status = record.get("status", "未知")
            duration = record.get("duration", 0)

            # 格式化时长
            hours = duration // 3600
            minutes = (duration % 3600) // 60
            seconds = duration % 60
            duration_str = f"{hours:02}:{minutes:02}:{seconds:02}"

            item_text = f"{file_name} - {status} - {duration_str}"
            self.history_list.addItem(item_text)

    def open_output_folder(self):
        """打开输出文件夹"""
        output_dir = self.module.output_directory
        if os.path.exists(output_dir):
            if platform.system() == "Windows":
                os.startfile(output_dir)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", output_dir])
            else:  # Linux
                subprocess.run(["xdg-open", output_dir])
        else:
            QMessageBox.warning(self, "警告", "输出目录不存在")

    def clear_history(self):
        """清空录制历史"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空录制历史吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.module.recording_history.clear()
            self.module._save_recording_history()
            self.refresh_history()

    def update_time_display(self, elapsed_time: int):
        """更新时间显示"""
        hours = elapsed_time // 3600
        minutes = (elapsed_time % 3600) // 60
        seconds = elapsed_time % 60
        time_str = f"{hours:02}:{minutes:02}:{seconds:02}"
        self.time_label.setText(time_str)

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.module.is_recording:
            reply = QMessageBox.question(
                self, "确认", "录制正在进行中，确定要关闭窗口吗？\n录制将会停止。",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.module.stop_recording()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

# 为了兼容性，保留原始类名
ScreenRecorder = ScreenRecorderModule
