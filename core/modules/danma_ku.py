import sys
import socketio
from PyQt5.QtWidgets import QApp<PERSON>, QMainWindow, QLabel
from PyQt5.QtCore import QTimer, Qt, pyqtSignal, QObject

# 创建一个信号类，用于在不同线程之间安全地更新UI
class DanmakuSignal(QObject):
    new_danmaku = pyqtSignal(str)

class DanmakuTeacherApp(QMainWindow):
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.sio = None
        self.danmaku_labels = []
        self.danmaku_speed = 5
        self.signals = DanmakuSignal()
        self.signals.new_danmaku.connect(self.create_danmaku)
        self.initUI()
        self.init_socketio_client()

    def initUI(self):
        self.setWindowTitle("教师端 - 弹幕显示")
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_danmaku)
        self.timer.start(30)

    def init_socketio_client(self):
        """初始化并连接Socket.IO客户端"""
        self.sio = socketio.Client()

        @self.sio.event
        def connect():
            print("[Socket.IO] 连接成功")
            # 加入教师自己的房间以接收定向消息
            if self.api_client and self.api_client.token:
                user_id = self.api_client.get('/api/user')['id'] # 假设有这样一个API
                self.sio.emit('join', {'room': f'teacher_{user_id}'})
                print(f"[Socket.IO] 已加入房间: teacher_{user_id}")

        @self.sio.event
        def disconnect():
            print("[Socket.IO] 已断开连接")

        @self.sio.on('student_interaction')
        def on_student_interaction(data):
            """处理学生互动事件"""
            print(f"[Socket.IO] 收到学生互动消息: {data}")
            content = data.get('content')
            student_name = data.get('student_name', '某学生')
            if content:
                # 通过信号在主线程创建弹幕，避免跨线程UI操作问题
                self.signals.new_danmaku.emit(f"{student_name}: {content}")
        
        try:
            # 从api_client获取服务器地址和token
            server_url = self.api_client.base_url
            auth_headers = self.api_client._get_auth_headers()
            self.sio.connect(server_url, headers=auth_headers, transports=['websocket'])
        except Exception as e:
            print(f"[Socket.IO] 连接失败: {e}")

    def create_danmaku(self, text):
        label = QLabel(text, self)
        label.setStyleSheet("color: white; font-size: 32px; font-weight: bold; background-color: rgba(0, 0, 0, 100); padding: 5px; border-radius: 5px;")
        label.adjustSize()
        y = self.get_random_y_position(label.height())
        label.move(self.width(), y)
        label.show()
        self.danmaku_labels.append(label)

    def get_random_y_position(self, label_height):
        import random
        screen_height = QApplication.primaryScreen().geometry().height()
        return random.randint(0, screen_height - label_height)

    def update_danmaku(self):
        for label in list(self.danmaku_labels):
            x = label.x() - self.danmaku_speed
            label.move(x, label.y())
            if x + label.width() < 0:
                label.deleteLater()
                self.danmaku_labels.remove(label)

    def closeEvent(self, event):
        if self.sio and self.sio.connected:
            self.sio.disconnect()
        super().closeEvent(event)

if __name__ == '__main__':
    from api_client import ApiClient
    from PyQt5.QtWidgets import QDialog, QLineEdit, QFormLayout, QDialogButtonBox

    class LoginDialog(QDialog):
        def __init__(self, parent=None):
            super().__init__(parent)
            self.setWindowTitle("登录")
            layout = QFormLayout(self)
            self.email_input = QLineEdit(self)
            self.password_input = QLineEdit(self)
            self.password_input.setEchoMode(QLineEdit.Password)
            layout.addRow("邮箱:", self.email_input)
            layout.addRow("密码:", self.password_input)
            buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, Qt.Horizontal, self)
            buttons.accepted.connect(self.accept)
            buttons.rejected.connect(self.reject)
            layout.addWidget(buttons)

        def getCredentials(self):
            return self.email_input.text(), self.password_input.text()

    app = QApplication(sys.argv)
    login_dialog = LoginDialog()
    if login_dialog.exec() == QDialog.Accepted:
        email, password = login_dialog.getCredentials()
        api_client_instance = ApiClient(base_url="https://192.168.0.11:5000")
        logged_in, user_info = api_client_instance.login(email, password)
        
        if logged_in:
            main_window = DanmakuTeacherApp(api_client=api_client_instance)
            main_window.showFullScreen()
            sys.exit(app.exec_())
        else:
            print("登录失败")
            sys.exit(1)
    else:
        sys.exit(0)
