"""
小组屏幕看板功能模块 - 教师专用功能
集成MediaMTX接收小组设备广播流，实现多路视频流同时显示
开发屏幕切换和布局管理功能
"""
import sys
import os
import json
import subprocess
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QListWidget, QListWidgetItem,
                            QGroupBox, QTextEdit, QCheckBox, QSpinBox,
                            QMessageBox, QProgressBar, QFrame, QGridLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QDialog, QDialogButtonBox, QInputDialog, QScrollArea)
from PyQt5.QtCore import QTimer, pyqtSignal, Qt, QThread, pyqtSlot, QObject, QUrl
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPixmap, QPainter
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtMultimediaWidgets import QVideoWidget
from .base_module import TeacherModule
from .udp_discovery import DeviceInfo
import logging

class StreamInfo:
    """视频流信息类"""
    
    def __init__(self, stream_id: str, device_id: str, device_name: str, 
                 stream_url: str, resolution: str = "1920x1080"):
        self.stream_id = stream_id
        self.device_id = device_id
        self.device_name = device_name
        self.stream_url = stream_url
        self.resolution = resolution
        self.is_active = False
        self.last_update = datetime.now()
        self.viewer_count = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "stream_id": self.stream_id,
            "device_id": self.device_id,
            "device_name": self.device_name,
            "stream_url": self.stream_url,
            "resolution": self.resolution,
            "is_active": self.is_active,
            "last_update": self.last_update.isoformat(),
            "viewer_count": self.viewer_count
        }

class VideoStreamWidget(QWidget):
    """视频流显示组件"""
    
    stream_clicked = pyqtSignal(str)  # stream_id
    
    def __init__(self, stream_info: StreamInfo):
        super().__init__()
        self.stream_info = stream_info
        self.media_player = None
        self.video_widget = None
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 设备信息标签
        self.info_label = QLabel(f"{self.stream_info.device_name}")
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.setStyleSheet("""
            QLabel {
                background-color: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 5px;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.info_label)
        
        # 视频显示区域
        self.video_widget = QVideoWidget()
        self.video_widget.setMinimumSize(320, 240)
        self.video_widget.setStyleSheet("""
            QVideoWidget {
                background-color: black;
                border: 2px solid #007acc;
            }
        """)
        layout.addWidget(self.video_widget)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.play_btn = QPushButton("播放")
        self.play_btn.clicked.connect(self.toggle_playback)
        control_layout.addWidget(self.play_btn)
        
        self.fullscreen_btn = QPushButton("全屏")
        self.fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        control_layout.addWidget(self.fullscreen_btn)
        
        layout.addLayout(control_layout)
        
        # 状态标签
        self.status_label = QLabel("未连接")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)
        
        # 创建媒体播放器
        self.media_player = QMediaPlayer()
        self.media_player.setVideoOutput(self.video_widget)
        self.media_player.stateChanged.connect(self.on_state_changed)
        self.media_player.error.connect(self.on_error)
        
        # 设置点击事件
        self.video_widget.mousePressEvent = self.on_video_clicked
    
    def start_stream(self):
        """开始播放流"""
        try:
            media_content = QMediaContent(QUrl(self.stream_info.stream_url))
            self.media_player.setMedia(media_content)
            self.media_player.play()
            self.status_label.setText("连接中...")
        except Exception as e:
            self.status_label.setText(f"错误: {str(e)}")
    
    def stop_stream(self):
        """停止播放流"""
        if self.media_player:
            self.media_player.stop()
            self.status_label.setText("已停止")
    
    def toggle_playback(self):
        """切换播放状态"""
        if self.media_player.state() == QMediaPlayer.PlayingState:
            self.media_player.pause()
        else:
            self.media_player.play()
    
    def toggle_fullscreen(self):
        """切换全屏模式"""
        if self.video_widget.isFullScreen():
            self.video_widget.setFullScreen(False)
            self.fullscreen_btn.setText("全屏")
        else:
            self.video_widget.setFullScreen(True)
            self.fullscreen_btn.setText("退出全屏")
    
    def on_state_changed(self, state):
        """播放状态改变处理"""
        if state == QMediaPlayer.PlayingState:
            self.play_btn.setText("暂停")
            self.status_label.setText("播放中")
        elif state == QMediaPlayer.PausedState:
            self.play_btn.setText("播放")
            self.status_label.setText("已暂停")
        elif state == QMediaPlayer.StoppedState:
            self.play_btn.setText("播放")
            self.status_label.setText("已停止")
    
    def on_error(self, error):
        """错误处理"""
        self.status_label.setText(f"播放错误: {error}")
    
    def on_video_clicked(self, event):
        """视频点击事件"""
        self.stream_clicked.emit(self.stream_info.stream_id)

class LayoutManager:
    """布局管理器"""
    
    LAYOUTS = {
        "grid_2x2": {"rows": 2, "cols": 2, "max_streams": 4},
        "grid_3x3": {"rows": 3, "cols": 3, "max_streams": 9},
        "grid_4x4": {"rows": 4, "cols": 4, "max_streams": 16},
        "single": {"rows": 1, "cols": 1, "max_streams": 1},
        "pip": {"rows": 1, "cols": 1, "max_streams": 2}  # 画中画
    }
    
    @classmethod
    def get_layout_info(cls, layout_name: str) -> Dict[str, int]:
        """获取布局信息"""
        return cls.LAYOUTS.get(layout_name, cls.LAYOUTS["grid_2x2"])
    
    @classmethod
    def get_available_layouts(cls) -> List[str]:
        """获取可用布局列表"""
        return list(cls.LAYOUTS.keys())

class GroupScreenMonitorModule(TeacherModule):
    """小组屏幕看板功能模块"""
    
    # 模块信号
    stream_added = pyqtSignal(object)      # StreamInfo
    stream_removed = pyqtSignal(str)       # stream_id
    layout_changed = pyqtSignal(str)       # layout_name
    stream_selected = pyqtSignal(str)      # stream_id
    
    def __init__(self):
        super().__init__(
            module_name="group_screen_monitor",
            display_name="小组屏幕看板",
            version="1.0.0"
        )
        
        # 流相关
        self.available_devices: Dict[str, DeviceInfo] = {}
        self.active_streams: Dict[str, StreamInfo] = {}
        self.stream_widgets: Dict[str, VideoStreamWidget] = {}
        
        # 界面
        self.monitor_window = None
        
        # 布局
        self.current_layout = "grid_2x2"
        self.selected_stream = None
        
        # MediaMTX相关
        self.mediamtx_url = "rtsp://localhost:8554"
        self.mediamtx_process = None
        
        # 配置
        self.config.update({
            "auto_discover_streams": True,      # 自动发现流
            "max_concurrent_streams": 9,       # 最大并发流数
            "stream_timeout": 30,              # 流超时时间（秒）
            "auto_layout": True,               # 自动布局
            "enable_audio": False,             # 启用音频
            "video_quality": "medium",         # 视频质量
            "mediamtx_port": 8554              # MediaMTX端口
        })
    
    def get_widget(self):
        return self.monitor_window

    def _initialize_module(self) -> bool:
        """初始化小组屏幕看板模块"""
        try:
            # 检查认证状态
            if not self.is_authenticated():
                self.logger.error("小组屏幕看板模块需要教师认证")
                return False
            
            # 检查MediaMTX是否可用
            if not self._check_mediamtx_available():
                self.logger.warning("MediaMTX不可用，将使用备用方案")
            
            # 创建界面
            self.monitor_window = GroupScreenMonitorWindow(self)
            
            # 获取设备发现模块，监听设备变化
            self._setup_device_discovery()
            
            # 启动流发现
            if self.get_config("auto_discover_streams", True):
                self._start_stream_discovery()
            
            self.logger.info("小组屏幕看板模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"小组屏幕看板模块初始化失败: {str(e)}")
            return False
    
    def _cleanup_module(self):
        """清理小组屏幕看板模块资源"""
        try:
            # 停止所有流
            for stream_widget in self.stream_widgets.values():
                stream_widget.stop_stream()
            
            # 停止MediaMTX
            self._stop_mediamtx()
            
            # 关闭界面
            if self.monitor_window:
                self.monitor_window.close()
                self.monitor_window = None
            
            # 清理数据
            self.available_devices.clear()
            self.active_streams.clear()
            self.stream_widgets.clear()
            
            self.logger.info("小组屏幕看板模块资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理小组屏幕看板模块资源时出错: {str(e)}")
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "add_stream":
            device_id = data.get("device_id")
            stream_url = data.get("stream_url")
            if device_id and stream_url:
                self.add_stream(device_id, stream_url)
        
        elif message_type == "remove_stream":
            stream_id = data.get("stream_id")
            if stream_id:
                self.remove_stream(stream_id)
        
        elif message_type == "change_layout":
            layout_name = data.get("layout_name")
            if layout_name:
                self.change_layout(layout_name)
        
        elif message_type == "select_stream":
            stream_id = data.get("stream_id")
            if stream_id:
                self.select_stream(stream_id)
        
        elif message_type == "get_monitor_status":
            self.send_monitor_status(sender)
        
        elif message_type == "show_monitor":
            self.show()
    
    def _check_mediamtx_available(self) -> bool:
        """检查MediaMTX是否可用"""
        try:
            # 尝试连接MediaMTX API
            import requests
            response = requests.get(f"http://localhost:9997/v3/config/global/get", timeout=5)
            return response.status_code == 200
        except Exception:
            return False
    
    def _setup_device_discovery(self):
        """设置设备发现"""
        try:
            # 获取UDP发现模块
            udp_discovery = self._get_udp_discovery_module()
            if udp_discovery:
                # 连接设备发现信号
                udp_discovery.device_discovered.connect(self.on_device_discovered)
                udp_discovery.device_updated.connect(self.on_device_updated)
                udp_discovery.device_offline.connect(self.on_device_offline)
                
                # 获取当前设备列表
                self.available_devices = udp_discovery.get_discovered_devices()
        except Exception as e:
            self.logger.error(f"设置设备发现失败: {e}")
    
    def _get_udp_discovery_module(self):
        """获取UDP发现模块实例"""
        try:
            if hasattr(self, 'module_manager') and self.module_manager:
                return self.module_manager.get_module_instance("udp_discovery")
            return None
        except Exception as e:
            self.logger.error(f"获取UDP发现模块失败: {e}")
            return None
    
    def _start_stream_discovery(self):
        """启动流发现"""
        try:
            # 为支持屏幕广播的设备自动添加流
            for device_id, device in self.available_devices.items():
                if "screen_share" in device.capabilities and device.status == "online":
                    stream_url = f"{self.mediamtx_url}/{device_id}"
                    self.add_stream(device_id, stream_url)
        except Exception as e:
            self.logger.error(f"启动流发现失败: {e}")
    
    def _start_mediamtx(self) -> bool:
        """启动MediaMTX"""
        try:
            if self.mediamtx_process and self.mediamtx_process.poll() is None:
                return True  # 已经在运行
            
            # 启动MediaMTX进程
            cmd = ['mediamtx', f'--rtsp-port={self.get_config("mediamtx_port", 8554)}']
            self.mediamtx_process = subprocess.Popen(cmd, 
                                                   stdout=subprocess.PIPE, 
                                                   stderr=subprocess.PIPE)
            
            # 等待启动
            time.sleep(2)
            
            if self.mediamtx_process.poll() is None:
                self.logger.info("MediaMTX启动成功")
                return True
            else:
                self.logger.error("MediaMTX启动失败")
                return False
                
        except Exception as e:
            self.logger.error(f"启动MediaMTX失败: {str(e)}")
            return False
    
    def _stop_mediamtx(self):
        """停止MediaMTX"""
        try:
            if self.mediamtx_process and self.mediamtx_process.poll() is None:
                self.mediamtx_process.terminate()
                self.mediamtx_process.wait(timeout=5)
                self.logger.info("MediaMTX已停止")
        except Exception as e:
            self.logger.error(f"停止MediaMTX失败: {str(e)}")

    def show(self):
        """显示小组屏幕看板窗口"""
        if self.monitor_window:
            self.monitor_window.show()
            self.monitor_window.raise_()
            self.monitor_window.activateWindow()

    def hide(self):
        """隐藏小组屏幕看板窗口"""
        if self.monitor_window:
            self.monitor_window.hide()

    def close(self):
        """关闭小组屏幕看板模块"""
        self.cleanup()

    def add_stream(self, device_id: str, stream_url: str) -> Optional[str]:
        """添加视频流"""
        try:
            if device_id not in self.available_devices:
                self.logger.error(f"设备 {device_id} 不可用")
                return None

            # 检查是否已达到最大流数
            max_streams = self.get_config("max_concurrent_streams", 9)
            if len(self.active_streams) >= max_streams:
                self.logger.error("已达到最大并发流数")
                return None

            device = self.available_devices[device_id]
            stream_id = f"stream_{device_id}_{int(time.time())}"

            # 创建流信息
            stream_info = StreamInfo(stream_id, device_id, device.device_name, stream_url)
            self.active_streams[stream_id] = stream_info

            # 创建流显示组件
            stream_widget = VideoStreamWidget(stream_info)
            stream_widget.stream_clicked.connect(self.on_stream_clicked)
            self.stream_widgets[stream_id] = stream_widget

            # 开始播放流
            stream_widget.start_stream()
            stream_info.is_active = True

            # 发送信号
            self.stream_added.emit(stream_info)

            # 通知其他模块
            self.broadcast_message("stream_added", {
                "stream_id": stream_id,
                "device_id": device_id,
                "device_name": device.device_name,
                "stream_url": stream_url,
                "timestamp": datetime.now().isoformat()
            })

            # 自动调整布局
            if self.get_config("auto_layout", True):
                self._auto_adjust_layout()

            self.logger.info(f"添加视频流: {device.device_name}")
            return stream_id

        except Exception as e:
            self.logger.error(f"添加视频流失败: {str(e)}")
            return None

    def remove_stream(self, stream_id: str):
        """移除视频流"""
        try:
            if stream_id not in self.active_streams:
                self.logger.warning(f"视频流 {stream_id} 不存在")
                return

            stream_info = self.active_streams[stream_id]

            # 停止播放
            if stream_id in self.stream_widgets:
                self.stream_widgets[stream_id].stop_stream()
                del self.stream_widgets[stream_id]

            # 移除流信息
            del self.active_streams[stream_id]

            # 发送信号
            self.stream_removed.emit(stream_id)

            # 通知其他模块
            self.broadcast_message("stream_removed", {
                "stream_id": stream_id,
                "device_id": stream_info.device_id,
                "timestamp": datetime.now().isoformat()
            })

            # 自动调整布局
            if self.get_config("auto_layout", True):
                self._auto_adjust_layout()

            self.logger.info(f"移除视频流: {stream_info.device_name}")

        except Exception as e:
            self.logger.error(f"移除视频流失败: {str(e)}")

    def change_layout(self, layout_name: str):
        """改变布局"""
        try:
            if layout_name not in LayoutManager.get_available_layouts():
                self.logger.error(f"布局 {layout_name} 不支持")
                return

            layout_info = LayoutManager.get_layout_info(layout_name)
            max_streams = layout_info["max_streams"]

            # 检查当前流数是否超过布局限制
            if len(self.active_streams) > max_streams:
                self.logger.warning(f"当前流数({len(self.active_streams)})超过布局限制({max_streams})")

            self.current_layout = layout_name

            # 发送信号
            self.layout_changed.emit(layout_name)

            # 通知界面更新布局
            if self.monitor_window:
                self.monitor_window.update_layout()

            self.logger.info(f"布局已改变为: {layout_name}")

        except Exception as e:
            self.logger.error(f"改变布局失败: {str(e)}")

    def select_stream(self, stream_id: str):
        """选择流"""
        try:
            if stream_id not in self.active_streams:
                self.logger.warning(f"视频流 {stream_id} 不存在")
                return

            self.selected_stream = stream_id

            # 发送信号
            self.stream_selected.emit(stream_id)

            stream_info = self.active_streams[stream_id]
            self.logger.info(f"选择视频流: {stream_info.device_name}")

        except Exception as e:
            self.logger.error(f"选择视频流失败: {str(e)}")

    def _auto_adjust_layout(self):
        """自动调整布局"""
        try:
            stream_count = len(self.active_streams)

            if stream_count == 0:
                return
            elif stream_count == 1:
                self.change_layout("single")
            elif stream_count <= 4:
                self.change_layout("grid_2x2")
            elif stream_count <= 9:
                self.change_layout("grid_3x3")
            else:
                self.change_layout("grid_4x4")

        except Exception as e:
            self.logger.error(f"自动调整布局失败: {str(e)}")

    def on_stream_clicked(self, stream_id: str):
        """流点击事件处理"""
        self.select_stream(stream_id)

    def on_device_discovered(self, device_info: DeviceInfo):
        """设备发现事件处理"""
        self.available_devices[device_info.device_id] = device_info

        # 如果设备支持屏幕广播，自动添加流
        if (self.get_config("auto_discover_streams", True) and
            "screen_share" in device_info.capabilities and
            device_info.status == "online"):
            stream_url = f"{self.mediamtx_url}/{device_info.device_id}"
            self.add_stream(device_info.device_id, stream_url)

        self.logger.info(f"发现新设备: {device_info.device_name}")

    def on_device_updated(self, device_info: DeviceInfo):
        """设备更新事件处理"""
        self.available_devices[device_info.device_id] = device_info

    def on_device_offline(self, device_id: str):
        """设备离线事件处理"""
        if device_id in self.available_devices:
            del self.available_devices[device_id]

        # 移除相关的视频流
        streams_to_remove = []
        for stream_id, stream_info in self.active_streams.items():
            if stream_info.device_id == device_id:
                streams_to_remove.append(stream_id)

        for stream_id in streams_to_remove:
            self.remove_stream(stream_id)

    def get_available_devices(self) -> List[DeviceInfo]:
        """获取支持屏幕广播的设备列表"""
        return [device for device in self.available_devices.values()
                if "screen_share" in device.capabilities and device.status == "online"]

    def get_monitor_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            "available_devices": len(self.available_devices),
            "active_streams": len(self.active_streams),
            "current_layout": self.current_layout,
            "selected_stream": self.selected_stream,
            "streams": [stream.to_dict() for stream in self.active_streams.values()]
        }

    def send_monitor_status(self, requester: str):
        """发送监控状态"""
        status = self.get_monitor_status()
        self.send_message(requester, "monitor_status", status)

class GroupScreenMonitorWindow(QWidget):
    """小组屏幕看板窗口"""

    def __init__(self, module: 'GroupScreenMonitorModule'):
        super().__init__()
        self.module = module
        self.setWindowTitle("小组屏幕看板")
        self.setGeometry(100, 100, 1280, 720)
        self.setWindowIcon(QIcon(os.path.join(os.path.dirname(__file__), '..', 'assets', 'icons', 'monitor.png')))
        self.setStyleSheet("background-color: #333;")

        self._init_ui()
        self._connect_signals()
        self.update_layout()

    def _init_ui(self):
        """初始化UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # 顶部工具栏
        toolbar = QFrame()
        toolbar.setStyleSheet("background-color: #444; border-radius: 5px;")
        toolbar.setFixedHeight(50)
        toolbar_layout = QHBoxLayout(toolbar)
        
        toolbar_layout.addWidget(QLabel("布局模式:", styleSheet="color: white; font-weight: bold;"))
        self.layout_combo = QComboBox()
        self.layout_combo.addItems(LayoutManager.get_available_layouts())
        toolbar_layout.addWidget(self.layout_combo)

        toolbar_layout.addStretch()

        self.status_label = QLabel("活动流: 0", styleSheet="color: white;")
        toolbar_layout.addWidget(self.status_label)

        self.stop_all_button = QPushButton("全部停止")
        toolbar_layout.addWidget(self.stop_all_button)

        main_layout.addWidget(toolbar)

        # 视频流网格区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setStyleSheet("border: none;")
        self.grid_container = QWidget()
        self.video_grid = QGridLayout(self.grid_container)
        self.video_grid.setSpacing(5)
        self.scroll_area.setWidget(self.grid_container)
        main_layout.addWidget(self.scroll_area)

    def _connect_signals(self):
        """连接信号和槽"""
        self.layout_combo.currentTextChanged.connect(self.module.change_layout)
        self.stop_all_button.clicked.connect(self.stop_all_streams)

        # Module signals
        self.module.stream_added.connect(self.on_stream_changed)
        self.module.stream_removed.connect(self.on_stream_changed)
        self.module.layout_changed.connect(self.on_layout_changed)
        self.module.stream_selected.connect(self.on_stream_selected)

    @pyqtSlot()
    def update_layout(self):
        """根据当前布局和活动流更新网格"""
        # 清除旧布局
        while self.video_grid.count():
            child = self.video_grid.takeAt(0)
            if child.widget():
                child.widget().setParent(None)

        layout_info = LayoutManager.get_layout_info(self.module.current_layout)
        rows, cols = layout_info["rows"], layout_info["cols"]
        
        active_widgets = list(self.module.stream_widgets.values())
        
        for i, widget in enumerate(active_widgets):
            if i >= rows * cols:
                widget.hide()
                continue
            
            row, col = i // cols, i % cols
            self.video_grid.addWidget(widget, row, col)
            widget.show()

        self.status_label.setText(f"活动流: {len(active_widgets)}")

    @pyqtSlot()
    def stop_all_streams(self):
        """停止所有视频流"""
        for stream_id in list(self.module.active_streams.keys()):
            self.module.remove_stream(stream_id)

    @pyqtSlot()
    def on_stream_changed(self):
        """流添加或移除时的槽函数"""
        self.update_layout()

    @pyqtSlot(str)
    def on_layout_changed(self, layout_name: str):
        """布局改变时的槽函数"""
        # 防止信号循环
        if self.layout_combo.currentText() != layout_name:
            self.layout_combo.setCurrentText(layout_name)
        self.update_layout()

    @pyqtSlot(str)
    def on_stream_selected(self, stream_id: str):
        """流被选中时的槽函数，可以实现放大等功能"""
        widget = self.module.stream_widgets.get(stream_id)
        if widget:
            # 示例：将选中的流切换到单屏放大模式
            if self.module.current_layout != "single":
                self.module.change_layout("single")
            
            # 将选中的widget放到第一个位置
            current_widgets = list(self.module.stream_widgets.values())
            selected_widget = self.module.stream_widgets.pop(stream_id)
            self.module.stream_widgets = {stream_id: selected_widget, **self.module.stream_widgets}
            self.update_layout()

    def closeEvent(self, event):
        """关闭窗口事件"""
        self.hide()
        event.ignore()
