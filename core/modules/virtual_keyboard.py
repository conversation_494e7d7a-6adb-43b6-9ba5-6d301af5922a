"""
虚拟键盘功能模块 - 教师专用功能
使用dbus-send命令调起Onboard虚拟键盘，实现教师与小组设备间互相调用键盘
开发键盘状态监控和输入同步功能
"""
import sys
import os
import json
import subprocess
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QListWidget, QListWidgetItem,
                            QGroupBox, QTextEdit, QCheckBox, QSpinBox,
                            QMessageBox, QProgressBar, QFrame, QGridLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView,QAbstractItemView,
                            QDialog, QDialogButtonBox, QInputDialog, QLineEdit)
from PyQt5.QtCore import QTimer, pyqtSignal, Qt, QThread, pyqtSlot, QObject
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPixmap, QPainter
from .base_module import TeacherModule
from .udp_discovery import DeviceInfo
import logging

class KeyboardSession:
    """键盘会话类"""
    
    def __init__(self, session_id: str, source_device: str, target_device: str):
        self.session_id = session_id
        self.source_device = source_device  # 发起键盘调用的设备
        self.target_device = target_device  # 目标设备
        self.start_time = datetime.now()
        self.is_active = True
        self.input_events: List[Dict[str, Any]] = []
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "session_id": self.session_id,
            "source_device": self.source_device,
            "target_device": self.target_device,
            "start_time": self.start_time.isoformat(),
            "is_active": self.is_active,
            "input_events_count": len(self.input_events)
        }

class KeyboardMonitor(QObject):
    """键盘状态监控器"""
    
    keyboard_state_changed = pyqtSignal(str, bool)  # device_id, is_visible
    input_event_detected = pyqtSignal(str, dict)    # device_id, event_data
    
    def __init__(self, device_id: str, device_ip: str):
        super().__init__()
        self.device_id = device_id
        self.device_ip = device_ip
        self.is_monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self):
        """开始监控"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1)
    
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 检查键盘状态
                is_visible = self._check_keyboard_visibility()
                self.keyboard_state_changed.emit(self.device_id, is_visible)
                
                time.sleep(1)  # 每秒检查一次
            except Exception as e:
                break
    
    def _check_keyboard_visibility(self) -> bool:
        """检查键盘是否可见"""
        try:
            if self.device_ip == "localhost" or self.device_ip == "127.0.0.1":
                # 本地设备
                cmd = [
                    'dbus-send', '--session', '--print-reply',
                    '--dest=org.onboard.Onboard',
                    '/org/onboard/Onboard/Keyboard',
                    'org.freedesktop.DBus.Properties.Get',
                    'string:org.onboard.Onboard.Keyboard',
                    'string:Visible'
                ]
            else:
                # 远程设备
                cmd = [
                    'ssh', f'user@{self.device_ip}',
                    'dbus-send --session --print-reply '
                    '--dest=org.onboard.Onboard '
                    '/org/onboard/Onboard/Keyboard '
                    'org.freedesktop.DBus.Properties.Get '
                    'string:org.onboard.Onboard.Keyboard '
                    'string:Visible'
                ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            
            # 解析返回结果
            if result.returncode == 0 and 'true' in result.stdout.lower():
                return True
            return False
            
        except Exception:
            return False

class VirtualKeyboardModule(TeacherModule):
    """虚拟键盘功能模块"""
    
    # 模块信号
    keyboard_session_started = pyqtSignal(object)  # KeyboardSession
    keyboard_session_ended = pyqtSignal(str)       # session_id
    keyboard_toggled = pyqtSignal(str, bool)       # device_id, is_visible
    input_synchronized = pyqtSignal(str, dict)     # device_id, input_data
    
    def __init__(self):
        super().__init__(
            module_name="virtual_keyboard",
            display_name="虚拟键盘",
            version="1.0.0"
        )
        
        # 键盘相关
        self.available_devices: Dict[str, DeviceInfo] = {}
        self.active_sessions: Dict[str, KeyboardSession] = {}
        self.keyboard_monitors: Dict[str, KeyboardMonitor] = {}
        
        # 界面
        self.keyboard_window = None
        
        # 配置
        self.config.update({
            "enable_remote_keyboard": True,     # 启用远程键盘
            "auto_monitor": True,               # 自动监控键盘状态
            "sync_input": True,                 # 同步输入
            "max_sessions": 5,                  # 最大会话数
            "session_timeout": 300,             # 会话超时时间（秒）
            "enable_input_logging": False       # 启用输入日志（安全考虑）
        })
    
    def get_widget(self):
        return self.keyboard_window

    def _initialize_module(self) -> bool:
        """初始化虚拟键盘模块"""
        try:
            # 检查认证状态
            if not self.is_authenticated():
                self.logger.error("虚拟键盘模块需要教师认证")
                return False
            
            # 检查dbus-send是否可用
            if not self._check_dbus_available():
                self.logger.error("dbus-send不可用，无法启用虚拟键盘功能")
                return False
            
            # 检查Onboard是否安装
            if not self._check_onboard_available():
                self.logger.warning("Onboard虚拟键盘未安装，部分功能可能不可用")
            
            # 创建界面
            self.keyboard_window = VirtualKeyboardWindow(self)
            
            # 获取设备发现模块，监听设备变化
            self._setup_device_discovery()
            
            self.logger.info("虚拟键盘模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"虚拟键盘模块初始化失败: {str(e)}")
            return False
    
    def _cleanup_module(self):
        """清理虚拟键盘模块资源"""
        try:
            # 停止所有键盘会话
            for session_id in list(self.active_sessions.keys()):
                self.end_keyboard_session(session_id)
            
            # 停止所有监控器
            for monitor in self.keyboard_monitors.values():
                monitor.stop_monitoring()
            self.keyboard_monitors.clear()
            
            # 关闭界面
            if self.keyboard_window:
                self.keyboard_window.close()
                self.keyboard_window = None
            
            # 清理数据
            self.available_devices.clear()
            self.active_sessions.clear()
            
            self.logger.info("虚拟键盘模块资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理虚拟键盘模块资源时出错: {str(e)}")
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "toggle_keyboard":
            device_id = data.get("device_id")
            if device_id:
                self.toggle_virtual_keyboard(device_id)
        
        elif message_type == "start_keyboard_session":
            target_device_id = data.get("target_device_id")
            if target_device_id:
                self.start_keyboard_session(target_device_id)
        
        elif message_type == "end_keyboard_session":
            session_id = data.get("session_id")
            if session_id:
                self.end_keyboard_session(session_id)
        
        elif message_type == "send_input":
            device_id = data.get("device_id")
            input_text = data.get("input_text")
            if device_id and input_text:
                self.send_input_to_device(device_id, input_text)
        
        elif message_type == "get_keyboard_status":
            self.send_keyboard_status(sender)
        
        elif message_type == "show_keyboard":
            self.show()
    
    def _check_dbus_available(self) -> bool:
        """检查dbus-send是否可用"""
        try:
            result = subprocess.run(['which', 'dbus-send'], 
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except Exception:
            return False
    
    def _check_onboard_available(self) -> bool:
        """检查Onboard是否可用"""
        try:
            result = subprocess.run(['which', 'onboard'], 
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except Exception:
            return False
    
    def _setup_device_discovery(self):
        """设置设备发现"""
        try:
            # 获取UDP发现模块
            udp_discovery = self._get_udp_discovery_module()
            if udp_discovery:
                # 连接设备发现信号
                udp_discovery.device_discovered.connect(self.on_device_discovered)
                udp_discovery.device_updated.connect(self.on_device_updated)
                udp_discovery.device_offline.connect(self.on_device_offline)
                
                # 获取当前设备列表
                self.available_devices = udp_discovery.get_discovered_devices()
                
                # 为在线设备创建监控器
                if self.get_config("auto_monitor", True):
                    self._create_monitors_for_devices()
        except Exception as e:
            self.logger.error(f"设置设备发现失败: {e}")
    
    def _get_udp_discovery_module(self):
        """获取UDP发现模块实例"""
        try:
            if hasattr(self, 'module_manager') and self.module_manager:
                return self.module_manager.get_module_instance("udp_discovery")
            return None
        except Exception as e:
            self.logger.error(f"获取UDP发现模块失败: {e}")
            return None
    
    def _create_monitors_for_devices(self):
        """为设备创建监控器"""
        for device_id, device in self.available_devices.items():
            if device.status == "online" and device_id not in self.keyboard_monitors:
                monitor = KeyboardMonitor(device_id, device.ip_address)
                monitor.keyboard_state_changed.connect(self.on_keyboard_state_changed)
                monitor.input_event_detected.connect(self.on_input_event_detected)
                
                self.keyboard_monitors[device_id] = monitor
                monitor.start_monitoring()
    
    def show(self):
        """显示虚拟键盘窗口"""
        if self.keyboard_window:
            self.keyboard_window.show()
            self.keyboard_window.raise_()
            self.keyboard_window.activateWindow()
    
    def hide(self):
        """隐藏虚拟键盘窗口"""
        if self.keyboard_window:
            self.keyboard_window.hide()
    
    def close(self):
        """关闭虚拟键盘模块"""
        self.cleanup()
    
    def toggle_virtual_keyboard(self, device_id: str) -> bool:
        """切换虚拟键盘显示状态"""
        try:
            if device_id not in self.available_devices:
                self.logger.error(f"设备 {device_id} 不可用")
                return False
            
            device = self.available_devices[device_id]
            
            # 构建dbus-send命令
            cmd = [
                'dbus-send', '--session',
                '--dest=org.onboard.Onboard',
                '--type=method_call',
                '/org/onboard/Onboard/Keyboard',
                'org.onboard.Onboard.Keyboard.ToggleVisible'
            ]
            
            if device.ip_address not in ["localhost", "127.0.0.1"]:
                # 远程设备
                remote_cmd = ' '.join(cmd)
                cmd = ['ssh', f'user@{device.ip_address}', remote_cmd]
            
            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                self.logger.info(f"成功切换设备 {device.device_name} 的虚拟键盘")
                
                # 发送信号
                self.keyboard_toggled.emit(device_id, True)  # 假设切换后为可见
                
                # 通知其他模块
                self.broadcast_message("keyboard_toggled", {
                    "device_id": device_id,
                    "device_name": device.device_name,
                    "timestamp": datetime.now().isoformat()
                })
                
                return True
            else:
                self.logger.error(f"切换虚拟键盘失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"切换虚拟键盘失败: {str(e)}")
            return False

    def start_keyboard_session(self, target_device_id: str) -> Optional[str]:
        """开始键盘会话"""
        try:
            if target_device_id not in self.available_devices:
                self.logger.error(f"目标设备 {target_device_id} 不可用")
                return None

            # 检查是否已达到最大会话数
            if len(self.active_sessions) >= self.get_config("max_sessions", 5):
                self.logger.error("已达到最大键盘会话数")
                return None

            # 创建会话
            session_id = f"keyboard_session_{int(time.time())}"
            source_device = "teacher_main"  # 教师设备ID

            session = KeyboardSession(session_id, source_device, target_device_id)
            self.active_sessions[session_id] = session

            # 发送信号
            self.keyboard_session_started.emit(session)

            # 通知其他模块
            self.broadcast_message("keyboard_session_started", {
                "session_id": session_id,
                "source_device": source_device,
                "target_device": target_device_id,
                "timestamp": datetime.now().isoformat()
            })

            self.logger.info(f"键盘会话已开始: {session_id}")
            return session_id

        except Exception as e:
            self.logger.error(f"开始键盘会话失败: {str(e)}")
            return None

    def end_keyboard_session(self, session_id: str):
        """结束键盘会话"""
        try:
            if session_id not in self.active_sessions:
                self.logger.warning(f"键盘会话 {session_id} 不存在")
                return

            session = self.active_sessions[session_id]
            session.is_active = False

            # 移除会话
            del self.active_sessions[session_id]

            # 发送信号
            self.keyboard_session_ended.emit(session_id)

            # 通知其他模块
            self.broadcast_message("keyboard_session_ended", {
                "session_id": session_id,
                "timestamp": datetime.now().isoformat()
            })

            self.logger.info(f"键盘会话已结束: {session_id}")

        except Exception as e:
            self.logger.error(f"结束键盘会话失败: {str(e)}")

    def send_input_to_device(self, device_id: str, input_text: str) -> bool:
        """向设备发送输入"""
        try:
            if device_id not in self.available_devices:
                self.logger.error(f"设备 {device_id} 不可用")
                return False

            device = self.available_devices[device_id]

            # 构建输入命令
            if device.ip_address in ["localhost", "127.0.0.1"]:
                # 本地设备
                cmd = ['xdotool', 'type', input_text]
            else:
                # 远程设备
                cmd = ['ssh', f'user@{device.ip_address}', f'xdotool type "{input_text}"']

            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                # 记录输入事件
                input_event = {
                    "type": "text_input",
                    "text": input_text,
                    "timestamp": datetime.now().isoformat(),
                    "device_id": device_id
                }

                # 添加到活跃会话
                for session in self.active_sessions.values():
                    if session.target_device == device_id and session.is_active:
                        session.input_events.append(input_event)

                # 发送信号
                self.input_synchronized.emit(device_id, input_event)

                self.logger.info(f"成功向设备 {device.device_name} 发送输入")
                return True
            else:
                self.logger.error(f"发送输入失败: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"发送输入失败: {str(e)}")
            return False

    def send_key_to_device(self, device_id: str, key: str) -> bool:
        """向设备发送按键"""
        try:
            if device_id not in self.available_devices:
                self.logger.error(f"设备 {device_id} 不可用")
                return False

            device = self.available_devices[device_id]

            # 构建按键命令
            if device.ip_address in ["localhost", "127.0.0.1"]:
                # 本地设备
                cmd = ['xdotool', 'key', key]
            else:
                # 远程设备
                cmd = ['ssh', f'user@{device.ip_address}', f'xdotool key {key}']

            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                # 记录按键事件
                key_event = {
                    "type": "key_press",
                    "key": key,
                    "timestamp": datetime.now().isoformat(),
                    "device_id": device_id
                }

                # 添加到活跃会话
                for session in self.active_sessions.values():
                    if session.target_device == device_id and session.is_active:
                        session.input_events.append(key_event)

                # 发送信号
                self.input_synchronized.emit(device_id, key_event)

                self.logger.info(f"成功向设备 {device.device_name} 发送按键: {key}")
                return True
            else:
                self.logger.error(f"发送按键失败: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"发送按键失败: {str(e)}")
            return False

    def on_device_discovered(self, device_info: DeviceInfo):
        """设备发现事件处理"""
        self.available_devices[device_info.device_id] = device_info

        # 为新设备创建监控器
        if (self.get_config("auto_monitor", True) and
            device_info.device_id not in self.keyboard_monitors):
            monitor = KeyboardMonitor(device_info.device_id, device_info.ip_address)
            monitor.keyboard_state_changed.connect(self.on_keyboard_state_changed)
            monitor.input_event_detected.connect(self.on_input_event_detected)

            self.keyboard_monitors[device_info.device_id] = monitor
            monitor.start_monitoring()

        self.logger.info(f"发现新设备: {device_info.device_name}")

    def on_device_updated(self, device_info: DeviceInfo):
        """设备更新事件处理"""
        self.available_devices[device_info.device_id] = device_info

    def on_device_offline(self, device_id: str):
        """设备离线事件处理"""
        if device_id in self.available_devices:
            del self.available_devices[device_id]

        # 停止监控器
        if device_id in self.keyboard_monitors:
            self.keyboard_monitors[device_id].stop_monitoring()
            del self.keyboard_monitors[device_id]

        # 结束相关的键盘会话
        sessions_to_end = []
        for session_id, session in self.active_sessions.items():
            if session.target_device == device_id:
                sessions_to_end.append(session_id)

        for session_id in sessions_to_end:
            self.end_keyboard_session(session_id)

    def on_keyboard_state_changed(self, device_id: str, is_visible: bool):
        """键盘状态改变事件处理"""
        self.keyboard_toggled.emit(device_id, is_visible)

        device_name = "未知设备"
        if device_id in self.available_devices:
            device_name = self.available_devices[device_id].device_name

        status = "显示" if is_visible else "隐藏"
        self.logger.info(f"设备 {device_name} 的虚拟键盘状态: {status}")

    def on_input_event_detected(self, device_id: str, event_data: Dict[str, Any]):
        """输入事件检测处理"""
        self.input_synchronized.emit(device_id, event_data)

        # 如果启用了输入日志
        if self.get_config("enable_input_logging", False):
            self.logger.info(f"检测到设备 {device_id} 的输入事件: {event_data}")

    def get_available_devices(self) -> List[DeviceInfo]:
        """获取可用设备列表"""
        return [device for device in self.available_devices.values()
                if device.status == "online"]

    def get_keyboard_status(self) -> Dict[str, Any]:
        """获取键盘状态"""
        return {
            "available_devices": len(self.available_devices),
            "active_sessions": len(self.active_sessions),
            "monitoring_devices": len(self.keyboard_monitors),
            "sessions": [session.to_dict() for session in self.active_sessions.values()]
        }

    def send_keyboard_status(self, requester: str):
        """发送键盘状态"""
        status = self.get_keyboard_status()
        self.send_message(requester, "keyboard_status", status)

class VirtualKeyboardWindow(QWidget):
    """虚拟键盘控制窗口"""

    def __init__(self, module: 'VirtualKeyboardModule'):
        super().__init__()
        self.module = module
        self.setWindowTitle("虚拟键盘控制中心")
        self.setGeometry(200, 200, 900, 600)
        self.setWindowIcon(QIcon(os.path.join(os.path.dirname(__file__), '..', 'assets', 'icons', 'keyboard.png')))

        self._init_ui()
        self._connect_signals()
        self.update_device_list()
        self.update_session_list()

    def _init_ui(self):
        """初始化UI界面"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 左侧面板：设备列表和控制
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        device_group = QGroupBox("设备列表与状态")
        device_layout = QVBoxLayout(device_group)
        self.device_table = QTableWidget()
        self.device_table.setColumnCount(4)
        self.device_table.setHorizontalHeaderLabels(["设备名称", "状态", "IP地址", "键盘可见"])
        self.device_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.device_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.device_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.device_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.refresh_button = QPushButton("刷新列表")
        device_layout.addWidget(self.device_table)
        device_layout.addWidget(self.refresh_button)
        left_layout.addWidget(device_group)

        control_group = QGroupBox("远程控制")
        control_layout = QGridLayout(control_group)
        self.toggle_keyboard_button = QPushButton("显示/隐藏键盘")
        self.start_session_button = QPushButton("开始输入会话")
        control_layout.addWidget(self.toggle_keyboard_button, 0, 0)
        control_layout.addWidget(self.start_session_button, 0, 1)
        left_layout.addWidget(control_group)

        # 右侧面板：输入同步和会话管理
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        input_group = QGroupBox("输入同步 (需开启会话)")
        self.input_group_widget = QWidget()
        input_layout = QVBoxLayout(self.input_group_widget)
        self.input_line_edit = QLineEdit()
        self.input_line_edit.setPlaceholderText("在此输入文本，按回车发送")
        self.send_text_button = QPushButton("发送文本")
        input_h_layout = QHBoxLayout()
        input_h_layout.addWidget(self.input_line_edit)
        input_h_layout.addWidget(self.send_text_button)
        input_layout.addLayout(input_h_layout)
        
        keys_layout = QGridLayout()
        keys_to_add = ['Return', 'BackSpace', 'Escape', 'Tab', 'Control_L', 'Alt_L', 'Shift_L', 'space']
        for i, key in enumerate(keys_to_add):
            btn = QPushButton(key)
            btn.clicked.connect(lambda ch, k=key: self.send_key(k))
            keys_layout.addWidget(btn, i // 4, i % 4)
        input_layout.addLayout(keys_layout)
        input_group.setLayout(input_layout)
        right_layout.addWidget(input_group)

        session_group = QGroupBox("当前会话")
        session_layout = QVBoxLayout(session_group)
        self.session_list = QListWidget()
        self.end_session_button = QPushButton("结束选中会话")
        session_layout.addWidget(self.session_list)
        session_layout.addWidget(self.end_session_button)
        right_layout.addWidget(session_group)

        main_layout.addWidget(left_panel, 2)
        main_layout.addWidget(right_panel, 1)

        self.input_group_widget.setEnabled(False) # 默认禁用

    def _connect_signals(self):
        """连接信号和槽"""
        self.refresh_button.clicked.connect(self.update_device_list)
        self.device_table.itemSelectionChanged.connect(self.on_device_selection_changed)
        
        self.toggle_keyboard_button.clicked.connect(self.toggle_keyboard)
        self.start_session_button.clicked.connect(self.start_session)
        
        self.send_text_button.clicked.connect(self.send_text)
        self.input_line_edit.returnPressed.connect(self.send_text)
        
        self.end_session_button.clicked.connect(self.end_session)
        self.session_list.itemSelectionChanged.connect(self.on_session_selection_changed)

        # Module signals
        self.module.keyboard_session_started.connect(self.on_session_started)
        self.module.keyboard_session_ended.connect(self.on_session_ended)
        self.module.keyboard_toggled.connect(self.on_keyboard_toggled)

    @pyqtSlot()
    def update_device_list(self):
        """更新设备列表"""
        self.device_table.setRowCount(0)
        devices = self.module.get_available_devices()
        for device in devices:
            row_pos = self.device_table.rowCount()
            self.device_table.insertRow(row_pos)
            self.device_table.setItem(row_pos, 0, QTableWidgetItem(device.device_name))
            self.device_table.setItem(row_pos, 1, QTableWidgetItem(device.status))
            self.device_table.setItem(row_pos, 2, QTableWidgetItem(device.ip_address))
            self.device_table.setItem(row_pos, 3, QTableWidgetItem("未知"))
            self.device_table.item(row_pos, 0).setData(Qt.UserRole, device.device_id)
        self.on_device_selection_changed()

    @pyqtSlot()
    def update_session_list(self):
        """更新会话列表"""
        self.session_list.clear()
        for session_id, session in self.module.active_sessions.items():
            target_name = self.module.available_devices.get(session.target_device, DeviceInfo("", "未知", "", "")).device_name
            item = QListWidgetItem(f"会话: {session.source_device} -> {target_name}")
            item.setData(Qt.UserRole, session_id)
            self.session_list.addItem(item)
        self.on_session_selection_changed()

    def get_selected_device_id(self) -> Optional[str]:
        """获取当前选中的设备ID"""
        selected_items = self.device_table.selectedItems()
        if selected_items:
            return selected_items[0].data(Qt.UserRole)
        return None

    @pyqtSlot()
    def on_device_selection_changed(self):
        """设备选择变化时更新UI"""
        device_id = self.get_selected_device_id()
        is_selected = device_id is not None
        self.toggle_keyboard_button.setEnabled(is_selected)
        self.start_session_button.setEnabled(is_selected)

    @pyqtSlot()
    def on_session_selection_changed(self):
        """会话选择变化时更新UI"""
        is_selected = self.session_list.currentItem() is not None
        self.end_session_button.setEnabled(is_selected)
        self.input_group_widget.setEnabled(is_selected)

    @pyqtSlot()
    def toggle_keyboard(self):
        device_id = self.get_selected_device_id()
        if device_id:
            self.module.toggle_virtual_keyboard(device_id)

    @pyqtSlot()
    def start_session(self):
        device_id = self.get_selected_device_id()
        if device_id:
            self.module.start_keyboard_session(device_id)

    @pyqtSlot()
    def end_session(self):
        current_item = self.session_list.currentItem()
        if current_item:
            session_id = current_item.data(Qt.UserRole)
            self.module.end_keyboard_session(session_id)

    @pyqtSlot()
    def send_text(self):
        text = self.input_line_edit.text()
        if not text: return
        current_session_item = self.session_list.currentItem()
        if not current_session_item: return
        
        session_id = current_session_item.data(Qt.UserRole)
        session = self.module.active_sessions.get(session_id)
        if session:
            self.module.send_input_to_device(session.target_device, text)
            self.input_line_edit.clear()

    @pyqtSlot(str)
    def send_key(self, key: str):
        current_session_item = self.session_list.currentItem()
        if not current_session_item: return
        
        session_id = current_session_item.data(Qt.UserRole)
        session = self.module.active_sessions.get(session_id)
        if session:
            self.module.send_key_to_device(session.target_device, key)

    @pyqtSlot(object)
    def on_session_started(self, session: KeyboardSession):
        self.update_session_list()

    @pyqtSlot(str)
    def on_session_ended(self, session_id: str):
        self.update_session_list()

    @pyqtSlot(str, bool)
    def on_keyboard_toggled(self, device_id: str, is_visible: bool):
        for row in range(self.device_table.rowCount()):
            if self.device_table.item(row, 0).data(Qt.UserRole) == device_id:
                status = "可见" if is_visible else "隐藏"
                color = QColor("green") if is_visible else QColor("red")
                item = QTableWidgetItem(status)
                item.setForeground(color)
                self.device_table.setItem(row, 3, item)
                break

    def closeEvent(self, event):
        """关闭窗口事件"""
        self.hide()
        event.ignore()
