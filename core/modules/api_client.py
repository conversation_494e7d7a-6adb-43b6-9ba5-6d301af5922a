import requests
import json
import os

class ApiClient:
    def __init__(self, base_url):
        self.base_url = base_url
        self.token = None
        # 构造证书的绝对路径并验证
        self.cert_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..' ,'..' ,'smart_classroom_web', 'cert.pem'))

    def login(self, email, password):
        """登录到API并获取token"""
        try:
            print(f"[信息] 正在尝试登录到 {self.base_url}/api/auth/login")
            response = requests.post(
                f'{self.base_url}/api/auth/login', 
                json={'email': email, 'password': password},
                verify=self.cert_path,  # 使用证书路径进行验证
                timeout=10  # 设置10秒超时
            )
            response.raise_for_status()
            data = response.json()
            self.token = data.get('token')
            print("[信息] 登录成功.")
            return True, data.get('user')
        except requests.exceptions.SSLError as e:
            print(f"[错误] SSL连接失败: {e}")
            return False, f"SSL验证失败: {e}"
        except requests.exceptions.ConnectionError as e:
            print(f"[错误] 无法连接到服务器: {e}")
            return False, f"无法连接到服务器: {e}"
        except requests.exceptions.Timeout:
            print(f"[错误] 请求超时。位于 {self.base_url} 的服务器没有响应。")
            return False, "请求超时"
        except requests.exceptions.RequestException as e:
            print(f"[错误] 发生了一个未知的请求错误: {e}")
            return False, f"请求发生未知错误: {e}"

    def _get_auth_headers(self):
        """获取包含认证token的请求头"""
        if not self.token:
            raise Exception("请先登录")
        return {
            'Authorization': f'Bearer {self.token}'
        }

    def get(self, endpoint, **kwargs):
        """发送GET请求"""
        headers = self._get_auth_headers()
        if 'headers' in kwargs:
            headers.update(kwargs.pop('headers'))
        
        try:
            response = requests.get(f'{self.base_url}{endpoint}', headers=headers, verify=self.cert_path, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"API GET请求失败: {e}")
            return None

    def post(self, endpoint, **kwargs):
        """发送POST请求"""
        headers = self._get_auth_headers()
        if 'headers' in kwargs:
            headers.update(kwargs.pop('headers'))

        try:
            response = requests.post(f'{self.base_url}{endpoint}', headers=headers, verify=self.cert_path, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"API POST请求失败: {e}")
            return None

# 实例化一个全局客户端
api_client = ApiClient(base_url="https://192.168.0.11:5000")
