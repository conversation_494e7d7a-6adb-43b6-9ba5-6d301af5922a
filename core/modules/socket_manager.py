from socketio.client import Client
import json
from PyQt5.QtCore import QObject, pyqtSignal, QTimer

class SocketManager(QObject):
    message_received = pyqtSignal(str, dict)  # 房间名, 消息数据
    connected = pyqtSignal()
    disconnected = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self, server_url, course_id=None, teacher_info=None):
        super().__init__()
        self.server_url = server_url
        self.course_id = course_id
        self.teacher_info = teacher_info
        self.sio = Client()
        self._register_events()
        self.reconnect_timer = QTimer(self)
        self.reconnect_timer.setInterval(5000)  # 5秒重连一次
        self.reconnect_timer.timeout.connect(self.connect_to_server)

    def _register_events(self):
        @self.sio.event
        def connect():
            print("SocketIO connected!")
            self.connected.emit()
            # 添加一个短暂延迟，确保连接完全建立后再加入房间
            import time
            time.sleep(1)  # 延迟1秒
            self._post_connect_actions()
            # 可以在这里发送认证信息
            if self.teacher_info:
                self.sio.emit('authenticate', {'user_type': 'teacher', 'user_id': self.teacher_info.get('id')})

    def _post_connect_actions(self):
        if self.course_id:
            self.join_room(f"course_{self.course_id}")

        @self.sio.event
        def disconnect():
            print("SocketIO disconnected!")
            self.disconnected.emit()
            self.reconnect_timer.start() # 启动重连定时器

        @self.sio.event
        def connect_error(data):
            print(f"SocketIO connection error: {data}")
            self.error_occurred.emit(str(data))
            self.reconnect_timer.start() # 启动重连定时器

        @self.sio.event
        def status(data):
            print(f"Status message from server: {data.get('msg')}")

        @self.sio.event
        def chat_message(data):
            room = data.get('room', 'default_room') # 假设消息中包含room信息
            self.message_received.emit(room, data)
            print(f"Received chat message for room {room}: {data}")

        # 注册其他可能需要的事件
        # @self.sio.on('some_custom_event')
        # def on_some_custom_event(data):
        #     print(f"Received custom event: {data}")
        #     self.message_received.emit('custom', data)

    def connect_to_server(self):
        if not self.sio.connected:
            try:
                print(f"Attempting to connect to SocketIO server at {self.server_url}...")
                self.sio.connect(self.server_url, transports=['websocket'])
                self.reconnect_timer.stop() # 连接成功，停止重连定时器
            except Exception as e:
                print(f"Failed to connect to SocketIO server: {e}")
                self.error_occurred.emit(f"连接失败: {e}")
                if not self.reconnect_timer.isActive():
                    self.reconnect_timer.start() # 确保定时器在连接失败时启动

    def disconnect_from_server(self):
        if self.sio.connected:
            self.sio.disconnect()
        self.reconnect_timer.stop() # 停止重连定时器

    def send_message(self, event_name, data, room=None):
        if self.sio.connected:
            if room:
                data['room'] = room # 将房间信息添加到数据中
            self.sio.emit(event_name, data)
        else:
            print("SocketIO not connected, cannot send message.")
            self.error_occurred.emit("SocketIO未连接，无法发送消息。")

    def join_room(self, room_name):
        if self.sio.connected:
            self.sio.emit('join', {'room': room_name})
            print(f"Attempting to join room: {room_name}")
        else:
            print("SocketIO not connected, cannot join room.")

    def leave_room(self, room_name):
        if self.sio.connected:
            self.sio.emit('leave', {'room': room_name})
            print(f"Attempting to leave room: {room_name}")
        else:
            print("SocketIO not connected, cannot leave room.")

    def is_connected(self):
        return self.sio.connected

if __name__ == '__main__':
    # 这是一个简单的测试用例
    from PyQt5.QtWidgets import QApplication
    import sys
    import time

    app = QApplication(sys.argv)
    
    # 假设后端运行在 http://localhost:8080
    # 实际项目中，这个URL应该从配置中读取
    test_server_url = "http://localhost:8080" 
    test_course_id = "test_course_123"
    test_teacher_info = {'id': 'T001', 'name': '测试教师'}

    socket_manager = SocketManager(test_server_url, test_course_id, test_teacher_info)

    def on_connected():
        print("Test: SocketManager connected!")
        socket_manager.send_message('send_message', {'message': 'Hello from client!'}, room=f"course_{test_course_id}")

    def on_message_received(room, data):
        print(f"Test: Message received in room {room}: {data}")

    def on_disconnected():
        print("Test: SocketManager disconnected!")

    def on_error(msg):
        print(f"Test: SocketManager error: {msg}")

    socket_manager.connected.connect(on_connected)
    socket_manager.message_received.connect(on_message_received)
    socket_manager.disconnected.connect(on_disconnected)
    socket_manager.error_occurred.connect(on_error)

    socket_manager.connect_to_server()

    # 运行一段时间，观察连接和消息
    # 在实际应用中，这将由PyQt事件循环管理
    QTimer.singleShot(10000, socket_manager.disconnect_from_server) # 10秒后断开连接
    QTimer.singleShot(15000, app.quit) # 15秒后退出应用

    sys.exit(app.exec_())
