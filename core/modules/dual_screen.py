"""
主副屏模式功能模块 - 教师专用功能
教师端设备主副屏模式功能，支持双屏检测和配置，实现主副屏不同内容显示
支持课件上下页联动控制，实现一屏课件一屏白板模式
"""
import sys
import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QListWidget, QListWidgetItem,
                            QGroupBox, QTextEdit, QCheckBox, QSpinBox,
                            QMessageBox, QProgressBar, QFrame, QGridLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QDialog, QDialogButtonBox, QInputDialog, QDesktopWidget,
                            QApplication, QMainWindow, QSplitter)
from PyQt5.QtCore import QTimer, pyqtSignal, Qt, QThread, pyqtSlot, QObject, QRect
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPixmap, QPainter, QScreen
from .base_module import TeacherModule
import logging

class ScreenInfo:
    """屏幕信息类"""
    
    def __init__(self, screen_id: int, geometry: QRect, is_primary: bool = False):
        self.screen_id = screen_id
        self.geometry = geometry
        self.is_primary = is_primary
        self.width = geometry.width()
        self.height = geometry.height()
        self.x = geometry.x()
        self.y = geometry.y()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "screen_id": self.screen_id,
            "width": self.width,
            "height": self.height,
            "x": self.x,
            "y": self.y,
            "is_primary": self.is_primary
        }

class DualScreenConfig:
    """双屏配置类"""
    
    def __init__(self):
        self.primary_screen_id = 0
        self.secondary_screen_id = 1
        self.primary_content = "courseware"  # courseware, whiteboard
        self.secondary_content = "whiteboard"  # courseware, whiteboard
        self.sync_navigation = True  # 是否同步课件翻页
        self.auto_switch = False  # 是否自动切换内容
        self.layout_mode = "extended"  # extended, mirrored, single
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "primary_screen_id": self.primary_screen_id,
            "secondary_screen_id": self.secondary_screen_id,
            "primary_content": self.primary_content,
            "secondary_content": self.secondary_content,
            "sync_navigation": self.sync_navigation,
            "auto_switch": self.auto_switch,
            "layout_mode": self.layout_mode
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DualScreenConfig':
        """从字典创建配置"""
        config = cls()
        config.primary_screen_id = data.get("primary_screen_id", 0)
        config.secondary_screen_id = data.get("secondary_screen_id", 1)
        config.primary_content = data.get("primary_content", "courseware")
        config.secondary_content = data.get("secondary_content", "whiteboard")
        config.sync_navigation = data.get("sync_navigation", True)
        config.auto_switch = data.get("auto_switch", False)
        config.layout_mode = data.get("layout_mode", "extended")
        return config

class CoursewareWindow(QMainWindow):
    """课件显示窗口"""
    
    page_changed = pyqtSignal(int)  # 页面改变信号
    
    def __init__(self, screen_geometry: QRect):
        super().__init__()
        self.screen_geometry = screen_geometry
        self.current_page = 1
        self.total_pages = 10  # 示例总页数
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("课件显示")
        self.setGeometry(self.screen_geometry)
        self.setWindowFlags(Qt.FramelessWindowHint)
        
        # 主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # 课件内容区域
        self.content_label = QLabel(f"课件内容 - 第 {self.current_page} 页")
        self.content_label.setAlignment(Qt.AlignCenter)
        self.content_label.setStyleSheet("""
            QLabel {
                background-color: white;
                border: 2px solid #007acc;
                border-radius: 8px;
                font-size: 24px;
                font-weight: bold;
                color: #007acc;
            }
        """)
        layout.addWidget(self.content_label)
        
        # 控制按钮区域
        control_layout = QHBoxLayout()
        
        self.prev_btn = QPushButton("上一页")
        self.prev_btn.clicked.connect(self.prev_page)
        control_layout.addWidget(self.prev_btn)
        
        self.page_label = QLabel(f"{self.current_page} / {self.total_pages}")
        self.page_label.setAlignment(Qt.AlignCenter)
        control_layout.addWidget(self.page_label)
        
        self.next_btn = QPushButton("下一页")
        self.next_btn.clicked.connect(self.next_page)
        control_layout.addWidget(self.next_btn)
        
        layout.addLayout(control_layout)
        
        central_widget.setLayout(layout)
        
        # 设置样式
        self.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        
        self.update_buttons()
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.update_content()
            self.page_changed.emit(self.current_page)
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.update_content()
            self.page_changed.emit(self.current_page)
    
    def goto_page(self, page: int):
        """跳转到指定页面"""
        if 1 <= page <= self.total_pages:
            self.current_page = page
            self.update_content()
    
    def update_content(self):
        """更新内容显示"""
        self.content_label.setText(f"课件内容 - 第 {self.current_page} 页")
        self.page_label.setText(f"{self.current_page} / {self.total_pages}")
        self.update_buttons()
    
    def update_buttons(self):
        """更新按钮状态"""
        self.prev_btn.setEnabled(self.current_page > 1)
        self.next_btn.setEnabled(self.current_page < self.total_pages)

class WhiteboardWindow(QMainWindow):
    """白板显示窗口"""
    
    def __init__(self, screen_geometry: QRect):
        super().__init__()
        self.screen_geometry = screen_geometry
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("白板")
        self.setGeometry(self.screen_geometry)
        self.setWindowFlags(Qt.FramelessWindowHint)
        
        # 主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # 白板内容区域
        self.whiteboard_label = QLabel("白板区域\n\n点击此处开始书写")
        self.whiteboard_label.setAlignment(Qt.AlignCenter)
        self.whiteboard_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px solid #28a745;
                border-radius: 8px;
                font-size: 18px;
                color: #28a745;
            }
        """)
        layout.addWidget(self.whiteboard_label)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.pen_btn = QPushButton("画笔")
        self.pen_btn.setCheckable(True)
        self.pen_btn.setChecked(True)
        toolbar_layout.addWidget(self.pen_btn)
        
        self.eraser_btn = QPushButton("橡皮")
        self.eraser_btn.setCheckable(True)
        toolbar_layout.addWidget(self.eraser_btn)
        
        self.clear_btn = QPushButton("清空")
        self.clear_btn.clicked.connect(self.clear_whiteboard)
        toolbar_layout.addWidget(self.clear_btn)
        
        toolbar_layout.addStretch()
        
        layout.addLayout(toolbar_layout)
        
        central_widget.setLayout(layout)
        
        # 设置样式
        self.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:checked {
                background-color: #1e7e34;
            }
        """)
    
    def clear_whiteboard(self):
        """清空白板"""
        self.whiteboard_label.setText("白板区域\n\n点击此处开始书写")

class DualScreenModule(TeacherModule):
    """主副屏模式功能模块"""
    
    # 模块信号
    screen_config_changed = pyqtSignal(object)  # DualScreenConfig
    page_navigation = pyqtSignal(int)           # 页面导航
    content_switched = pyqtSignal(str, str)     # 内容切换 (screen, content)
    
    def __init__(self):
        super().__init__(
            module_name="dual_screen",
            display_name="主副屏模式",
            version="1.0.0"
        )
        
        # 屏幕相关
        self.available_screens: List[ScreenInfo] = []
        self.dual_screen_config = DualScreenConfig()
        self.is_dual_screen_mode = False
        
        # 窗口
        self.control_window = None
        self.courseware_windows: Dict[int, CoursewareWindow] = {}
        self.whiteboard_windows: Dict[int, WhiteboardWindow] = {}
        
        # 配置
        self.config.update({
            "auto_detect_screens": True,        # 自动检测屏幕
            "remember_config": True,            # 记住配置
            "sync_navigation": True,            # 同步导航
            "enable_hotkeys": True,             # 启用热键
            "auto_fullscreen": True             # 自动全屏
        })
    
    def get_widget(self):
        return self.control_window

    def _initialize_module(self) -> bool:
        """初始化主副屏模式模块"""
        try:
            # 检查认证状态
            if not self.is_authenticated():
                self.logger.error("主副屏模式模块需要教师认证")
                return False
            
            # 检测可用屏幕
            self.detect_screens()
            
            # 创建控制界面
            self.control_window = DualScreenControlWindow(self)
            
            # 加载配置
            self.load_screen_config()
            
            self.logger.info("主副屏模式模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"主副屏模式模块初始化失败: {str(e)}")
            return False

    def _cleanup_module(self):
        """清理主副屏模式模块资源"""
        try:
            # 保存配置
            if self.get_config("remember_config", True):
                self.save_screen_config()

            # 关闭所有窗口
            self.close_all_windows()

            # 关闭控制界面
            if self.control_window:
                self.control_window.close()
                self.control_window = None

            # 清理数据
            self.available_screens.clear()
            self.courseware_windows.clear()
            self.whiteboard_windows.clear()

            self.logger.info("主副屏模式模块资源清理完成")

        except Exception as e:
            self.logger.error(f"清理主副屏模式模块资源时出错: {str(e)}")

    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "enable_dual_screen":
            self.enable_dual_screen_mode()

        elif message_type == "disable_dual_screen":
            self.disable_dual_screen_mode()

        elif message_type == "switch_content":
            screen_id = data.get("screen_id")
            content_type = data.get("content_type")
            if screen_id is not None and content_type:
                self.switch_screen_content(screen_id, content_type)

        elif message_type == "navigate_page":
            page = data.get("page")
            if page:
                self.navigate_to_page(page)

        elif message_type == "get_screen_status":
            self.send_screen_status(sender)

        elif message_type == "show_dual_screen":
            self.show()

    def show(self):
        """显示主副屏控制窗口"""
        if self.control_window:
            self.control_window.show()
            self.control_window.raise_()
            self.control_window.activateWindow()

    def hide(self):
        """隐藏主副屏控制窗口"""
        if self.control_window:
            self.control_window.hide()

    def close(self):
        """关闭主副屏模式模块"""
        self.cleanup()

    def detect_screens(self):
        """检测可用屏幕"""
        try:
            self.available_screens.clear()

            app = QApplication.instance()
            if not app:
                self.logger.error("无法获取QApplication实例")
                return

            desktop = app.desktop()
            screen_count = desktop.screenCount()

            for i in range(screen_count):
                geometry = desktop.screenGeometry(i)
                is_primary = (i == desktop.primaryScreen())

                screen_info = ScreenInfo(i, geometry, is_primary)
                self.available_screens.append(screen_info)

                self.logger.info(f"检测到屏幕 {i}: {geometry.width()}x{geometry.height()} "
                               f"位置({geometry.x()}, {geometry.y()}) "
                               f"{'主屏' if is_primary else '副屏'}")

            self.logger.info(f"共检测到 {len(self.available_screens)} 个屏幕")

        except Exception as e:
            self.logger.error(f"检测屏幕失败: {str(e)}")

    def enable_dual_screen_mode(self) -> bool:
        """启用双屏模式"""
        try:
            if len(self.available_screens) < 2:
                self.logger.error("需要至少2个屏幕才能启用双屏模式")
                return False

            if self.is_dual_screen_mode:
                self.logger.info("双屏模式已经启用")
                return True

            # 创建主屏内容窗口
            primary_screen = self.available_screens[self.dual_screen_config.primary_screen_id]
            if self.dual_screen_config.primary_content == "courseware":
                self.create_courseware_window(primary_screen.screen_id, primary_screen.geometry)
            else:
                self.create_whiteboard_window(primary_screen.screen_id, primary_screen.geometry)

            # 创建副屏内容窗口
            secondary_screen = self.available_screens[self.dual_screen_config.secondary_screen_id]
            if self.dual_screen_config.secondary_content == "courseware":
                self.create_courseware_window(secondary_screen.screen_id, secondary_screen.geometry)
            else:
                self.create_whiteboard_window(secondary_screen.screen_id, secondary_screen.geometry)

            self.is_dual_screen_mode = True

            # 发送信号
            self.screen_config_changed.emit(self.dual_screen_config)

            # 通知其他模块
            self.broadcast_message("dual_screen_enabled", {
                "config": self.dual_screen_config.to_dict(),
                "timestamp": datetime.now().isoformat()
            })

            self.logger.info("双屏模式已启用")
            return True

        except Exception as e:
            self.logger.error(f"启用双屏模式失败: {str(e)}")
            return False

    def disable_dual_screen_mode(self):
        """禁用双屏模式"""
        try:
            if not self.is_dual_screen_mode:
                self.logger.info("双屏模式未启用")
                return

            # 关闭所有内容窗口
            self.close_all_windows()

            self.is_dual_screen_mode = False

            # 通知其他模块
            self.broadcast_message("dual_screen_disabled", {
                "timestamp": datetime.now().isoformat()
            })

            self.logger.info("双屏模式已禁用")

        except Exception as e:
            self.logger.error(f"禁用双屏模式失败: {str(e)}")

    def create_courseware_window(self, screen_id: int, geometry: QRect):
        """创建课件窗口"""
        try:
            if screen_id in self.courseware_windows:
                self.courseware_windows[screen_id].close()

            courseware_window = CoursewareWindow(geometry)
            courseware_window.page_changed.connect(self.on_page_changed)
            courseware_window.show()

            self.courseware_windows[screen_id] = courseware_window

            self.logger.info(f"在屏幕 {screen_id} 创建课件窗口")

        except Exception as e:
            self.logger.error(f"创建课件窗口失败: {str(e)}")

    def create_whiteboard_window(self, screen_id: int, geometry: QRect):
        """创建白板窗口"""
        try:
            if screen_id in self.whiteboard_windows:
                self.whiteboard_windows[screen_id].close()

            whiteboard_window = WhiteboardWindow(geometry)
            whiteboard_window.show()

            self.whiteboard_windows[screen_id] = whiteboard_window

            self.logger.info(f"在屏幕 {screen_id} 创建白板窗口")

        except Exception as e:
            self.logger.error(f"创建白板窗口失败: {str(e)}")

    def close_all_windows(self):
        """关闭所有窗口"""
        try:
            # 关闭课件窗口
            for window in self.courseware_windows.values():
                window.close()
            self.courseware_windows.clear()

            # 关闭白板窗口
            for window in self.whiteboard_windows.values():
                window.close()
            self.whiteboard_windows.clear()

        except Exception as e:
            self.logger.error(f"关闭窗口失败: {str(e)}")

    def switch_screen_content(self, screen_id: int, content_type: str):
        """切换屏幕内容"""
        try:
            if screen_id >= len(self.available_screens):
                self.logger.error(f"屏幕ID {screen_id} 无效")
                return

            screen_geometry = self.available_screens[screen_id].geometry

            # 关闭当前屏幕的窗口
            if screen_id in self.courseware_windows:
                self.courseware_windows[screen_id].close()
                del self.courseware_windows[screen_id]

            if screen_id in self.whiteboard_windows:
                self.whiteboard_windows[screen_id].close()
                del self.whiteboard_windows[screen_id]

            # 创建新的内容窗口
            if content_type == "courseware":
                self.create_courseware_window(screen_id, screen_geometry)
            elif content_type == "whiteboard":
                self.create_whiteboard_window(screen_id, screen_geometry)

            # 更新配置
            if screen_id == self.dual_screen_config.primary_screen_id:
                self.dual_screen_config.primary_content = content_type
            elif screen_id == self.dual_screen_config.secondary_screen_id:
                self.dual_screen_config.secondary_content = content_type

            # 发送信号
            self.content_switched.emit(str(screen_id), content_type)

            self.logger.info(f"屏幕 {screen_id} 内容已切换为 {content_type}")

        except Exception as e:
            self.logger.error(f"切换屏幕内容失败: {str(e)}")

    def navigate_to_page(self, page: int):
        """导航到指定页面"""
        try:
            # 更新所有课件窗口的页面
            for window in self.courseware_windows.values():
                window.goto_page(page)

            # 发送信号
            self.page_navigation.emit(page)

            self.logger.info(f"导航到第 {page} 页")

        except Exception as e:
            self.logger.error(f"页面导航失败: {str(e)}")

    def on_page_changed(self, page: int):
        """页面改变事件处理"""
        try:
            # 如果启用了同步导航，同步所有课件窗口
            if self.dual_screen_config.sync_navigation:
                for window in self.courseware_windows.values():
                    if window.current_page != page:
                        window.goto_page(page)

            # 发送信号
            self.page_navigation.emit(page)

        except Exception as e:
            self.logger.error(f"处理页面改变事件失败: {str(e)}")

    def get_screen_status(self) -> Dict[str, Any]:
        """获取屏幕状态"""
        return {
            "available_screens": len(self.available_screens),
            "is_dual_screen_mode": self.is_dual_screen_mode,
            "config": self.dual_screen_config.to_dict(),
            "active_windows": {
                "courseware": len(self.courseware_windows),
                "whiteboard": len(self.whiteboard_windows)
            }
        }

    def send_screen_status(self, requester: str):
        """发送屏幕状态"""
        status = self.get_screen_status()
        self.send_message(requester, "screen_status", status)

    def load_screen_config(self):
        """加载屏幕配置"""
        try:
            import os
            config_file = os.path.join(os.path.dirname(__file__), "..", "database", "dual_screen_config.json")

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                self.dual_screen_config = DualScreenConfig.from_dict(config_data)
                self.logger.info("屏幕配置加载成功")

        except Exception as e:
            self.logger.error(f"加载屏幕配置失败: {str(e)}")

    def save_screen_config(self):
        """保存屏幕配置"""
        try:
            import os
            config_file = os.path.join(os.path.dirname(__file__), "..", "database", "dual_screen_config.json")
            os.makedirs(os.path.dirname(config_file), exist_ok=True)

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.dual_screen_config.to_dict(), f, ensure_ascii=False, indent=2)

            self.logger.info("屏幕配置保存成功")

        except Exception as e:
            self.logger.error(f"保存屏幕配置失败: {str(e)}")

class DualScreenControlWindow(QWidget):
    """主副屏控制窗口"""

    def __init__(self, module: 'DualScreenModule'):
        super().__init__()
        self.module = module
        self.setWindowTitle("主副屏模式控制")
        self.setGeometry(200, 200, 800, 500)
        self.setWindowIcon(QIcon(os.path.join(os.path.dirname(__file__), '..', 'assets', 'icons', 'dual_screen.png')))

        self._init_ui()
        self._connect_signals()
        self.update_ui_from_config()

    def _init_ui(self):
        """初始化UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(12)

        # 屏幕检测信息
        detection_group = QGroupBox("屏幕检测")
        detection_layout = QHBoxLayout(detection_group)
        self.screen_info_label = QLabel(f"检测到 {len(self.module.available_screens)} 个屏幕")
        self.refresh_screens_button = QPushButton("重新检测")
        detection_layout.addWidget(self.screen_info_label)
        detection_layout.addStretch()
        detection_layout.addWidget(self.refresh_screens_button)
        main_layout.addWidget(detection_group)

        # 主配置区
        config_group = QGroupBox("双屏配置")
        config_layout = QGridLayout(config_group)

        config_layout.addWidget(QLabel("主屏幕:"), 0, 0)
        self.primary_screen_combo = QComboBox()
        config_layout.addWidget(self.primary_screen_combo, 0, 1)

        config_layout.addWidget(QLabel("主屏内容:"), 0, 2)
        self.primary_content_combo = QComboBox()
        self.primary_content_combo.addItems(["课件", "白板"])
        config_layout.addWidget(self.primary_content_combo, 0, 3)

        config_layout.addWidget(QLabel("副屏幕:"), 1, 0)
        self.secondary_screen_combo = QComboBox()
        config_layout.addWidget(self.secondary_screen_combo, 1, 1)

        config_layout.addWidget(QLabel("副屏内容:"), 1, 2)
        self.secondary_content_combo = QComboBox()
        self.secondary_content_combo.addItems(["白板", "课件"])
        config_layout.addWidget(self.secondary_content_combo, 1, 3)
        
        self.swap_button = QPushButton("交换内容")
        config_layout.addWidget(self.swap_button, 0, 4, 2, 1, Qt.AlignCenter)

        main_layout.addWidget(config_group)

        # 选项设置
        options_group = QGroupBox("功能选项")
        options_layout = QHBoxLayout(options_group)
        self.sync_nav_check = QCheckBox("同步课件翻页")
        self.auto_fullscreen_check = QCheckBox("自动全屏显示")
        self.remember_config_check = QCheckBox("记住当前配置")
        options_layout.addWidget(self.sync_nav_check)
        options_layout.addWidget(self.auto_fullscreen_check)
        options_layout.addWidget(self.remember_config_check)
        options_layout.addStretch()
        main_layout.addWidget(options_group)

        # 控制按钮
        control_layout = QHBoxLayout()
        self.apply_button = QPushButton("应用配置")
        self.toggle_mode_button = QPushButton("启用双屏模式")
        self.toggle_mode_button.setCheckable(True)
        control_layout.addStretch()
        control_layout.addWidget(self.apply_button)
        control_layout.addWidget(self.toggle_mode_button)
        control_layout.addStretch()
        main_layout.addLayout(control_layout)

        self._populate_screen_combos()
        self._apply_styles()

    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #ccc;
                border-radius: 5px;
                margin-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
            }
            QPushButton {
                padding: 8px 15px;
                border-radius: 4px;
                font-size: 14px;
            }
            #toggle_mode_button[checked="true"] {
                background-color: #dc3545;
                border: 1px solid #dc3545;
                color: white;
            }
            #toggle_mode_button[checked="false"] {
                background-color: #28a745;
                border: 1px solid #28a745;
                color: white;
            }
        """)
        self.apply_button.setStyleSheet("background-color: #007bff; color: white; border: 1px solid #007bff;")

    def _connect_signals(self):
        """连接信号和槽"""
        self.refresh_screens_button.clicked.connect(self.refresh_screens)
        self.apply_button.clicked.connect(self.apply_config)
        self.toggle_mode_button.clicked.connect(self.toggle_dual_screen_mode)
        self.swap_button.clicked.connect(self.swap_content)
        self.module.screen_config_changed.connect(self.update_ui_from_config)

    def _populate_screen_combos(self):
        """填充屏幕选择下拉框"""
        self.primary_screen_combo.clear()
        self.secondary_screen_combo.clear()
        for screen in self.module.available_screens:
            screen_text = f"屏幕 {screen.screen_id} ({screen.width}x{screen.height})"
            if screen.is_primary:
                screen_text += " [主]"
            self.primary_screen_combo.addItem(screen_text, screen.screen_id)
            self.secondary_screen_combo.addItem(screen_text, screen.screen_id)

    @pyqtSlot()
    def update_ui_from_config(self):
        """根据模块配置更新UI"""
        config = self.module.dual_screen_config
        self.primary_screen_combo.setCurrentIndex(self.primary_screen_combo.findData(config.primary_screen_id))
        self.secondary_screen_combo.setCurrentIndex(self.secondary_screen_combo.findData(config.secondary_screen_id))
        self.primary_content_combo.setCurrentText("课件" if config.primary_content == "courseware" else "白板")
        self.secondary_content_combo.setCurrentText("白板" if config.secondary_content == "whiteboard" else "课件")

        self.sync_nav_check.setChecked(config.sync_navigation)
        self.auto_fullscreen_check.setChecked(self.module.get_config("auto_fullscreen", True))
        self.remember_config_check.setChecked(self.module.get_config("remember_config", True))

        self.toggle_mode_button.setChecked(self.module.is_dual_screen_mode)
        self.toggle_mode_button.setText("禁用双屏模式" if self.module.is_dual_screen_mode else "启用双屏模式")

    @pyqtSlot()
    def refresh_screens(self):
        """刷新屏幕列表"""
        self.module.detect_screens()
        self.screen_info_label.setText(f"检测到 {len(self.module.available_screens)} 个屏幕")
        self._populate_screen_combos()
        self.update_ui_from_config()
        QMessageBox.information(self, "刷新成功", f"已重新检测屏幕，共发现 {len(self.module.available_screens)} 个屏幕。")

    @pyqtSlot()
    def apply_config(self):
        """应用当前UI配置到模块"""
        primary_id = self.primary_screen_combo.currentData()
        secondary_id = self.secondary_screen_combo.currentData()

        if primary_id == secondary_id:
            QMessageBox.warning(self, "配置错误", "主屏幕和副屏幕不能是同一个显示器。")
            return

        config = self.module.dual_screen_config
        config.primary_screen_id = primary_id
        config.secondary_screen_id = secondary_id
        config.primary_content = "courseware" if self.primary_content_combo.currentText() == "课件" else "whiteboard"
        config.secondary_content = "whiteboard" if self.secondary_content_combo.currentText() == "白板" else "courseware"
        config.sync_navigation = self.sync_nav_check.isChecked()
        
        self.module.set_config("auto_fullscreen", self.auto_fullscreen_check.isChecked())
        self.module.set_config("remember_config", self.remember_config_check.isChecked())

        self.module.screen_config_changed.emit(config)
        QMessageBox.information(self, "应用成功", "配置已应用。如果双屏模式已启用，将立即生效。")

        # 如果模式已开启，则重新应用
        if self.module.is_dual_screen_mode:
            self.module.disable_dual_screen_mode()
            self.module.enable_dual_screen_mode()

    @pyqtSlot(bool)
    def toggle_dual_screen_mode(self, checked):
        """切换双屏模式的启用/禁用状态"""
        if checked:
            if not self.module.enable_dual_screen_mode():
                QMessageBox.critical(self, "启动失败", "无法启用双屏模式，请检查屏幕数量和配置。")
                self.toggle_mode_button.setChecked(False)
        else:
            self.module.disable_dual_screen_mode()
        
        self.update_ui_from_config()

    @pyqtSlot()
    def swap_content(self):
        """交换主副屏内容"""
        primary_idx = self.primary_content_combo.currentIndex()
        secondary_idx = self.secondary_content_combo.currentIndex()
        self.primary_content_combo.setCurrentIndex(secondary_idx)
        self.secondary_content_combo.setCurrentIndex(primary_idx)
        self.apply_config()

    def closeEvent(self, event):
        """关闭窗口事件"""
        self.hide()
        event.ignore()
