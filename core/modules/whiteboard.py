import time  
import subprocess  
import os  
import sys  
import platform
from datetime import datetime  
from PyQt5.QtWidgets import *  
from PyQt5.QtGui import *  
from PyQt5.QtCore import *  
from modules.sys_css import MyCss
from .ppt_control import PptControl
import qtawesome as fa

class Whiteboard(QMainWindow):
    def __init__(self):
        super().__init__()
        # 获取屏幕尺寸（高DPI适配）
        screen = QApplication.primaryScreen()
        self.screen_rect = screen.availableGeometry()
        self.screen_width = self.screen_rect.width()
        self.screen_height = self.screen_rect.height()
        # 录制变量
        self.is_recording = False  # 初始化录制状态
        self.recording_process = None  # 初始化录制进程
        self.output_file = None  # 初始化输出文件路径
        self.timer = QTimer()  # 初始化定时器
        self.timer.timeout.connect(self.update_timer)  # 连接定时器信号
        self.ppt = PptControl()  # PPT控制
        # 初始化变量
        self.shapes = []  # 存储绘制的形状
        self.drawing = False  # 绘图状态
        self.pen_color = QColor(255, 0, 0)  # 默认画笔颜色为红色
        self.current_shape = "freehand"  # 当前绘制的形状
        self.fine = 5  # 线条粗细
        self.start_point = QPoint()  # 起始点
        self.end_point = QPoint()  # 结束点
        self.last_point = QPoint()  # 初始化 last_point
        self.isMenuExpanded = True  # 菜单状态，初始为展开状态

        self.eraser_points = []  # 初始化擦除路径
        self.eraser_size = 20  # 橡皮擦大小
        self.spotlight_radius = 100  # 聚光灯半径
        self.magnifier_radius = 100  # 放大镜半径

        self.pixmap = QPixmap(self.size())  # 用于保存绘制内容
        self.pixmap.fill(Qt.transparent)  # 设置透明背景
        
        # 设置窗口属性
        self.setWindowFlags(Qt.FramelessWindowHint |Qt.Tool | self.windowFlags())
        self.setAttribute(Qt.WA_TranslucentBackground)
     
        # 主绘图层
        self.widget_main = QWidget(self)
        self.widget_main.setGeometry(0, 0, self.screen_width, self.screen_height)
        self.widget_main.setStyleSheet("background-color: rgba(0, 0, 0, 2)")
  
        # 底部标注菜单
        self.Draw_Menu = QWidget(self)
        self.Draw_Menu.setGeometry(
            (self.screen_width - 800) // 2, self.screen_height - 140, 550, 200)
        self.horizontal_layout = QHBoxLayout(self.Draw_Menu)

        # 菜单伸缩按钮
        self.menu_pushButton = QPushButton( "", self.Draw_Menu)
        self.menu_pushButton.setFixedSize(50, 50)
        self.menu_pushButton.clicked.connect(self.toggleMenu)
        self.menu_pushButton.setStyleSheet(MyCss.menuIconCss)
        self.horizontal_layout.addWidget(self.menu_pushButton)

        # 画笔按钮
        self.plotting_pushButton = QPushButton("", self.Draw_Menu)
        self.plotting_pushButton.setFixedSize(50, 50)
        self.plotting_pushButton.clicked.connect(self.show_pen_menu)
        self.plotting_pushButton.setStyleSheet(MyCss.plottingIconCss)
        self.horizontal_layout.addWidget(self.plotting_pushButton)

        # 画笔菜单
        self.pen_menu = QMenu(self.Draw_Menu)
        self.pen_menu.addAction("选择颜色", self.openColorDialog)

        self.width_menu = QMenu("选择笔粗细", self.pen_menu)
        for i in range(1, 11):
            self.width_menu.addAction(f"笔宽 {i}", lambda w=i: self.set_fine(w))
        self.pen_menu.addMenu(self.width_menu)

        self.shape_menu = QMenu("形状选择", self.pen_menu)
        self.shape_menu.addAction("手绘", lambda: self.set_shape("freehand"))
        self.shape_menu.addAction("直线", lambda: self.set_shape("line"))
        self.shape_menu.addAction("矩形", lambda: self.set_shape("rectangle"))
        self.shape_menu.addAction("圆形", lambda: self.set_shape("circle"))
        self.shape_menu.addAction("三角形", lambda: self.set_shape("triangle"))
        self.pen_menu.addMenu(self.shape_menu)

        # 聚光灯按钮
        self.spotlight_pushButton = QPushButton("", self.Draw_Menu)
        self.spotlight_pushButton.setFixedSize(50, 50)
        self.spotlight_pushButton.clicked.connect(
            lambda: self.set_shape("spotlight"))
        self.spotlight_pushButton.setStyleSheet(MyCss.spotlightCss)
        self.horizontal_layout.addWidget(self.spotlight_pushButton)

        # 放大镜按钮
        self.magnify_pushButton = QPushButton("", self.Draw_Menu)
        self.magnify_pushButton.setFixedSize(50, 50)
        self.magnify_pushButton.clicked.connect(
            lambda: self.set_shape("magnifier"))
        self.magnify_pushButton.setStyleSheet(MyCss.MagnifyCss)
        self.horizontal_layout.addWidget(self.magnify_pushButton)

        # 擦除按钮
        self.eraser_pushButton = QPushButton("", self.Draw_Menu)
        self.eraser_pushButton.setFixedSize(50, 50)
        self.eraser_pushButton.clicked.connect(
            lambda: self.set_shape("eraser"))
        self.eraser_pushButton.setStyleSheet(MyCss.erasureIconCss)
        self.horizontal_layout.addWidget(self.eraser_pushButton)

        # 撤销按钮
        self.revoke_pushButton = QPushButton("", self.Draw_Menu)
        self.revoke_pushButton.setFixedSize(50, 50)
        self.revoke_pushButton.clicked.connect(self.undoLastDrawing)
        self.revoke_pushButton.setStyleSheet(MyCss.revokeIconCss)
        self.horizontal_layout.addWidget(self.revoke_pushButton)

        # 清屏按钮
        self.Clear_pushButton = QPushButton("", self.Draw_Menu)
        self.Clear_pushButton.setFixedSize(50, 50)
        self.Clear_pushButton.clicked.connect(self.erasureClick)
        self.Clear_pushButton.setStyleSheet(MyCss.ClearIconCss)
        self.horizontal_layout.addWidget(self.Clear_pushButton)
        
        # 截屏按钮
        self.capture_screen_pushButton = QPushButton("", self.Draw_Menu)
        self.capture_screen_pushButton.setFixedSize(50, 50)
        self.capture_screen_pushButton.setStyleSheet(MyCss. ScreenshotCss)
        self.capture_screen_pushButton.clicked.connect(self.capture_screen)
        self.horizontal_layout.addWidget(self.capture_screen_pushButton)

        # 录屏按钮
        self.record_screen_pushButton = QPushButton("", self.Draw_Menu)
        self.record_screen_pushButton.setFixedSize(50, 50)
        self.record_screen_pushButton.setStyleSheet(MyCss.recordCss)
        self.record_screen_pushButton.clicked.connect(self.record_screen)
        self.horizontal_layout.addWidget(self.record_screen_pushButton)

        # 绘制窗体关闭
        self.ppt_pushButton_close = QPushButton("", self.Draw_Menu)
        self.ppt_pushButton_close.setFixedSize(50, 50)
        self.ppt_pushButton_close.clicked.connect(self.exit_Whiteboard)
        self.ppt_pushButton_close.setStyleSheet(MyCss.chCss)
        self.horizontal_layout.addWidget(self.ppt_pushButton_close)

        # ############################底部PPT菜单##############################
        self.PPT_Menu = QWidget(self)
        self.PPT_Menu.setGeometry(
            (self.screen_width - 250), self.screen_height - 140, 250, 200)
        self.horizontal_layout_ppt = QHBoxLayout(self.PPT_Menu)
        # PPT控制按钮
        self.ppt_pushButton_start = QPushButton("", self.PPT_Menu)
        self.ppt_pushButton_start.setFixedSize(50, 50)
        self.ppt_pushButton_start.setStyleSheet(MyCss.pptCss)
        self.ppt_pushButton_start.clicked.connect(self.start_ppt)
        self.horizontal_layout_ppt.addWidget(self.ppt_pushButton_start)

        self.ppt_pushButton_next = QPushButton("", self.PPT_Menu)
        self.ppt_pushButton_next.setFixedSize(50, 50)
        self.ppt_pushButton_next.setStyleSheet(MyCss.ppt_rightCss)
        self.ppt_pushButton_next.clicked.connect(self.next_slide)
        self.horizontal_layout_ppt.addWidget(self.ppt_pushButton_next)

        self.ppt_pushButton_prev = QPushButton("", self.PPT_Menu)
        self.ppt_pushButton_prev.setFixedSize(50, 50)
        self.ppt_pushButton_prev.setStyleSheet(MyCss.ppt_leftCss)
        self.ppt_pushButton_prev.clicked.connect(self.prev_slide)
        self.horizontal_layout_ppt.addWidget(self.ppt_pushButton_prev)

        self.ppt_pushButton_stop = QPushButton("", self.PPT_Menu)
        self.ppt_pushButton_stop.setFixedSize(50, 50)
        self.ppt_pushButton_stop.setStyleSheet(MyCss.ppt_EndCss)
        self.ppt_pushButton_stop.clicked.connect(self.stop_ppt)
        self.horizontal_layout_ppt.addWidget(self.ppt_pushButton_stop)
        # ——————————————————————————————————————————————————————————————————————————

    def toggleMenu(self):
        """切换菜单状态（优化穿透版）"""
        # 切换其他按钮的可见性
        for i in range(1, self.horizontal_layout.count()):
            widget = self.horizontal_layout.itemAt(i).widget()
            widget.setVisible(not widget.isVisible())

        if self.isMenuExpanded:
            # 收缩状态：关闭透明层和主图层
            self.widget_main.hide()  # 隐藏主图层
            
            self.setAttribute(Qt.WA_TransparentForMouseEvents, True)  # 设置穿透
            
            # 确保收缩按钮保持可点击
            self.menu_pushButton.setAttribute(Qt.WA_TransparentForMouseEvents, False)
            self.menu_pushButton.raise_()
            
            self.erasureClick()
        else:
            # 展开状态：恢复透明层和主图层
            self.widget_main.show()
            self.widget_main.setStyleSheet("background-color: rgba(0, 0, 0, 2)")
            self.setAttribute(Qt.WA_TransparentForMouseEvents, False)
        
        # 保持窗口置顶
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.show()
        self.isMenuExpanded = not self.isMenuExpanded

    def show_pen_menu(self):
        """显示画笔菜单"""
        pos = self.plotting_pushButton.mapToGlobal(
            self.plotting_pushButton.rect().topLeft())
        pos.setY(pos.y() - self.pen_menu.sizeHint().height())
        self.pen_menu.exec_(pos)

    def set_fine(self, fine):
        """设置画笔粗细"""
        self.fine = fine

    def openColorDialog(self):
        """打开颜色选择对话框"""
        color = QColorDialog.getColor(self.pen_color, self, "选择颜色")
        if color.isValid():
            self.pen_color = color

    def set_shape(self, shape):
        """设置当前绘制的形状"""
        self.current_shape = shape

    def erasureClick(self):
        """清空所有绘制内容"""
        self.shapes.clear()
        self.pixmap.fill(Qt.transparent)  # 清空 QPixmap
        self.update()

    def undoLastDrawing(self):
        """撤销最后一次绘制"""
        if self.shapes:
            self.shapes.pop()
            self.pixmap.fill(Qt.transparent)  # 清空 QPixmap
            # 重新绘制所有形状
            painter = QPainter(self.pixmap)
            for shape in self.shapes:
                self.draw_shape(painter, shape)
            self.update()

    def draw_shape(self, painter, shape):
        """绘制单个形状"""
        pen = QPen(shape["color"], shape["fine"], Qt.SolidLine, Qt.RoundCap)
        painter.setPen(pen)

        if shape["shape"] == "line":
            painter.drawLine(shape["start"], shape["end"])
        elif shape["shape"] == "rectangle":
            rect = QRect(shape["start"], shape["end"])
            painter.drawRect(rect)
        elif shape["shape"] == "triangle":
            polygon = QPolygon(shape["points"])
            painter.drawPolygon(polygon)
        elif shape["shape"] == "circle":
            radius = int((shape["end"] - shape["start"]).manhattanLength() / 2)
            center = shape["start"]
            painter.drawEllipse(center, radius, radius)
        elif shape["shape"] == "freehand":
            for i in range(1, len(shape["points"])):
                painter.drawLine(shape["points"][i - 1], shape["points"][i])

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.drawing = True
            self.start_point = event.pos()
            if self.current_shape == "freehand":
                self.points = [event.pos()]
            elif self.current_shape == "eraser":
                self.eraser_points = [event.pos()]  # 初始化擦除路径

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() & Qt.LeftButton and self.drawing:
            self.end_point = event.pos()
            if self.current_shape == "freehand":
                self.points.append(event.pos())
            elif self.current_shape == "eraser":
                # 在 QPixmap 上擦除
                painter = QPainter(self.pixmap)
                painter.setCompositionMode(
                    QPainter.CompositionMode_Clear)  # 使用透明模式擦除
                painter.setPen(
                    QPen(Qt.transparent, self.eraser_size, Qt.SolidLine, Qt.RoundCap))
                painter.drawLine(self.last_point, event.pos())
                self.last_point = event.pos()
            elif self.current_shape == "spotlight":
                self.last_point = event.pos()  # 更新聚光灯位置
            elif self.current_shape == "magnifier":
                self.last_point = event.pos()  # 更新放大镜位置
            self.update()  # 触发 paintEvent

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.drawing:
            self.drawing = False
            shape = None

            if self.current_shape == "line":
                shape = {
                    "shape": self.current_shape,
                    "start": self.start_point,
                    "end": self.end_point,
                    "color": self.pen_color,
                    "fine": self.fine
                }
            elif self.current_shape == "rectangle":
                shape = {
                    "shape": self.current_shape,
                    "start": self.start_point,
                    "end": self.end_point,
                    "color": self.pen_color,
                    "fine": self.fine
                }
            elif self.current_shape == "triangle":
                mid_x = (self.start_point.x() + self.end_point.x()) / 2
                side_length = abs(self.end_point.x() - self.start_point.x())
                height = int((3 ** 0.5 / 2) * side_length)
                points = [
                    QPoint(self.start_point.x(), self.end_point.y()),
                    QPoint(self.end_point.x(), self.end_point.y()),
                    QPoint(int(mid_x), self.end_point.y() - height)
                ]
                shape = {
                    "shape": self.current_shape,
                    "points": points,
                    "color": self.pen_color,
                    "fine": self.fine
                }
            elif self.current_shape == "circle":
                shape = {
                    "shape": self.current_shape,
                    "start": self.start_point,
                    "end": self.end_point,
                    "color": self.pen_color,
                    "fine": self.fine
                }
            elif self.current_shape == "freehand":
                shape = {
                    "shape": self.current_shape,
                    "points": self.points[:],
                    "color": self.pen_color,
                    "fine": self.fine
                }

            if shape:
                self.shapes.append(shape)
                # 将形状绘制到 QPixmap 中
                painter = QPainter(self.pixmap)
                self.draw_shape(painter, shape)
            self.update()

    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.drawPixmap(0, 0, self.pixmap)

        # 绘制当前实时预览形状
        if self.drawing:
            pen = QPen(self.pen_color, self.fine, Qt.SolidLine, Qt.RoundCap)
            painter.setPen(pen)

            if self.current_shape == "line":
                painter.drawLine(self.start_point, self.end_point)
            elif self.current_shape == "rectangle":
                rect = QRect(self.start_point, self.end_point)
                painter.drawRect(rect)
            elif self.current_shape == "triangle":
                mid_x = (self.start_point.x() + self.end_point.x()) / 2
                side_length = abs(self.end_point.x() - self.start_point.x())
                height = int((3 ** 0.5 / 2) * side_length)
                points = [
                    QPoint(self.start_point.x(), self.end_point.y()),
                    QPoint(self.end_point.x(), self.end_point.y()),
                    QPoint(int(mid_x), self.end_point.y() - height)
                ]
                painter.drawPolygon(QPolygon(points))
            elif self.current_shape == "circle":
                radius = int(
                    (self.end_point - self.start_point).manhattanLength() / 2)
                center = self.start_point
                painter.drawEllipse(center, radius, radius)
            elif self.current_shape == "freehand" and hasattr(self, 'points') and len(self.points) > 1:
                for i in range(1, len(self.points)):
                    painter.drawLine(self.points[i - 1], self.points[i])

            # 聚光灯效果
            elif self.current_shape == "spotlight":
                gradient = QRadialGradient(
                    self.last_point, self.spotlight_radius)
                gradient.setColorAt(0, QColor(255, 255, 255, 150))  # 中心区域半透明
                gradient.setColorAt(1, QColor(0, 0, 0, 150))  # 边缘区域半透明
                painter.setBrush(QBrush(gradient))
                painter.setPen(Qt.NoPen)
                painter.drawEllipse(
                    self.last_point, self.spotlight_radius, self.spotlight_radius)

            # 放大镜效果
            elif self.current_shape == "magnifier":
                magnified_rect = QRect(self.last_point.x() - self.magnifier_radius,
                                       self.last_point.y() - self.magnifier_radius,
                                       self.magnifier_radius * 2,
                                       self.magnifier_radius * 2)
                magnified_pixmap = self.pixmap.copy(
                    magnified_rect).scaled(magnified_rect.size() * 2)
                painter.drawPixmap(magnified_rect.topLeft(), magnified_pixmap)
                painter.setPen(QPen(Qt.black, 2))
                painter.drawEllipse(
                    self.last_point, self.magnifier_radius, self.magnifier_radius)

    def resizeEvent(self, event):
        """窗口大小调整事件"""
        new_pixmap = QPixmap(self.size())
        new_pixmap.fill(Qt.transparent)
        painter = QPainter(new_pixmap)
        painter.drawPixmap(0, 0, self.pixmap)
        self.pixmap = new_pixmap
    # ************************截屏函数***********************************

    def capture_screen(self):
        """Linux 兼容的截屏（使用 scrot 或 maim）"""
        try:
            screenshot_folder = os.path.join(os.getcwd(), "record/img")
            if not os.path.exists(screenshot_folder):
                os.makedirs(screenshot_folder)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(screenshot_folder, f"{timestamp}.png")

            # 方法 1: 使用 scrot（需安装）
            subprocess.run(["scrot", filename])

            # 方法 2: 使用 maim（更现代）
            # subprocess.run(["maim", "-u", filename])

            QMessageBox.information(self, "截屏成功", f"截图已保存为: {filename}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"截屏失败: {str(e)}")
    # ************************录屏函数***********************************

    def record_screen(self):
        if not self.is_recording:
            self.start_recording()
            self.record_screen_pushButton.setStyleSheet(MyCss.butBCss)
        else:
            self.stop_recording()
            self.record_screen_pushButton.setStyleSheet(MyCss.recordCss)

    def start_recording(self):
        """跨平台录屏（兼容 Windows 和 Linux）"""
        try:
            output_dir = os.path.join(os.getcwd(), "record")
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.output_file = os.path.join(output_dir, f"{timestamp}.flv")

            # 根据操作系统选择 FFmpeg 参数
            if platform.system() == "Windows":
                command = [
                    'ffmpeg',
                    '-f', 'gdigrab',
                    '-framerate', '30',
                    '-i', 'desktop',
                    '-c:v', 'libx264',
                    '-preset', 'fast',
                    self.output_file
                ]
                kwargs = {"creationflags": subprocess.CREATE_NO_WINDOW}
            else:  # Linux
                command = [
                    'ffmpeg',
                    '-f', 'x11grab',
                    '-framerate', '30',
                    '-i', ':0.0',
                    '-c:v', 'libx264',
                    '-preset', 'fast',
                    self.output_file
                ]
                kwargs = {}

            self.recording_process = subprocess.Popen(
                command,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                **kwargs
            )

            self.is_recording = True
            self.start_time = time.time()
            self.timer.start(1000)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"录制启动失败: {str(e)}")

    def stop_recording(self):
        """停止录制"""
        try:
            if self.recording_process:
                self.recording_process.terminate()  # 终止 FFmpeg 进程
                self.recording_process.wait()  # 等待进程结束
                self.recording_process = None

            self.is_recording = False

            # 停止定时器
            self.timer.stop()

            # 重置按钮文本
            self.record_screen_pushButton.setText("")

            # 提示录制完成
            QMessageBox.information(
                self, "提示", f"录制已停止，文件保存为: {self.output_file}")

        except subprocess.SubprocessError as e:
            QMessageBox.critical(self, "错误", f"停止录制失败: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"未知错误: {str(e)}")

    def update_timer(self):
        """定时器触发时更新录制时间"""
        elapsed_time = int(time.time() - self.start_time)
        minutes = elapsed_time // 60
        seconds = elapsed_time % 60
        time_str = f"{minutes:02}:{seconds:02}"  # 格式化为 MM:SS
        self.record_screen_pushButton.setText(time_str)  # 更新按钮文本

    # ************************重写PPT控制***********************************
    def activate_ppt_window(self):
        """在 Linux 上激活 PPT 窗口（兼容 WPS 或 LibreOffice）"""
        try:
            # 查找 PPT 窗口（假设标题包含 "WPS Presentation" 或 "LibreOffice Impress"）
            # import subprocess
            result = subprocess.run(
                ["wmctrl", "-l"], 
                capture_output=True, 
                text=True
            )
            windows = result.stdout.splitlines()
            ppt_windows = [w for w in windows if "WPS Presentation" in w or "LibreOffice Impress" in w]

            if ppt_windows:
                window_id = ppt_windows[0].split()[0]  # 提取窗口 ID
                subprocess.run(["wmctrl", "-i", "-a", window_id])  # 激活窗口
                subprocess.run(["xdotool", "windowfocus", window_id])  # 确保焦点
            else:
                QMessageBox.warning(self, "警告", "未检测到 PPT 窗口")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"激活窗口失败: {str(e)}")

    def start_ppt(self):
        """开始放映 PPT（Linux 兼容）"""
        self.activate_ppt_window()
        subprocess.run(["xdotool", "key", "F5"])  # 代替 pyautogui.press('f5')

    def prev_slide(self):
        """上一页（Linux 兼容）"""
        self.activate_ppt_window()
        subprocess.run(["xdotool", "key", "Left"])  # 代替 pyautogui.press('left')

    def next_slide(self):
        """下一页（Linux 兼容）"""
        self.activate_ppt_window()
        subprocess.run(["xdotool", "key", "Right"])  # 代替 pyautogui.press('right')

    def stop_ppt(self):
        """结束放映（Linux 兼容）"""
        self.activate_ppt_window()
        subprocess.run(["xdotool", "key", "Escape"])  # 代替 pyautogui.press('esc')

    def exit_Whiteboard(self):
        """关闭当前实例（窗口）"""
        self.close()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = Whiteboard()
    window.showFullScreen()
    sys.exit(app.exec_())
