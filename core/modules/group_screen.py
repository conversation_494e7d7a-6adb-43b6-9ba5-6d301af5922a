import sys
import vlc

from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QGridLayout, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPalette, QColor

class VideoPlayer(QWidget):
    """一个接收VLC实例并播放视频的QWidget"""
    def __init__(self, vlc_instance, master=None):
        super(VideoPlayer, self).__init__(master)
        if vlc_instance is None:
            raise ValueError("VLC Instance must be provided")
            
        self.mediaplayer = vlc_instance.media_player_new()

        palette = self.palette()
        palette.setColor(QPalette.Window, QColor(0, 0, 0))
        self.setPalette(palette)
        self.setAutoFillBackground(True)

    def play(self, url):
        """播放指定的URL"""
        media = self.mediaplayer.get_instance().media_new(url)
        # 这些选项也可以在这里设置，并且可能更有效
        media.add_option(':network-caching=500')
        media.add_option(':realrtsp-caching=100')
        self.mediaplayer.set_media(media)

        if sys.platform.startswith('linux'):
            self.mediaplayer.set_xwindow(int(self.winId()))
        elif sys.platform == "win32":
            self.mediaplayer.set_hwnd(int(self.winId()))
        elif sys.platform == "darwin":
            self.mediaplayer.set_nsobject(int(self.winId()))
        
        self.mediaplayer.play()

    def stop(self):
        self.mediaplayer.stop()

class Groupscreen(QMainWindow):
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("多屏互动 (VLC)")
        self.setGeometry(600, 200, 800, 600)
        
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        main_layout = QVBoxLayout(self.central_widget)
        
        self.grid_layout = QGridLayout()
        self.grid_layout.setSpacing(3)
        main_layout.addLayout(self.grid_layout, 1)
        
        controls_container = QWidget()
        controls_layout = QHBoxLayout(controls_container)
        self.setup_controls(controls_layout)
        main_layout.addWidget(controls_container)

        # 创建一个共享VLC实例
        vlc_options = [
            '--avcodec-hw=vaapi', # 使用 VAAPI 硬件加速，更适合 Intel 显卡
            '--network-caching=1000', # 增加到1000ms
        ]
        self.vlc_instance = vlc.Instance(" ".join(vlc_options))

        self.rtsp_urls = [f"rtsp://192.168.0.11:8554/stream{i:02d}" for i in range(1, 10)]
        self.players = []
        for i in range(9):
            # 将共享实例传递给每个播放器
            player = VideoPlayer(self.vlc_instance, self)
            self.players.append(player)
            player.setVisible(False)
            # 初始放置，set_layout会重新安排
            self.grid_layout.addWidget(player, i // 3, i % 3)

        self.set_layout(4)

    def get_widget(self):
        return self

    def setup_controls(self, layout):
        buttons_info = [
            ("1", 1), ("2", 2),
            ("4", 4), ("6", 6), ("9", 9)
        ]
        for text, num in buttons_info:
            btn = QPushButton(text)
            btn.clicked.connect(lambda _, n=num: self.set_layout(n))
            layout.addWidget(btn)

    def set_layout(self, num_screens):
        # 停止并隐藏所有播放器
        for player in self.players:
            if player.isVisible():
                player.stop()
                player.setVisible(False)
        
        # 清空网格布局
        while self.grid_layout.count():
            child = self.grid_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)
        
        # 重置列拉伸
        for i in range(self.grid_layout.columnCount()):
            self.grid_layout.setColumnStretch(i, 0)

        # 定义布局
        layouts = {
            1: [(0, 0, 1, 1)],
            2: [(0, 0, 1, 1), (0, 1, 1, 1)],
            4: [(0, 0, 1, 1), (0, 1, 1, 1), (1, 0, 1, 1), (1, 1, 1, 1)],
            6: [(0, 0, 2, 2), (0, 2, 1, 1), (1, 2, 1, 1), (2, 0, 1, 1), (2, 1, 1, 1), (2, 2, 1, 1)],
            9: [(i // 3, i % 3, 1, 1) for i in range(9)]
        }
        
        current_layout = layouts.get(num_screens, [])
        for i, (row, col, rowspan, colspan) in enumerate(current_layout):
            if i < len(self.players):
                self.grid_layout.addWidget(self.players[i], row, col, rowspan, colspan)

        if num_screens == 3 or num_screens == 6:
            self.grid_layout.setColumnStretch(0, 2)
            self.grid_layout.setColumnStretch(1, 2)
            self.grid_layout.setColumnStretch(2, 1)

        # 启动并显示需要的播放器
        for i in range(min(num_screens, len(self.players))):
            self.players[i].setVisible(True)
            self.players[i].play(self.rtsp_urls[i])
    
    def closeEvent(self, event):
        for player in self.players:
            player.stop()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = Groupscreen()
    window.show()
    sys.exit(app.exec_())
