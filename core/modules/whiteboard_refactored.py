"""
重构后的白板模块 - 基于新的模块化架构
优化性能和用户体验，支持内容保存和加载
"""
import time
import subprocess
import os
import sys
import platform
import json
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
from modules.sys_css import MyCss
from .ppt_control import PptControl
from .base_module import CommonModule
from typing import Dict, Any, List, Optional
try:
    import qtawesome as fa
except ImportError:
    fa = None

class WhiteboardModule(CommonModule):
    """重构后的白板模块"""

    def __init__(self):
        super().__init__(
            module_name="whiteboard",
            display_name="电子白板",
            version="2.0.0"
        )

        # 白板相关属性
        self.whiteboard_window = None
        self.shapes = []  # 存储所有绘制的形状
        self.undo_stack = []  # 撤销栈
        self.redo_stack = []  # 重做栈
        self.drawing = False
        self.pen_color = QColor(255, 0, 0)
        self.current_shape = "freehand"
        self.fine = 5
        self.start_point = QPoint()
        self.end_point = QPoint()
        self.last_point = QPoint()
        self.isMenuExpanded = True
        self.eraser_points = []
        self.eraser_size = 20
        self.spotlight_radius = 100
        self.magnifier_radius = 100

        # 性能优化相关
        self.drawing_cache = QPixmap()  # 绘图缓存
        self.cache_dirty = True  # 缓存是否需要更新
        self.batch_update_timer = QTimer()  # 批量更新定时器
        self.batch_update_timer.setSingleShot(True)
        self.batch_update_timer.timeout.connect(self._update_cache)

        # 录制相关属性
        self.is_recording = False
        self.recording_process = None
        self.output_file = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_timer)
        self.start_time = 0

        # PPT控制
        self.ppt = None

        # 文件保存相关
        self.save_directory = "whiteboard_saves"
        self.auto_save_enabled = True
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self._auto_save)
        self.auto_save_interval = 30000  # 30秒自动保存

        # 协作相关
        self.collaboration_enabled = False
        self.collaboration_target = None
    
    def get_widget(self):
        return self.whiteboard_window

    def _initialize_module(self) -> bool:
        """初始化白板模块"""
        try:
            # 创建保存目录
            if not os.path.exists(self.save_directory):
                os.makedirs(self.save_directory)

            # 创建录制目录
            record_dir = os.path.join(os.getcwd(), "record")
            if not os.path.exists(record_dir):
                os.makedirs(record_dir)

            # 初始化PPT控制
            self.ppt = PptControl()

            # 创建白板窗口
            self.whiteboard_window = WhiteboardWindow(self)

            # 设置模块配置
            self.config.update({
                "default_pen_color": "#FF0000",
                "default_pen_size": 5,
                "enable_recording": True,
                "auto_save": True,
                "auto_save_interval": 30000,
                "max_undo_steps": 50,
                "enable_performance_mode": True,
                "enable_collaboration": False
            })

            # 启动自动保存
            if self.config.get("auto_save", True):
                self.auto_save_timer.start(self.config.get("auto_save_interval", 30000))

            self.logger.info("白板模块初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"白板模块初始化失败: {str(e)}")
            return False
    
    def _cleanup_module(self):
        """清理白板模块资源"""
        try:
            # 停止录制
            if self.is_recording:
                self.stop_recording()

            # 停止自动保存
            if self.auto_save_timer.isActive():
                self.auto_save_timer.stop()

            # 停止批量更新定时器
            if self.batch_update_timer.isActive():
                self.batch_update_timer.stop()

            # 关闭白板窗口
            if self.whiteboard_window:
                self.whiteboard_window.close()
                self.whiteboard_window = None

            # 停止定时器
            if self.timer.isActive():
                self.timer.stop()

            self.logger.info("白板模块资源清理完成")

        except Exception as e:
            self.logger.error(f"清理白板模块资源时出错: {str(e)}")

    def _auto_save(self):
        """自动保存白板内容"""
        if self.whiteboard_window and self.shapes:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(self.save_directory, f"auto_save_{timestamp}.json")
            self.save_board(filename)
            self.logger.info(f"自动保存完成: {filename}")

    def _update_cache(self):
        """更新绘图缓存"""
        if self.cache_dirty and self.whiteboard_window:
            # 重新绘制缓存
            self.cache_dirty = False
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "ppt_control":
            self.handle_ppt_control(data)
        elif message_type == "drawing_sync":
            self.handle_drawing_sync(data)
        elif message_type == "clear_board":
            self.clear_board()
        elif message_type == "save_board":
            self.save_board(data.get("filename"))
        elif message_type == "load_board":
            self.load_board(data.get("filename"))
    
    def show(self):
        """显示白板"""
        if self.whiteboard_window:
            self.whiteboard_window.show()
            
            # 通知其他模块白板已显示
            self.broadcast_message("whiteboard_shown", {
                "timestamp": datetime.now().isoformat()
            })
    
    def hide(self):
        """隐藏白板"""
        if self.whiteboard_window:
            self.whiteboard_window.hide()
            
            # 通知其他模块白板已隐藏
            self.broadcast_message("whiteboard_hidden", {
                "timestamp": datetime.now().isoformat()
            })
    
    def close(self):
        """关闭白板"""
        self.cleanup()
    
    def handle_ppt_control(self, data: Dict[str, Any]):
        """处理PPT控制消息"""
        action = data.get("action")
        if action == "next_slide" and self.ppt:
            self.ppt.next_slide()
        elif action == "prev_slide" and self.ppt:
            self.ppt.prev_slide()
        elif action == "start_slideshow" and self.ppt:
            self.ppt.start_slideshow()
        elif action == "end_slideshow" and self.ppt:
            self.ppt.end_slideshow()
    
    def handle_drawing_sync(self, data: Dict[str, Any]):
        """处理绘图同步消息"""
        if self.whiteboard_window:
            self.whiteboard_window.sync_drawing(data)
    
    def clear_board(self):
        """清空白板"""
        if self.whiteboard_window:
            self.whiteboard_window.clear_all()
            
            # 通知其他模块白板已清空
            self.broadcast_message("board_cleared", {
                "timestamp": datetime.now().isoformat()
            })
    
    def save_board(self, filename: str = None):
        """保存白板内容"""
        if self.whiteboard_window:
            saved_file = self.whiteboard_window.save_content(filename)
            if saved_file:
                # 通知其他模块白板已保存
                self.broadcast_message("board_saved", {
                    "filename": saved_file,
                    "timestamp": datetime.now().isoformat()
                })
                return saved_file
        return None
    
    def load_board(self, filename: str):
        """加载白板内容"""
        if self.whiteboard_window and filename:
            if self.whiteboard_window.load_content(filename):
                # 通知其他模块白板已加载
                self.broadcast_message("board_loaded", {
                    "filename": filename,
                    "timestamp": datetime.now().isoformat()
                })
                return True
        return False
    
    def start_recording(self):
        """开始录制"""
        if not self.is_recording:
            try:
                output_dir = os.path.join(os.getcwd(), "record")
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                self.output_file = os.path.join(output_dir, f"whiteboard_{timestamp}.flv")

                # 根据操作系统选择 FFmpeg 参数
                if platform.system() == "Windows":
                    command = [
                        'ffmpeg',
                        '-f', 'gdigrab',
                        '-framerate', '30',
                        '-i', 'desktop',
                        '-c:v', 'libx264',
                        '-preset', 'fast',
                        self.output_file
                    ]
                    kwargs = {"creationflags": subprocess.CREATE_NO_WINDOW}
                else:  # Linux
                    command = [
                        'ffmpeg',
                        '-f', 'x11grab',
                        '-framerate', '30',
                        '-i', ':0.0',
                        '-c:v', 'libx264',
                        '-preset', 'fast',
                        self.output_file
                    ]
                    kwargs = {}

                self.recording_process = subprocess.Popen(
                    command,
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    **kwargs
                )

                self.is_recording = True
                self.start_time = time.time()
                self.timer.start(1000)

                # 通知其他模块开始录制
                self.broadcast_message("recording_started", {
                    "output_file": self.output_file,
                    "timestamp": datetime.now().isoformat()
                })

                self.logger.info(f"开始录制: {self.output_file}")

            except Exception as e:
                self.logger.error(f"启动录制失败: {str(e)}")

    def stop_recording(self):
        """停止录制"""
        if self.is_recording:
            try:
                if self.recording_process:
                    self.recording_process.terminate()
                    self.recording_process.wait()
                    self.recording_process = None

                self.is_recording = False
                self.timer.stop()

                # 通知其他模块停止录制
                self.broadcast_message("recording_stopped", {
                    "output_file": self.output_file,
                    "timestamp": datetime.now().isoformat()
                })

                self.logger.info(f"录制已停止: {self.output_file}")

            except Exception as e:
                self.logger.error(f"停止录制失败: {str(e)}")

    def update_timer(self):
        """更新录制计时器"""
        if self.is_recording and self.whiteboard_window:
            elapsed_time = int(time.time() - self.start_time)
            minutes = elapsed_time // 60
            seconds = elapsed_time % 60
            time_str = f"{minutes:02}:{seconds:02}"
            self.whiteboard_window.update_recording_time(time_str)

class WhiteboardWindow(QMainWindow):
    """白板窗口类"""

    def __init__(self, module: WhiteboardModule):
        super().__init__()
        self.module = module

        # 获取屏幕尺寸（高DPI适配）
        screen = QApplication.primaryScreen()
        self.screen_rect = screen.availableGeometry()
        self.screen_width = self.screen_rect.width()
        self.screen_height = self.screen_rect.height()

        # 初始化绘图相关属性
        self.pixmap = QPixmap(self.screen_width, self.screen_height)
        self.pixmap.fill(Qt.transparent)

        # 绘图状态
        self.drawing = False
        self.last_point = QPoint()
        self.current_points = []  # 当前绘制的点

        # 设置窗口属性
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool | self.windowFlags())
        self.setAttribute(Qt.WA_TranslucentBackground)

        # 创建UI
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主绘图层
        self.widget_main = QWidget(self)
        self.widget_main.setGeometry(0, 0, self.screen_width, self.screen_height)
        self.widget_main.setStyleSheet("background-color: rgba(0, 0, 0, 2)")

        # 底部工具菜单
        self.draw_menu = QWidget(self)
        self.draw_menu.setGeometry(
            (self.screen_width - 800) // 2, self.screen_height - 140, 800, 200)
        self.horizontal_layout = QHBoxLayout(self.draw_menu)

        # 创建工具按钮
        self.create_tool_buttons()

        # PPT控制菜单
        self.ppt_menu = QWidget(self)
        self.ppt_menu.setGeometry(
            (self.screen_width - 250), self.screen_height - 140, 250, 200)
        self.ppt_layout = QHBoxLayout(self.ppt_menu)

        # 创建PPT控制按钮
        self.create_ppt_buttons()

        # 菜单状态
        self.isMenuExpanded = True
    
    def create_tool_buttons(self):
        """创建工具按钮"""
        # 菜单伸缩按钮
        self.menu_pushButton = QPushButton("", self.draw_menu)
        self.menu_pushButton.setFixedSize(50, 50)
        self.menu_pushButton.clicked.connect(self.toggle_menu)
        self.menu_pushButton.setStyleSheet(MyCss.menuIconCss)
        self.horizontal_layout.addWidget(self.menu_pushButton)

        # 画笔按钮
        self.plotting_pushButton = QPushButton("", self.draw_menu)
        self.plotting_pushButton.setFixedSize(50, 50)
        self.plotting_pushButton.clicked.connect(self.show_pen_menu)
        self.plotting_pushButton.setStyleSheet(MyCss.plottingIconCss)
        self.horizontal_layout.addWidget(self.plotting_pushButton)

        # 画笔菜单
        self.pen_menu = QMenu(self.draw_menu)
        self.pen_menu.addAction("选择颜色", self.open_color_dialog)

        self.width_menu = QMenu("选择笔粗细", self.pen_menu)
        for i in range(1, 11):
            self.width_menu.addAction(f"笔宽 {i}", lambda w=i: self.set_pen_width(w))
        self.pen_menu.addMenu(self.width_menu)

        self.shape_menu = QMenu("形状选择", self.pen_menu)
        self.shape_menu.addAction("手绘", lambda: self.set_shape("freehand"))
        self.shape_menu.addAction("直线", lambda: self.set_shape("line"))
        self.shape_menu.addAction("矩形", lambda: self.set_shape("rectangle"))
        self.shape_menu.addAction("圆形", lambda: self.set_shape("circle"))
        self.shape_menu.addAction("三角形", lambda: self.set_shape("triangle"))
        self.pen_menu.addMenu(self.shape_menu)

        # 聚光灯按钮
        self.spotlight_pushButton = QPushButton("", self.draw_menu)
        self.spotlight_pushButton.setFixedSize(50, 50)
        self.spotlight_pushButton.clicked.connect(lambda: self.set_shape("spotlight"))
        self.spotlight_pushButton.setStyleSheet(MyCss.spotlightCss)
        self.horizontal_layout.addWidget(self.spotlight_pushButton)

        # 放大镜按钮
        self.magnify_pushButton = QPushButton("", self.draw_menu)
        self.magnify_pushButton.setFixedSize(50, 50)
        self.magnify_pushButton.clicked.connect(lambda: self.set_shape("magnifier"))
        self.magnify_pushButton.setStyleSheet(MyCss.MagnifyCss)
        self.horizontal_layout.addWidget(self.magnify_pushButton)

        # 擦除按钮
        self.eraser_pushButton = QPushButton("", self.draw_menu)
        self.eraser_pushButton.setFixedSize(50, 50)
        self.eraser_pushButton.clicked.connect(lambda: self.set_shape("eraser"))
        self.eraser_pushButton.setStyleSheet(MyCss.erasureIconCss)
        self.horizontal_layout.addWidget(self.eraser_pushButton)

        # 撤销按钮
        self.revoke_pushButton = QPushButton("", self.draw_menu)
        self.revoke_pushButton.setFixedSize(50, 50)
        self.revoke_pushButton.clicked.connect(self.undo_last_drawing)
        self.revoke_pushButton.setStyleSheet(MyCss.revokeIconCss)
        self.horizontal_layout.addWidget(self.revoke_pushButton)

        # 清屏按钮
        self.clear_pushButton = QPushButton("", self.draw_menu)
        self.clear_pushButton.setFixedSize(50, 50)
        self.clear_pushButton.clicked.connect(self.clear_all)
        self.clear_pushButton.setStyleSheet(MyCss.ClearIconCss)
        self.horizontal_layout.addWidget(self.clear_pushButton)

        # 截屏按钮
        self.capture_screen_pushButton = QPushButton("", self.draw_menu)
        self.capture_screen_pushButton.setFixedSize(50, 50)
        self.capture_screen_pushButton.setStyleSheet(MyCss.ScreenshotCss)
        self.capture_screen_pushButton.clicked.connect(self.capture_screen)
        self.horizontal_layout.addWidget(self.capture_screen_pushButton)

        # 录屏按钮
        self.record_screen_pushButton = QPushButton("", self.draw_menu)
        self.record_screen_pushButton.setFixedSize(50, 50)
        self.record_screen_pushButton.setStyleSheet(MyCss.recordCss)
        self.record_screen_pushButton.clicked.connect(self.toggle_recording)
        self.horizontal_layout.addWidget(self.record_screen_pushButton)

        # 保存按钮
        self.save_pushButton = QPushButton("💾", self.draw_menu)
        self.save_pushButton.setFixedSize(50, 50)
        self.save_pushButton.clicked.connect(self.save_content)
        self.save_pushButton.setStyleSheet(MyCss.ClearIconCss)
        self.horizontal_layout.addWidget(self.save_pushButton)

        # 加载按钮
        self.load_pushButton = QPushButton("📁", self.draw_menu)
        self.load_pushButton.setFixedSize(50, 50)
        self.load_pushButton.clicked.connect(self.load_content)
        self.load_pushButton.setStyleSheet(MyCss.ClearIconCss)
        self.horizontal_layout.addWidget(self.load_pushButton)

        # 关闭按钮
        self.close_pushButton = QPushButton("", self.draw_menu)
        self.close_pushButton.setFixedSize(50, 50)
        self.close_pushButton.clicked.connect(self.close)
        self.close_pushButton.setStyleSheet(MyCss.chCss)
        self.horizontal_layout.addWidget(self.close_pushButton)
    
    def create_ppt_buttons(self):
        """创建PPT控制按钮"""
        # PPT开始按钮
        self.ppt_pushButton_start = QPushButton("", self.ppt_menu)
        self.ppt_pushButton_start.setFixedSize(50, 50)
        self.ppt_pushButton_start.setStyleSheet(MyCss.pptCss)
        self.ppt_pushButton_start.clicked.connect(self.start_ppt)
        self.ppt_layout.addWidget(self.ppt_pushButton_start)

        # PPT下一页按钮
        self.ppt_pushButton_next = QPushButton("", self.ppt_menu)
        self.ppt_pushButton_next.setFixedSize(50, 50)
        self.ppt_pushButton_next.setStyleSheet(MyCss.ppt_rightCss)
        self.ppt_pushButton_next.clicked.connect(self.next_slide)
        self.ppt_layout.addWidget(self.ppt_pushButton_next)

        # PPT上一页按钮
        self.ppt_pushButton_prev = QPushButton("", self.ppt_menu)
        self.ppt_pushButton_prev.setFixedSize(50, 50)
        self.ppt_pushButton_prev.setStyleSheet(MyCss.ppt_leftCss)
        self.ppt_pushButton_prev.clicked.connect(self.prev_slide)
        self.ppt_layout.addWidget(self.ppt_pushButton_prev)

        # PPT停止按钮
        self.ppt_pushButton_stop = QPushButton("", self.ppt_menu)
        self.ppt_pushButton_stop.setFixedSize(50, 50)
        self.ppt_pushButton_stop.setStyleSheet(MyCss.ppt_EndCss)
        self.ppt_pushButton_stop.clicked.connect(self.stop_ppt)
        self.ppt_layout.addWidget(self.ppt_pushButton_stop)

    def toggle_menu(self):
        """切换菜单状态"""
        # 切换其他按钮的可见性
        for i in range(1, self.horizontal_layout.count()):
            widget = self.horizontal_layout.itemAt(i).widget()
            widget.setVisible(not widget.isVisible())

        if self.isMenuExpanded:
            # 收缩状态：关闭透明层和主图层
            self.widget_main.hide()
            self.setAttribute(Qt.WA_TransparentForMouseEvents, True)

            # 确保收缩按钮保持可点击
            self.menu_pushButton.setAttribute(Qt.WA_TransparentForMouseEvents, False)
            self.menu_pushButton.raise_()

            self.clear_all()
        else:
            # 展开状态：恢复透明层和主图层
            self.widget_main.show()
            self.widget_main.setStyleSheet("background-color: rgba(0, 0, 0, 2)")
            self.setAttribute(Qt.WA_TransparentForMouseEvents, False)

        # 保持窗口置顶
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.show()
        self.isMenuExpanded = not self.isMenuExpanded

    def show_pen_menu(self):
        """显示画笔菜单"""
        pos = self.plotting_pushButton.mapToGlobal(
            self.plotting_pushButton.rect().topLeft())
        pos.setY(pos.y() - self.pen_menu.sizeHint().height())
        self.pen_menu.exec_(pos)

    def open_color_dialog(self):
        """打开颜色选择对话框"""
        color = QColorDialog.getColor(self.module.pen_color, self, "选择颜色")
        if color.isValid():
            self.module.pen_color = color

    def set_pen_width(self, width: int):
        """设置画笔粗细"""
        self.module.fine = width

    def set_shape(self, shape: str):
        """设置当前绘制的形状"""
        self.module.current_shape = shape

    def undo_last_drawing(self):
        """撤销最后一次绘制"""
        if self.module.shapes:
            # 将当前状态保存到重做栈
            self.module.redo_stack.append(self.module.shapes.pop())

            # 重新绘制
            self.pixmap.fill(Qt.transparent)
            painter = QPainter(self.pixmap)
            for shape in self.module.shapes:
                self.draw_shape(painter, shape)
            painter.end()
            self.update()

    def clear_all(self):
        """清空所有内容"""
        self.module.shapes.clear()
        self.module.undo_stack.clear()
        self.module.redo_stack.clear()
        self.pixmap.fill(Qt.transparent)
        self.update()

    def save_content(self, filename: str = None):
        """保存白板内容"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_name = f"whiteboard_{timestamp}"
            filename, _ = QFileDialog.getSaveFileName(
                self, "保存白板内容", default_name,
                "PNG Files (*.png);;JSON Files (*.json);;All Files (*)"
            )

        if filename:
            try:
                if filename.endswith('.json'):
                    # 保存为JSON格式（包含形状数据）
                    data = {
                        "shapes": self.module.shapes,
                        "timestamp": datetime.now().isoformat(),
                        "version": "2.0.0"
                    }
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                else:
                    # 保存为图片格式
                    self.pixmap.save(filename)

                QMessageBox.information(self, "保存成功", f"白板内容已保存到: {filename}")
                return filename
            except Exception as e:
                QMessageBox.warning(self, "保存失败", f"无法保存白板内容: {str(e)}")
        return None

    def load_content(self, filename: str = None):
        """加载白板内容"""
        if not filename:
            filename, _ = QFileDialog.getOpenFileName(
                self, "加载白板内容", "",
                "JSON Files (*.json);;PNG Files (*.png);;All Files (*)"
            )

        if filename and os.path.exists(filename):
            try:
                if filename.endswith('.json'):
                    # 从JSON格式加载
                    with open(filename, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    self.module.shapes = data.get("shapes", [])
                    self.redraw_all_shapes()
                else:
                    # 从图片格式加载
                    self.pixmap.load(filename)

                self.update()
                QMessageBox.information(self, "加载成功", f"白板内容已从 {filename} 加载")
                return True
            except Exception as e:
                QMessageBox.warning(self, "加载失败", f"无法加载白板内容: {str(e)}")
        return False

    def redraw_all_shapes(self):
        """重新绘制所有形状"""
        self.pixmap.fill(Qt.transparent)
        painter = QPainter(self.pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        for shape in self.module.shapes:
            self.draw_shape(painter, shape)

        painter.end()

    def capture_screen(self):
        """截屏功能"""
        try:
            screenshot_folder = os.path.join(os.getcwd(), "record/img")
            if not os.path.exists(screenshot_folder):
                os.makedirs(screenshot_folder)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(screenshot_folder, f"screenshot_{timestamp}.png")

            # 使用scrot或其他截屏工具
            if platform.system() == "Linux":
                subprocess.run(["scrot", filename])
            else:
                # Windows下可以使用其他方法
                screen = QApplication.primaryScreen()
                screenshot = screen.grabWindow(0)
                screenshot.save(filename)

            QMessageBox.information(self, "截屏成功", f"截图已保存为: {filename}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"截屏失败: {str(e)}")

    def toggle_recording(self):
        """切换录制状态"""
        if self.module.is_recording:
            self.module.stop_recording()
            self.record_screen_pushButton.setStyleSheet(MyCss.recordCss)
        else:
            self.module.start_recording()
            self.record_screen_pushButton.setStyleSheet(MyCss.butBCss)

    def update_recording_time(self, time_str: str):
        """更新录制时间显示"""
        if hasattr(self, 'record_screen_pushButton'):
            self.record_screen_pushButton.setText(time_str)

    # PPT控制方法
    def activate_ppt_window(self):
        """激活PPT窗口"""
        try:
            if platform.system() == "Linux":
                result = subprocess.run(
                    ["wmctrl", "-l"],
                    capture_output=True,
                    text=True
                )
                windows = result.stdout.splitlines()
                ppt_windows = [w for w in windows if "WPS Presentation" in w or "LibreOffice Impress" in w]

                if ppt_windows:
                    window_id = ppt_windows[0].split()[0]
                    subprocess.run(["wmctrl", "-i", "-a", window_id])
                    subprocess.run(["xdotool", "windowfocus", window_id])
                else:
                    QMessageBox.warning(self, "警告", "未检测到 PPT 窗口")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"激活窗口失败: {str(e)}")

    def start_ppt(self):
        """开始放映PPT"""
        self.activate_ppt_window()
        if platform.system() == "Linux":
            subprocess.run(["xdotool", "key", "F5"])
        else:
            # Windows下的实现
            pass

    def prev_slide(self):
        """上一页"""
        self.activate_ppt_window()
        if platform.system() == "Linux":
            subprocess.run(["xdotool", "key", "Left"])

    def next_slide(self):
        """下一页"""
        self.activate_ppt_window()
        if platform.system() == "Linux":
            subprocess.run(["xdotool", "key", "Right"])

    def stop_ppt(self):
        """结束放映"""
        self.activate_ppt_window()
        if platform.system() == "Linux":
            subprocess.run(["xdotool", "key", "Escape"])

    def draw_shape(self, painter: QPainter, shape: Dict[str, Any]):
        """绘制单个形状"""
        if not isinstance(shape, dict):
            return

        pen = QPen(QColor(shape.get("color", "#FF0000")),
                  shape.get("fine", 5), Qt.SolidLine, Qt.RoundCap)
        painter.setPen(pen)

        shape_type = shape.get("shape", "freehand")

        if shape_type == "line":
            start = QPoint(shape["start"]["x"], shape["start"]["y"])
            end = QPoint(shape["end"]["x"], shape["end"]["y"])
            painter.drawLine(start, end)
        elif shape_type == "rectangle":
            start = QPoint(shape["start"]["x"], shape["start"]["y"])
            end = QPoint(shape["end"]["x"], shape["end"]["y"])
            rect = QRect(start, end)
            painter.drawRect(rect)
        elif shape_type == "triangle":
            points = shape.get("points", [])
            if len(points) >= 3:
                qpoints = [QPoint(p["x"], p["y"]) for p in points]
                polygon = QPolygon(qpoints)
                painter.drawPolygon(polygon)
        elif shape_type == "circle":
            start = QPoint(shape["start"]["x"], shape["start"]["y"])
            end = QPoint(shape["end"]["x"], shape["end"]["y"])
            radius = int((end - start).manhattanLength() / 2)
            center = start
            painter.drawEllipse(center, radius, radius)
        elif shape_type == "freehand":
            points = shape.get("points", [])
            if len(points) > 1:
                for i in range(1, len(points)):
                    p1 = QPoint(points[i-1]["x"], points[i-1]["y"])
                    p2 = QPoint(points[i]["x"], points[i]["y"])
                    painter.drawLine(p1, p2)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.drawing = True
            self.module.start_point = event.pos()
            self.last_point = event.pos()

            if self.module.current_shape == "freehand":
                self.current_points = [{"x": event.pos().x(), "y": event.pos().y()}]
            elif self.module.current_shape == "eraser":
                self.module.eraser_points = [event.pos()]

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.drawing and event.buttons() & Qt.LeftButton:
            self.module.end_point = event.pos()

            if self.module.current_shape == "freehand":
                self.current_points.append({"x": event.pos().x(), "y": event.pos().y()})
            elif self.module.current_shape == "eraser":
                # 在pixmap上擦除
                painter = QPainter(self.pixmap)
                painter.setCompositionMode(QPainter.CompositionMode_Clear)
                painter.setPen(QPen(Qt.transparent, self.module.eraser_size, Qt.SolidLine, Qt.RoundCap))
                painter.drawLine(self.last_point, event.pos())
                painter.end()
                self.last_point = event.pos()
            elif self.module.current_shape == "spotlight":
                self.last_point = event.pos()
            elif self.module.current_shape == "magnifier":
                self.last_point = event.pos()

            self.update()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.drawing:
            self.drawing = False
            shape = None

            if self.module.current_shape == "line":
                shape = {
                    "shape": self.module.current_shape,
                    "start": {"x": self.module.start_point.x(), "y": self.module.start_point.y()},
                    "end": {"x": self.module.end_point.x(), "y": self.module.end_point.y()},
                    "color": self.module.pen_color.name(),
                    "fine": self.module.fine
                }
            elif self.module.current_shape == "rectangle":
                shape = {
                    "shape": self.module.current_shape,
                    "start": {"x": self.module.start_point.x(), "y": self.module.start_point.y()},
                    "end": {"x": self.module.end_point.x(), "y": self.module.end_point.y()},
                    "color": self.module.pen_color.name(),
                    "fine": self.module.fine
                }
            elif self.module.current_shape == "triangle":
                mid_x = (self.module.start_point.x() + self.module.end_point.x()) / 2
                side_length = abs(self.module.end_point.x() - self.module.start_point.x())
                height = int((3 ** 0.5 / 2) * side_length)
                points = [
                    {"x": self.module.start_point.x(), "y": self.module.end_point.y()},
                    {"x": self.module.end_point.x(), "y": self.module.end_point.y()},
                    {"x": int(mid_x), "y": self.module.end_point.y() - height}
                ]
                shape = {
                    "shape": self.module.current_shape,
                    "points": points,
                    "color": self.module.pen_color.name(),
                    "fine": self.module.fine
                }
            elif self.module.current_shape == "circle":
                shape = {
                    "shape": self.module.current_shape,
                    "start": {"x": self.module.start_point.x(), "y": self.module.start_point.y()},
                    "end": {"x": self.module.end_point.x(), "y": self.module.end_point.y()},
                    "color": self.module.pen_color.name(),
                    "fine": self.module.fine
                }
            elif self.module.current_shape == "freehand":
                shape = {
                    "shape": self.module.current_shape,
                    "points": self.current_points[:],
                    "color": self.module.pen_color.name(),
                    "fine": self.module.fine
                }

            if shape:
                self.module.shapes.append(shape)
                # 将形状绘制到pixmap中
                painter = QPainter(self.pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                self.draw_shape(painter, shape)
                painter.end()

                # 通知协作模块
                if self.module.collaboration_enabled:
                    self.module.broadcast_message("drawing_sync", {
                        "action": "add_shape",
                        "shape": shape,
                        "timestamp": datetime.now().isoformat()
                    })

            self.update()

    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.drawPixmap(0, 0, self.pixmap)

        # 绘制当前实时预览形状
        if self.drawing:
            pen = QPen(self.module.pen_color, self.module.fine, Qt.SolidLine, Qt.RoundCap)
            painter.setPen(pen)

            if self.module.current_shape == "line":
                painter.drawLine(self.module.start_point, self.module.end_point)
            elif self.module.current_shape == "rectangle":
                rect = QRect(self.module.start_point, self.module.end_point)
                painter.drawRect(rect)
            elif self.module.current_shape == "triangle":
                mid_x = (self.module.start_point.x() + self.module.end_point.x()) / 2
                side_length = abs(self.module.end_point.x() - self.module.start_point.x())
                height = int((3 ** 0.5 / 2) * side_length)
                points = [
                    QPoint(self.module.start_point.x(), self.module.end_point.y()),
                    QPoint(self.module.end_point.x(), self.module.end_point.y()),
                    QPoint(int(mid_x), self.module.end_point.y() - height)
                ]
                painter.drawPolygon(QPolygon(points))
            elif self.module.current_shape == "circle":
                radius = int((self.module.end_point - self.module.start_point).manhattanLength() / 2)
                center = self.module.start_point
                painter.drawEllipse(center, radius, radius)
            elif self.module.current_shape == "freehand" and hasattr(self, 'current_points') and len(self.current_points) > 1:
                for i in range(1, len(self.current_points)):
                    p1 = QPoint(self.current_points[i-1]["x"], self.current_points[i-1]["y"])
                    p2 = QPoint(self.current_points[i]["x"], self.current_points[i]["y"])
                    painter.drawLine(p1, p2)

            # 聚光灯效果
            elif self.module.current_shape == "spotlight":
                gradient = QRadialGradient(self.last_point, self.module.spotlight_radius)
                gradient.setColorAt(0, QColor(255, 255, 255, 150))
                gradient.setColorAt(1, QColor(0, 0, 0, 150))
                painter.setBrush(QBrush(gradient))
                painter.setPen(Qt.NoPen)
                painter.drawEllipse(self.last_point, self.module.spotlight_radius, self.module.spotlight_radius)

            # 放大镜效果
            elif self.module.current_shape == "magnifier":
                magnified_rect = QRect(self.last_point.x() - self.module.magnifier_radius,
                                     self.last_point.y() - self.module.magnifier_radius,
                                     self.module.magnifier_radius * 2,
                                     self.module.magnifier_radius * 2)
                magnified_pixmap = self.pixmap.copy(magnified_rect).scaled(magnified_rect.size() * 2)
                painter.drawPixmap(magnified_rect.topLeft(), magnified_pixmap)
                painter.setPen(QPen(Qt.black, 2))
                painter.drawEllipse(self.last_point, self.module.magnifier_radius, self.module.magnifier_radius)

    def resizeEvent(self, event):
        """窗口大小调整事件"""
        new_pixmap = QPixmap(self.size())
        new_pixmap.fill(Qt.transparent)
        painter = QPainter(new_pixmap)
        painter.drawPixmap(0, 0, self.pixmap)
        painter.end()
        self.pixmap = new_pixmap

    def sync_drawing(self, data: Dict[str, Any]):
        """同步绘图数据"""
        action = data.get("action")
        if action == "add_shape":
            shape = data.get("shape")
            if shape:
                self.module.shapes.append(shape)
                painter = QPainter(self.pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                self.draw_shape(painter, shape)
                painter.end()
                self.update()
        elif action == "clear_all":
            self.clear_all()
        elif action == "undo":
            self.undo_last_drawing()

# 为了兼容性，保留原始类名
Whiteboard = WhiteboardModule