import sys
import os
import json
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtCore import QUrl, Qt, QThread, pyqtSignal


class UrlLoaderThread(QThread):
    """用于异步加载网址的线程"""
    url_loaded = pyqtSignal(str)  # 信号，用于传递加载的网址

    def __init__(self, json_path):
        super().__init__()
        self.json_path = json_path

    def run(self):
        """线程运行方法"""
        try:
            with open(self.json_path, "r", encoding="utf-8") as f:
                data = json.load(f)
                url = data.get("url")  # 提取 url 字段
                if url:
                    self.url_loaded.emit(url)  # 发射信号，传递网址
                else:
                    print("JSON 文件中未找到有效的网址！")
        except Exception as e:
            print(f"读取 JSON 文件时出错: {e}")


class OpenPlatformApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()

    def get_widget(self):
        return self

    def initUI(self):
        # 设置窗口标题和大小
        self.setWindowTitle("资源平台")
        self.setGeometry(0, 0, 800, 600)  # 默认窗口大小
        self.setWindowFlags(Qt.FramelessWindowHint |Qt.Tool | self.windowFlags())

        # 创建一个 QWebEngineView 组件
        self.browser = QWebEngineView()
        
        # 设置布局
        layout = QVBoxLayout()
        layout.addWidget(self.browser)

        # 创建一个容器窗口
        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

        # 延迟加载网址
        self.load_url_after_show()

    def load_url_after_show(self):
        """在窗口显示后加载网址"""
        # 从 JSON 文件中读取网址
        # current_dir = os.path.dirname(os.path.abspath(__file__))
        json_path = os.path.join("./database/web_urls.json")

        # 检查 JSON 文件是否存在
        if not os.path.exists(json_path):
            print(f"文件 {json_path} 不存在！")
            return

        # 创建并启动异步加载线程
        self.url_loader_thread = UrlLoaderThread(json_path)
        self.url_loader_thread.url_loaded.connect(self.on_url_loaded)  # 连接信号
        self.url_loader_thread.start()

    def on_url_loaded(self, url):
        """异步加载网址完成后的回调"""
        print(f"加载网址: {url}")
        self.browser.setUrl(QUrl(url))


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = OpenPlatformApp()
    window.show()
    sys.exit(app.exec_())
