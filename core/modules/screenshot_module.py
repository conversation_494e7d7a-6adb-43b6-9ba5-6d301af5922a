"""
截屏模块 - 基于新的模块化架构
支持快捷键、编辑标注和分享功能
"""
import os
import sys
import platform
import subprocess
import json
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
from .base_module import CommonModule

class ScreenshotModule(CommonModule):
    """截屏模块"""
    
    def __init__(self):
        super().__init__(
            module_name="screenshot",
            display_name="屏幕截图",
            version="1.0.0"
        )
        
        # 截屏相关属性
        self.screenshot_window = None
        self.editor_window = None
        self.current_screenshot = None
        self.screenshot_history = []
        
        # 截屏设置
        self.save_directory = "screenshots"
        self.image_format = "png"  # png, jpg, bmp
        self.image_quality = 95  # 1-100
        self.auto_save = True
        self.show_cursor = True
        self.capture_delay = 0  # 延迟秒数
        
        # 快捷键
        self.hotkey_fullscreen = "Ctrl+Shift+A"
        self.hotkey_region = "Ctrl+Shift+S"
        self.hotkey_window = "Ctrl+Shift+W"
        
        # 编辑工具
        self.current_tool = "none"  # none, pen, arrow, rectangle, text
        self.pen_color = QColor(255, 0, 0)
        self.pen_width = 3
        self.annotations = []
    
    def _initialize_module(self) -> bool:
        """初始化截屏模块"""
        try:
            # 创建截图保存目录
            if not os.path.exists(self.save_directory):
                os.makedirs(self.save_directory)
            
            # 创建截屏窗口
            self.screenshot_window = ScreenshotWindow(self)
            
            # 设置模块配置
            self.config.update({
                "save_directory": "screenshots",
                "default_format": "png",
                "default_quality": 95,
                "auto_save": True,
                "show_cursor": True,
                "capture_delay": 0,
                "enable_hotkeys": True,
                "enable_editor": True
            })
            
            # 注册全局快捷键
            self._register_hotkeys()
            
            # 加载截屏历史
            self._load_screenshot_history()
            
            self.logger.info("截屏模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"截屏模块初始化失败: {str(e)}")
            return False
    
    def _cleanup_module(self):
        """清理截屏模块资源"""
        try:
            # 注销快捷键
            self._unregister_hotkeys()
            
            # 保存截屏历史
            self._save_screenshot_history()
            
            # 关闭窗口
            if self.screenshot_window:
                self.screenshot_window.close()
                self.screenshot_window = None
            
            if self.editor_window:
                self.editor_window.close()
                self.editor_window = None
            
            self.logger.info("截屏模块资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理截屏模块资源时出错: {str(e)}")
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "take_screenshot":
            capture_type = data.get("type", "fullscreen")
            self.take_screenshot(capture_type)
        elif message_type == "open_editor":
            self.open_editor(data.get("image_path"))
        elif message_type == "get_screenshot_history":
            self._send_screenshot_history(sender)
    
    def show(self):
        """显示截屏窗口"""
        if self.screenshot_window:
            self.screenshot_window.show()
            self.screenshot_window.raise_()
            self.screenshot_window.activateWindow()
    
    def hide(self):
        """隐藏截屏窗口"""
        if self.screenshot_window:
            self.screenshot_window.hide()
    
    def close(self):
        """关闭截屏模块"""
        self.cleanup()
    
    def take_screenshot(self, capture_type: str = "fullscreen", delay: int = None) -> str:
        """截取屏幕截图"""
        try:
            if delay is None:
                delay = self.capture_delay
            
            # 延迟截图
            if delay > 0:
                QTimer.singleShot(delay * 1000, lambda: self._do_screenshot(capture_type))
                return None
            else:
                return self._do_screenshot(capture_type)
                
        except Exception as e:
            self.logger.error(f"截屏失败: {str(e)}")
            return None
    
    def _do_screenshot(self, capture_type: str) -> str:
        """执行截屏操作"""
        try:
            screenshot = None
            
            if capture_type == "fullscreen":
                screenshot = self._capture_fullscreen()
            elif capture_type == "region":
                screenshot = self._capture_region()
            elif capture_type == "window":
                screenshot = self._capture_window()
            
            if screenshot:
                # 生成文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"screenshot_{timestamp}.{self.image_format}"
                filepath = os.path.join(self.save_directory, filename)
                
                # 保存截图
                if self.auto_save:
                    screenshot.save(filepath, self.image_format.upper(), self.image_quality)
                
                # 更新当前截图
                self.current_screenshot = screenshot
                
                # 添加到历史记录
                self._add_to_history(filepath, capture_type)
                
                # 通知其他模块
                self.broadcast_message("screenshot_taken", {
                    "filepath": filepath,
                    "type": capture_type,
                    "timestamp": datetime.now().isoformat()
                })
                
                self.logger.info(f"截屏完成: {filepath}")
                return filepath
            
        except Exception as e:
            self.logger.error(f"执行截屏失败: {str(e)}")
            return None
    
    def _capture_fullscreen(self) -> QPixmap:
        """捕获全屏"""
        screen = QApplication.primaryScreen()
        return screen.grabWindow(0)
    
    def _capture_region(self) -> QPixmap:
        """捕获区域（需要用户选择）"""
        # 创建区域选择窗口
        selector = RegionSelector()
        if selector.exec_() == QDialog.Accepted:
            rect = selector.selected_rect
            if rect.isValid():
                screen = QApplication.primaryScreen()
                return screen.grabWindow(0, rect.x(), rect.y(), rect.width(), rect.height())
        return None
    
    def _capture_window(self) -> QPixmap:
        """捕获活动窗口"""
        try:
            if platform.system() == "Linux":
                # 使用xdotool获取活动窗口
                result = subprocess.run(
                    ["xdotool", "getactivewindow"], 
                    capture_output=True, 
                    text=True
                )
                if result.returncode == 0:
                    window_id = result.stdout.strip()
                    # 使用import命令截取窗口
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    temp_file = f"/tmp/window_screenshot_{timestamp}.png"
                    subprocess.run(["import", "-window", window_id, temp_file])
                    
                    if os.path.exists(temp_file):
                        pixmap = QPixmap(temp_file)
                        os.remove(temp_file)
                        return pixmap
            else:
                # Windows下使用Qt的方式
                return self._capture_fullscreen()
                
        except Exception as e:
            self.logger.error(f"捕获窗口失败: {str(e)}")
            return self._capture_fullscreen()
    
    def open_editor(self, image_path: str = None):
        """打开截图编辑器"""
        try:
            if image_path and os.path.exists(image_path):
                pixmap = QPixmap(image_path)
            elif self.current_screenshot:
                pixmap = self.current_screenshot
            else:
                QMessageBox.warning(None, "警告", "没有可编辑的截图")
                return
            
            # 创建编辑器窗口
            if self.editor_window:
                self.editor_window.close()
            
            self.editor_window = ScreenshotEditor(self, pixmap)
            self.editor_window.show()
            
        except Exception as e:
            self.logger.error(f"打开编辑器失败: {str(e)}")
    
    def save_screenshot(self, pixmap: QPixmap, annotations: List = None) -> str:
        """保存截图（包含标注）"""
        try:
            # 如果有标注，先绘制到截图上
            if annotations:
                painter = QPainter(pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                
                for annotation in annotations:
                    self._draw_annotation(painter, annotation)
                
                painter.end()
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_edited_{timestamp}.{self.image_format}"
            filepath = os.path.join(self.save_directory, filename)
            
            # 保存文件
            pixmap.save(filepath, self.image_format.upper(), self.image_quality)
            
            # 添加到历史记录
            self._add_to_history(filepath, "edited")
            
            self.logger.info(f"截图已保存: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"保存截图失败: {str(e)}")
            return None
    
    def _draw_annotation(self, painter: QPainter, annotation: Dict[str, Any]):
        """绘制标注"""
        tool = annotation.get("tool")
        color = QColor(annotation.get("color", "#FF0000"))
        width = annotation.get("width", 3)
        
        pen = QPen(color, width)
        painter.setPen(pen)
        
        if tool == "pen":
            points = annotation.get("points", [])
            for i in range(1, len(points)):
                p1 = QPoint(points[i-1]["x"], points[i-1]["y"])
                p2 = QPoint(points[i]["x"], points[i]["y"])
                painter.drawLine(p1, p2)
        
        elif tool == "rectangle":
            rect = annotation.get("rect")
            if rect:
                painter.drawRect(rect["x"], rect["y"], rect["width"], rect["height"])
        
        elif tool == "arrow":
            start = annotation.get("start")
            end = annotation.get("end")
            if start and end:
                # 绘制箭头线
                painter.drawLine(start["x"], start["y"], end["x"], end["y"])
                # 绘制箭头头部（简化版）
                # TODO: 实现更精确的箭头绘制
        
        elif tool == "text":
            text = annotation.get("text", "")
            pos = annotation.get("position")
            if text and pos:
                painter.drawText(pos["x"], pos["y"], text)
    
    def _register_hotkeys(self):
        """注册全局快捷键"""
        try:
            if self.config.get("enable_hotkeys", True):
                # 这里需要实现全局快捷键注册
                # 由于PyQt5的全局快捷键支持有限，可能需要使用第三方库
                pass
        except Exception as e:
            self.logger.error(f"注册快捷键失败: {str(e)}")
    
    def _unregister_hotkeys(self):
        """注销全局快捷键"""
        try:
            # 注销快捷键
            pass
        except Exception as e:
            self.logger.error(f"注销快捷键失败: {str(e)}")
    
    def _add_to_history(self, filepath: str, capture_type: str):
        """添加到截屏历史"""
        record = {
            "filepath": filepath,
            "filename": os.path.basename(filepath),
            "type": capture_type,
            "timestamp": datetime.now().isoformat(),
            "size": os.path.getsize(filepath) if os.path.exists(filepath) else 0
        }
        
        self.screenshot_history.append(record)
        
        # 限制历史记录数量
        if len(self.screenshot_history) > 100:
            self.screenshot_history = self.screenshot_history[-100:]
    
    def get_screenshot_history(self) -> List[Dict[str, Any]]:
        """获取截屏历史"""
        return self.screenshot_history.copy()
    
    def _send_screenshot_history(self, requester: str):
        """发送截屏历史给请求者"""
        history = self.get_screenshot_history()
        self.send_message(requester, "screenshot_history", {"history": history})
    
    def _load_screenshot_history(self):
        """加载截屏历史"""
        try:
            history_file = os.path.join(self.save_directory, "screenshot_history.json")
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    self.screenshot_history = json.load(f)
        except Exception as e:
            self.logger.error(f"加载截屏历史失败: {str(e)}")
            self.screenshot_history = []
    
    def _save_screenshot_history(self):
        """保存截屏历史"""
        try:
            history_file = os.path.join(self.save_directory, "screenshot_history.json")
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(self.screenshot_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存截屏历史失败: {str(e)}")

class RegionSelector(QDialog):
    """区域选择器"""
    
    def __init__(self):
        super().__init__()
        self.selected_rect = QRect()
        self.start_point = QPoint()
        self.end_point = QPoint()
        self.selecting = False
        
        # 设置为全屏透明窗口
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowState(Qt.WindowFullScreen)
        
        # 设置鼠标跟踪
        self.setMouseTracking(True)
        
        # 获取屏幕截图作为背景
        screen = QApplication.primaryScreen()
        self.background = screen.grabWindow(0)
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        
        # 绘制背景截图
        painter.drawPixmap(0, 0, self.background)
        
        # 绘制半透明遮罩
        painter.fillRect(self.rect(), QColor(0, 0, 0, 100))
        
        # 如果正在选择，绘制选择区域
        if self.selecting and self.selected_rect.isValid():
            # 清除选择区域的遮罩
            painter.setCompositionMode(QPainter.CompositionMode_Clear)
            painter.fillRect(self.selected_rect, Qt.transparent)
            
            # 绘制选择框边框
            painter.setCompositionMode(QPainter.CompositionMode_SourceOver)
            painter.setPen(QPen(Qt.red, 2))
            painter.drawRect(self.selected_rect)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.start_point = event.pos()
            self.selecting = True
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.selecting:
            self.end_point = event.pos()
            self.selected_rect = QRect(self.start_point, self.end_point).normalized()
            self.update()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.selecting:
            self.selecting = False
            if self.selected_rect.width() > 10 and self.selected_rect.height() > 10:
                self.accept()
            else:
                self.reject()
    
    def keyPressEvent(self, event):
        """键盘按下事件"""
        if event.key() == Qt.Key_Escape:
            self.reject()

class ScreenshotWindow(QMainWindow):
    """截屏主窗口"""

    def __init__(self, module: ScreenshotModule):
        super().__init__()
        self.module = module

        # 窗口设置
        self.setWindowTitle("屏幕截图工具")
        self.setGeometry(100, 100, 400, 300)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)

        # 创建UI
        self.setup_ui()

        # 连接信号
        self.setup_signals()

    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 截屏按钮区域
        capture_group = QGroupBox("截屏操作")
        capture_layout = QVBoxLayout(capture_group)

        # 截屏按钮
        button_layout = QHBoxLayout()

        self.fullscreen_btn = QPushButton("全屏截图")
        self.fullscreen_btn.setStyleSheet(self._get_button_style("#4CAF50"))
        self.fullscreen_btn.setMinimumHeight(40)
        button_layout.addWidget(self.fullscreen_btn)

        self.region_btn = QPushButton("区域截图")
        self.region_btn.setStyleSheet(self._get_button_style("#2196F3"))
        self.region_btn.setMinimumHeight(40)
        button_layout.addWidget(self.region_btn)

        self.window_btn = QPushButton("窗口截图")
        self.window_btn.setStyleSheet(self._get_button_style("#FF9800"))
        self.window_btn.setMinimumHeight(40)
        button_layout.addWidget(self.window_btn)

        capture_layout.addLayout(button_layout)

        # 延迟设置
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("延迟时间:"))
        self.delay_spin = QSpinBox()
        self.delay_spin.setRange(0, 10)
        self.delay_spin.setSuffix(" 秒")
        self.delay_spin.setValue(self.module.capture_delay)
        delay_layout.addWidget(self.delay_spin)
        delay_layout.addStretch()

        capture_layout.addLayout(delay_layout)
        main_layout.addWidget(capture_group)

        # 设置区域
        settings_group = QGroupBox("截图设置")
        settings_layout = QFormLayout(settings_group)

        # 图片格式
        self.format_combo = QComboBox()
        self.format_combo.addItems(["png", "jpg", "bmp"])
        self.format_combo.setCurrentText(self.module.image_format)
        settings_layout.addRow("图片格式:", self.format_combo)

        # 图片质量
        self.quality_spin = QSpinBox()
        self.quality_spin.setRange(1, 100)
        self.quality_spin.setValue(self.module.image_quality)
        settings_layout.addRow("图片质量:", self.quality_spin)

        # 自动保存
        self.auto_save_cb = QCheckBox("自动保存")
        self.auto_save_cb.setChecked(self.module.auto_save)
        settings_layout.addRow("", self.auto_save_cb)

        # 保存目录
        save_layout = QHBoxLayout()
        self.save_path_edit = QLineEdit(self.module.save_directory)
        self.browse_btn = QPushButton("浏览")
        save_layout.addWidget(self.save_path_edit)
        save_layout.addWidget(self.browse_btn)
        settings_layout.addRow("保存目录:", save_layout)

        main_layout.addWidget(settings_group)

        # 操作按钮区域
        action_layout = QHBoxLayout()

        self.editor_btn = QPushButton("打开编辑器")
        self.editor_btn.setStyleSheet(self._get_button_style("#9C27B0"))
        action_layout.addWidget(self.editor_btn)

        self.history_btn = QPushButton("查看历史")
        self.history_btn.setStyleSheet(self._get_button_style("#607D8B"))
        action_layout.addWidget(self.history_btn)

        self.folder_btn = QPushButton("打开文件夹")
        self.folder_btn.setStyleSheet(self._get_button_style("#795548"))
        action_layout.addWidget(self.folder_btn)

        main_layout.addLayout(action_layout)
        main_layout.addStretch()

    def setup_signals(self):
        """设置信号连接"""
        # 截屏按钮
        self.fullscreen_btn.clicked.connect(lambda: self.take_screenshot("fullscreen"))
        self.region_btn.clicked.connect(lambda: self.take_screenshot("region"))
        self.window_btn.clicked.connect(lambda: self.take_screenshot("window"))

        # 设置变更
        self.delay_spin.valueChanged.connect(self.update_settings)
        self.format_combo.currentTextChanged.connect(self.update_settings)
        self.quality_spin.valueChanged.connect(self.update_settings)
        self.auto_save_cb.toggled.connect(self.update_settings)
        self.save_path_edit.textChanged.connect(self.update_settings)

        # 其他按钮
        self.browse_btn.clicked.connect(self.browse_save_directory)
        self.editor_btn.clicked.connect(self.open_editor)
        self.history_btn.clicked.connect(self.show_history)
        self.folder_btn.clicked.connect(self.open_save_folder)

    def _get_button_style(self, color: str) -> str:
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background-color: {color}DD;
            }}
            QPushButton:pressed {{
                background-color: {color}BB;
            }}
        """

    def take_screenshot(self, capture_type: str):
        """截取屏幕截图"""
        # 隐藏窗口以避免截到自己
        self.hide()

        # 延迟一点时间让窗口完全隐藏
        QTimer.singleShot(200, lambda: self._do_take_screenshot(capture_type))

    def _do_take_screenshot(self, capture_type: str):
        """执行截屏"""
        filepath = self.module.take_screenshot(capture_type, self.delay_spin.value())

        # 显示窗口
        self.show()

        if filepath:
            QMessageBox.information(self, "截屏成功", f"截图已保存到: {filepath}")
        else:
            QMessageBox.warning(self, "截屏失败", "截图操作失败，请重试")

    def update_settings(self):
        """更新设置"""
        self.module.capture_delay = self.delay_spin.value()
        self.module.image_format = self.format_combo.currentText()
        self.module.image_quality = self.quality_spin.value()
        self.module.auto_save = self.auto_save_cb.isChecked()
        self.module.save_directory = self.save_path_edit.text()

        # 确保保存目录存在
        if not os.path.exists(self.module.save_directory):
            os.makedirs(self.module.save_directory)

    def browse_save_directory(self):
        """浏览保存目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择保存目录", self.save_path_edit.text()
        )
        if directory:
            self.save_path_edit.setText(directory)
            self.update_settings()

    def open_editor(self):
        """打开编辑器"""
        self.module.open_editor()

    def show_history(self):
        """显示历史记录"""
        history_dialog = ScreenshotHistoryDialog(self.module, self)
        history_dialog.exec_()

    def open_save_folder(self):
        """打开保存文件夹"""
        save_dir = self.module.save_directory
        if os.path.exists(save_dir):
            if platform.system() == "Windows":
                os.startfile(save_dir)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", save_dir])
            else:  # Linux
                subprocess.run(["xdg-open", save_dir])
        else:
            QMessageBox.warning(self, "警告", "保存目录不存在")

class ScreenshotEditor(QMainWindow):
    """截图编辑器"""

    def __init__(self, module: ScreenshotModule, pixmap: QPixmap):
        super().__init__()
        self.module = module
        self.original_pixmap = pixmap.copy()
        self.current_pixmap = pixmap.copy()
        self.annotations = []

        # 编辑状态
        self.current_tool = "none"
        self.pen_color = QColor(255, 0, 0)
        self.pen_width = 3
        self.drawing = False
        self.last_point = QPoint()
        self.current_annotation = None

        # 窗口设置
        self.setWindowTitle("截图编辑器")
        self.setGeometry(100, 100, 800, 600)

        # 创建UI
        self.setup_ui()

        # 连接信号
        self.setup_signals()

    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 工具栏
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)

        # 图片显示区域
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("border: 1px solid gray;")
        self.image_label.setMinimumSize(400, 300)

        # 设置图片
        self.update_image_display()

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.image_label)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)

    def create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QWidget()
        layout = QHBoxLayout(toolbar)

        # 工具按钮
        self.select_btn = QPushButton("选择")
        self.select_btn.setCheckable(True)
        self.select_btn.setChecked(True)
        layout.addWidget(self.select_btn)

        self.pen_btn = QPushButton("画笔")
        self.pen_btn.setCheckable(True)
        layout.addWidget(self.pen_btn)

        self.rect_btn = QPushButton("矩形")
        self.rect_btn.setCheckable(True)
        layout.addWidget(self.rect_btn)

        self.arrow_btn = QPushButton("箭头")
        self.arrow_btn.setCheckable(True)
        layout.addWidget(self.arrow_btn)

        self.text_btn = QPushButton("文字")
        self.text_btn.setCheckable(True)
        layout.addWidget(self.text_btn)

        # 工具按钮组
        self.tool_group = QButtonGroup()
        self.tool_group.addButton(self.select_btn, 0)
        self.tool_group.addButton(self.pen_btn, 1)
        self.tool_group.addButton(self.rect_btn, 2)
        self.tool_group.addButton(self.arrow_btn, 3)
        self.tool_group.addButton(self.text_btn, 4)

        layout.addSeparator()

        # 颜色选择
        self.color_btn = QPushButton("颜色")
        self.color_btn.setStyleSheet(f"background-color: {self.pen_color.name()};")
        layout.addWidget(self.color_btn)

        # 线宽选择
        layout.addWidget(QLabel("线宽:"))
        self.width_spin = QSpinBox()
        self.width_spin.setRange(1, 20)
        self.width_spin.setValue(self.pen_width)
        layout.addWidget(self.width_spin)

        layout.addSeparator()

        # 操作按钮
        self.undo_btn = QPushButton("撤销")
        layout.addWidget(self.undo_btn)

        self.clear_btn = QPushButton("清除")
        layout.addWidget(self.clear_btn)

        self.save_btn = QPushButton("保存")
        layout.addWidget(self.save_btn)

        self.close_btn = QPushButton("关闭")
        layout.addWidget(self.close_btn)

        layout.addStretch()

        return toolbar

    def setup_signals(self):
        """设置信号连接"""
        # 工具选择
        self.tool_group.buttonClicked.connect(self.tool_changed)

        # 其他控件
        self.color_btn.clicked.connect(self.choose_color)
        self.width_spin.valueChanged.connect(self.width_changed)

        # 操作按钮
        self.undo_btn.clicked.connect(self.undo_annotation)
        self.clear_btn.clicked.connect(self.clear_annotations)
        self.save_btn.clicked.connect(self.save_image)
        self.close_btn.clicked.connect(self.close)

        # 图片标签鼠标事件
        self.image_label.mousePressEvent = self.image_mouse_press
        self.image_label.mouseMoveEvent = self.image_mouse_move
        self.image_label.mouseReleaseEvent = self.image_mouse_release

    def tool_changed(self, button):
        """工具改变"""
        tool_map = {0: "none", 1: "pen", 2: "rectangle", 3: "arrow", 4: "text"}
        self.current_tool = tool_map.get(self.tool_group.id(button), "none")

    def choose_color(self):
        """选择颜色"""
        color = QColorDialog.getColor(self.pen_color, self, "选择颜色")
        if color.isValid():
            self.pen_color = color
            self.color_btn.setStyleSheet(f"background-color: {color.name()};")

    def width_changed(self, value):
        """线宽改变"""
        self.pen_width = value

    def image_mouse_press(self, event):
        """图片鼠标按下"""
        if event.button() == Qt.LeftButton and self.current_tool != "none":
            self.drawing = True
            self.last_point = event.pos()

            # 开始新的标注
            self.current_annotation = {
                "tool": self.current_tool,
                "color": self.pen_color.name(),
                "width": self.pen_width
            }

            if self.current_tool == "pen":
                self.current_annotation["points"] = [
                    {"x": event.pos().x(), "y": event.pos().y()}
                ]

    def image_mouse_move(self, event):
        """图片鼠标移动"""
        if self.drawing and self.current_annotation:
            if self.current_tool == "pen":
                self.current_annotation["points"].append(
                    {"x": event.pos().x(), "y": event.pos().y()}
                )
                self.update_image_display()

    def image_mouse_release(self, event):
        """图片鼠标释放"""
        if self.drawing and self.current_annotation:
            self.drawing = False

            if self.current_tool == "rectangle":
                rect = QRect(self.last_point, event.pos()).normalized()
                self.current_annotation["rect"] = {
                    "x": rect.x(), "y": rect.y(),
                    "width": rect.width(), "height": rect.height()
                }
            elif self.current_tool == "arrow":
                self.current_annotation["start"] = {
                    "x": self.last_point.x(), "y": self.last_point.y()
                }
                self.current_annotation["end"] = {
                    "x": event.pos().x(), "y": event.pos().y()
                }

            # 添加到标注列表
            self.annotations.append(self.current_annotation)
            self.current_annotation = None

            # 更新显示
            self.update_image_display()

    def update_image_display(self):
        """更新图片显示"""
        # 复制原始图片
        display_pixmap = self.original_pixmap.copy()

        # 绘制所有标注
        painter = QPainter(display_pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制已完成的标注
        for annotation in self.annotations:
            self.module._draw_annotation(painter, annotation)

        # 绘制当前正在绘制的标注
        if self.current_annotation and self.current_tool == "pen":
            points = self.current_annotation.get("points", [])
            if len(points) > 1:
                pen = QPen(self.pen_color, self.pen_width)
                painter.setPen(pen)
                for i in range(1, len(points)):
                    p1 = QPoint(points[i-1]["x"], points[i-1]["y"])
                    p2 = QPoint(points[i]["x"], points[i]["y"])
                    painter.drawLine(p1, p2)

        painter.end()

        # 更新显示
        self.image_label.setPixmap(display_pixmap)
        self.current_pixmap = display_pixmap

    def undo_annotation(self):
        """撤销标注"""
        if self.annotations:
            self.annotations.pop()
            self.update_image_display()

    def clear_annotations(self):
        """清除所有标注"""
        self.annotations.clear()
        self.update_image_display()

    def save_image(self):
        """保存图片"""
        filepath = self.module.save_screenshot(self.current_pixmap, self.annotations)
        if filepath:
            QMessageBox.information(self, "保存成功", f"图片已保存到: {filepath}")
        else:
            QMessageBox.warning(self, "保存失败", "保存图片失败")

class ScreenshotHistoryDialog(QDialog):
    """截屏历史对话框"""

    def __init__(self, module: ScreenshotModule, parent=None):
        super().__init__(parent)
        self.module = module

        self.setWindowTitle("截屏历史")
        self.setGeometry(200, 200, 600, 400)

        self.setup_ui()
        self.load_history()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)

        # 历史列表
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(4)
        self.history_table.setHorizontalHeaderLabels(["文件名", "类型", "大小", "时间"])

        # 设置表格属性
        header = self.history_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)

        layout.addWidget(self.history_table)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.open_btn = QPushButton("打开")
        self.edit_btn = QPushButton("编辑")
        self.delete_btn = QPushButton("删除")
        self.clear_btn = QPushButton("清空历史")
        self.close_btn = QPushButton("关闭")

        button_layout.addWidget(self.open_btn)
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

        # 连接信号
        self.open_btn.clicked.connect(self.open_selected)
        self.edit_btn.clicked.connect(self.edit_selected)
        self.delete_btn.clicked.connect(self.delete_selected)
        self.clear_btn.clicked.connect(self.clear_history)
        self.close_btn.clicked.connect(self.close)

    def load_history(self):
        """加载历史记录"""
        history = self.module.get_screenshot_history()
        self.history_table.setRowCount(len(history))

        for row, record in enumerate(reversed(history)):
            self.history_table.setItem(row, 0, QTableWidgetItem(record.get("filename", "")))
            self.history_table.setItem(row, 1, QTableWidgetItem(record.get("type", "")))

            # 格式化文件大小
            size = record.get("size", 0)
            if size > 1024 * 1024:
                size_str = f"{size / (1024 * 1024):.1f} MB"
            elif size > 1024:
                size_str = f"{size / 1024:.1f} KB"
            else:
                size_str = f"{size} B"

            self.history_table.setItem(row, 2, QTableWidgetItem(size_str))
            self.history_table.setItem(row, 3, QTableWidgetItem(record.get("timestamp", "")))

    def open_selected(self):
        """打开选中的文件"""
        current_row = self.history_table.currentRow()
        if current_row >= 0:
            history = self.module.get_screenshot_history()
            if current_row < len(history):
                record = list(reversed(history))[current_row]
                filepath = record.get("filepath", "")
                if os.path.exists(filepath):
                    if platform.system() == "Windows":
                        os.startfile(filepath)
                    elif platform.system() == "Darwin":
                        subprocess.run(["open", filepath])
                    else:
                        subprocess.run(["xdg-open", filepath])
                else:
                    QMessageBox.warning(self, "警告", "文件不存在")

    def edit_selected(self):
        """编辑选中的文件"""
        current_row = self.history_table.currentRow()
        if current_row >= 0:
            history = self.module.get_screenshot_history()
            if current_row < len(history):
                record = list(reversed(history))[current_row]
                filepath = record.get("filepath", "")
                if os.path.exists(filepath):
                    self.module.open_editor(filepath)
                else:
                    QMessageBox.warning(self, "警告", "文件不存在")

    def delete_selected(self):
        """删除选中的文件"""
        current_row = self.history_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(
                self, "确认", "确定要删除选中的文件吗？",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                history = self.module.get_screenshot_history()
                if current_row < len(history):
                    record = list(reversed(history))[current_row]
                    filepath = record.get("filepath", "")

                    # 删除文件
                    if os.path.exists(filepath):
                        os.remove(filepath)

                    # 从历史中移除
                    self.module.screenshot_history.remove(record)
                    self.module._save_screenshot_history()

                    # 刷新显示
                    self.load_history()

    def clear_history(self):
        """清空历史"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空所有历史记录吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.module.screenshot_history.clear()
            self.module._save_screenshot_history()
            self.load_history()

# 为了兼容性，保留原始类名
Screenshot = ScreenshotModule
