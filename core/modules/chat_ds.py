import sys
import json
import queue
import threading
import requests
import sounddevice as sd
from vosk import Model, KaldiRecognizer
import pyttsx3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTextEdit, QPushButton, QVBoxLayout, QHBoxLayout, QWidget
)
from PyQt5.QtCore import Qt


class DeepSeekVoiceAssistant(QMainWindow):
    def __init__(self, model_path: str = "model", sample_rate: int = 16000):
        super().__init__()

        # ===== 全局配置 =====
        self.API_URL = "https://api.deepseek.com/v1/chat/completions"
        self.API_TOKEN = ""
        self.MODEL_PATH = model_path
        self.SAMPLE_RATE = sample_rate

        # ===== 语音识别相关全局变量 =====
        self.audio_queue = queue.Queue()
        self.is_recording = False

        # ===== 初始化语音识别模型 =====
        self.model = Model(self.MODEL_PATH)
        self.recognizer = KaldiRecognizer(self.model, self.SAMPLE_RATE)

        # ===== 初始化音频流 =====
        self.audio_stream = sd.RawInputStream(
            samplerate=self.SAMPLE_RATE,
            blocksize=1600,
            dtype="int16",
            channels=1,
            callback=self.audio_callback
        )

        # ===== 初始化主界面 =====
        self.setWindowTitle("小马对话机器人")
        self.setGeometry(600, 300, 800, 600)
        self.setWindowFlags(Qt.Tool | self.windowFlags())

        # ===== 初始化界面组件 =====
        self._init_ui()

    def _init_ui(self):
        """初始化用户界面"""

        # 主布局
        main_layout = QVBoxLayout()

        # 消息显示区域
        self.display_box = QTextEdit(self)
        self.display_box.setReadOnly(True)
        self.display_box.setFontPointSize(12)
        main_layout.addWidget(self.display_box)

        # 输入控件框架
        input_layout = QHBoxLayout()

        # 文本输入
        self.user_input = QTextEdit(self)
        self.user_input.setMaximumHeight(100)
        input_layout.addWidget(self.user_input)

        # 按钮框架
        button_layout = QVBoxLayout()

        self.start_button = QPushButton("开始录音", self)
        self.start_button.clicked.connect(self.start_recording)
        button_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("停止录音", self)
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_recording)
        button_layout.addWidget(self.stop_button)

        self.send_button = QPushButton("发送", self)
        self.send_button.clicked.connect(self.send_text)
        button_layout.addWidget(self.send_button)

        input_layout.addLayout(button_layout)
        main_layout.addLayout(input_layout)

        # 设置主布局
        central_widget = QWidget(self)
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

    def audio_callback(self, indata, frames, time, status):
        """实时音频采集回调函数"""
        if self.is_recording:
            self.audio_queue.put(bytes(indata))

    def start_recording(self):
        """开始录音"""
        self.is_recording = True
        self.audio_stream.start()
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.update_display("录音中...（点击停止按钮结束）")

    def stop_recording(self):
        """停止录音并进行识别"""
        self.is_recording = False
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

        # 在子线程处理语音识别
        threading.Thread(target=self.process_audio).start()

    def process_audio(self):
        """处理录音队列中的音频数据"""
        try:
            full_text = []

            while not self.audio_queue.empty() or self.is_recording:
                try:
                    data = self.audio_queue.get_nowait()
                    if self.recognizer.AcceptWaveform(data):
                        result = json.loads(self.recognizer.Result())
                        if text := result.get("text"):
                            full_text.append(text)
                except queue.Empty:
                    continue

            # 获取最终结果
            final_result = json.loads(self.recognizer.FinalResult())
            if text := final_result.get("text"):
                full_text.append(text)

            if full_text:
                user_input = " ".join(full_text)
                self.update_display(f"用户（语音）：{user_input}")
                self.get_ai_response(user_input)

        except Exception as e:
            self.update_display(f"语音识别错误：{str(e)}")

    def call_deepseek_api(self, input_text: str) -> str:
        """调用DeepSeek API并返回响应"""
        try:
            headers = {"Authorization": f"Bearer {self.API_TOKEN}"}
            payload = {
                "model": "deepseek-chat",
                "messages": [{"role": "user", "content": input_text}],
                "temperature": 0.7
            }
            response = requests.post(
                self.API_URL, json=payload, headers=headers, timeout=10)
            return response.json()['choices'][0]['message']['content'] if response.ok else "API请求失败"
        except Exception as e:
            return f"API错误：{str(e)}"

    def get_ai_response(self, text: str):
        """获取并显示AI回复"""
        def worker():
            self.update_display("小马机器人：思考中...")
            response = self.call_deepseek_api(text)
            self.update_display(f"小马机器人：{response}")
            self.speak_text(response)

        threading.Thread(target=worker).start()

    def send_text(self):
        """处理文本框输入"""
        text = self.user_input.toPlainText().strip()
        if text:
            self.user_input.clear()
            self.update_display(f"用户：{text}")
            self.get_ai_response(text)
        else:
            self.update_display("提示：输入不能为空")

    def speak_text(self, text: str):
        """文本转语音"""
        def worker():
            engine = pyttsx3.init()
            engine.say(text)
            engine.runAndWait()
        threading.Thread(target=worker).start()

    def update_display(self, message: str):
        """更新消息显示"""
        self.display_box.append(f"{message}\n{'-'*50}\n")
        self.display_box.verticalScrollBar().setValue(
            self.display_box.verticalScrollBar().maximum())

    def closeEvent(self, event):
        """处理窗口关闭事件"""
        self.audio_stream.stop()
        event.accept()


# ===== 主程序入口 =====
if __name__ == "__main__":
    app = QApplication(sys.argv)
    assistant = DeepSeekVoiceAssistant()
    assistant.show()
    sys.exit(app.exec())
