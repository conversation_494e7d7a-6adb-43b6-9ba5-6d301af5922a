"""
白板协作模块 - 教师专用功能
扩展现有白板功能，增加协作开关，使用xdotool实现双向实时同步
"""
import sys
import os
import json
import subprocess
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QListWidget, QListWidgetItem,
                            QGroupBox, QTextEdit, QCheckBox, QSpinBox,
                            QMessageBox, QProgressBar, QFrame, QGridLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QDialog, QDialogButtonBox)
from PyQt5.QtCore import QTimer, pyqtSignal, Qt, QThread, pyqtSlot, QObject
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPixmap, QPainter
from .base_module import TeacherModule
from .udp_discovery import DeviceInfo
import logging

class CollaborationSession:
    """协作会话类"""
    
    def __init__(self, session_id: str, teacher_device: str, group_device: str):
        self.session_id = session_id
        self.teacher_device = teacher_device
        self.group_device = group_device
        self.start_time = datetime.now()
        self.is_active = True
        self.sync_events: List[Dict[str, Any]] = []
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "session_id": self.session_id,
            "teacher_device": self.teacher_device,
            "group_device": self.group_device,
            "start_time": self.start_time.isoformat(),
            "is_active": self.is_active,
            "sync_events_count": len(self.sync_events)
        }

class XDotoolSyncWorker(QObject):
    """xdotool同步工作线程"""
    
    sync_event = pyqtSignal(dict)  # 同步事件信号
    error_occurred = pyqtSignal(str)  # 错误信号
    
    def __init__(self, target_device_ip: str):
        super().__init__()
        self.target_device_ip = target_device_ip
        self.is_running = False
        self.sync_thread = None
    
    def start_sync(self):
        """开始同步"""
        if not self.is_running:
            self.is_running = True
            self.sync_thread = threading.Thread(target=self._sync_loop, daemon=True)
            self.sync_thread.start()
    
    def stop_sync(self):
        """停止同步"""
        self.is_running = False
        if self.sync_thread and self.sync_thread.is_alive():
            self.sync_thread.join(timeout=1)
    
    def _sync_loop(self):
        """同步循环"""
        while self.is_running:
            try:
                # 监听本地鼠标和键盘事件
                self._capture_input_events()
                time.sleep(0.01)  # 10ms间隔
            except Exception as e:
                self.error_occurred.emit(f"同步循环错误: {str(e)}")
                break
    
    def _capture_input_events(self):
        """捕获输入事件"""
        try:
            # 使用xdotool获取鼠标位置
            result = subprocess.run(['xdotool', 'getmouselocation'], 
                                  capture_output=True, text=True, timeout=1)
            if result.returncode == 0:
                # 解析鼠标位置
                output = result.stdout.strip()
                # 格式: x:123 y:456 screen:0 window:789
                parts = output.split()
                x = int(parts[0].split(':')[1])
                y = int(parts[1].split(':')[1])
                
                # 发送同步事件
                self.sync_event.emit({
                    "type": "mouse_move",
                    "x": x,
                    "y": y,
                    "timestamp": datetime.now().isoformat()
                })
        except Exception as e:
            # 忽略超时和其他小错误
            pass
    
    def send_mouse_event(self, x: int, y: int, event_type: str = "move"):
        """发送鼠标事件到目标设备"""
        try:
            if event_type == "move":
                cmd = ['ssh', f'user@{self.target_device_ip}', 
                       f'xdotool mousemove {x} {y}']
            elif event_type == "click":
                cmd = ['ssh', f'user@{self.target_device_ip}', 
                       f'xdotool mousemove {x} {y} click 1']
            elif event_type == "drag":
                cmd = ['ssh', f'user@{self.target_device_ip}', 
                       f'xdotool mousemove {x} {y} mousedown 1']
            elif event_type == "release":
                cmd = ['ssh', f'user@{self.target_device_ip}', 
                       f'xdotool mouseup 1']
            
            subprocess.run(cmd, timeout=1, capture_output=True)
        except Exception as e:
            self.error_occurred.emit(f"发送鼠标事件失败: {str(e)}")
    
    def send_key_event(self, key: str, event_type: str = "press"):
        """发送键盘事件到目标设备"""
        try:
            if event_type == "press":
                cmd = ['ssh', f'user@{self.target_device_ip}', 
                       f'xdotool key {key}']
            elif event_type == "type":
                cmd = ['ssh', f'user@{self.target_device_ip}', 
                       f'xdotool type "{key}"']
            
            subprocess.run(cmd, timeout=1, capture_output=True)
        except Exception as e:
            self.error_occurred.emit(f"发送键盘事件失败: {str(e)}")

class WhiteboardCollaborationModule(TeacherModule):
    """白板协作模块"""
    
    # 模块信号
    collaboration_started = pyqtSignal(object)  # CollaborationSession
    collaboration_stopped = pyqtSignal(str)     # session_id
    sync_event_received = pyqtSignal(dict)      # 同步事件
    
    def __init__(self):
        super().__init__(
            module_name="whiteboard_collaboration",
            display_name="白板协作",
            version="1.0.0"
        )
        
        # 协作相关
        self.active_sessions: Dict[str, CollaborationSession] = {}
        self.sync_workers: Dict[str, XDotoolSyncWorker] = {}
        self.available_devices: Dict[str, DeviceInfo] = {}
        
        # 界面
        self.collaboration_window = None
        
        # 白板模块引用
        self.whiteboard_module = None
        
        # 配置
        self.config.update({
            "enable_bidirectional_sync": True,  # 双向同步
            "sync_mouse_events": True,          # 同步鼠标事件
            "sync_keyboard_events": True,       # 同步键盘事件
            "sync_interval_ms": 10,             # 同步间隔（毫秒）
            "max_sessions": 5,                  # 最大协作会话数
            "auto_reconnect": True              # 自动重连
        })
    
    def get_widget(self):
        return self.collaboration_window

    def _initialize_module(self) -> bool:
        """初始化白板协作模块"""
        try:
            # 检查认证状态
            if not self.is_authenticated():
                self.logger.error("白板协作模块需要教师认证")
                return False
            
            # 检查xdotool是否可用
            if not self._check_xdotool_available():
                self.logger.error("xdotool不可用，无法启用白板协作功能")
                return False
            
            # 获取白板模块实例
            self.whiteboard_module = self._get_whiteboard_module()
            if not self.whiteboard_module:
                self.logger.warning("无法获取白板模块，协作功能可能受限")
            
            # 创建界面
            self.collaboration_window = WhiteboardCollaborationWindow(self)
            
            # 获取设备发现模块，监听设备变化
            self._setup_device_discovery()
            
            self.logger.info("白板协作模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"白板协作模块初始化失败: {str(e)}")
            return False
    
    def _cleanup_module(self):
        """清理白板协作模块资源"""
        try:
            # 停止所有协作会话
            for session_id in list(self.active_sessions.keys()):
                self.stop_collaboration(session_id)
            
            # 停止所有同步工作线程
            for worker in self.sync_workers.values():
                worker.stop_sync()
            self.sync_workers.clear()
            
            # 关闭界面
            if self.collaboration_window:
                self.collaboration_window.close()
                self.collaboration_window = None
            
            # 清理数据
            self.active_sessions.clear()
            self.available_devices.clear()
            
            self.logger.info("白板协作模块资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理白板协作模块资源时出错: {str(e)}")
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "start_collaboration":
            group_device_id = data.get("group_device_id")
            if group_device_id:
                self.start_collaboration(group_device_id)
        
        elif message_type == "stop_collaboration":
            session_id = data.get("session_id")
            if session_id:
                self.stop_collaboration(session_id)
        
        elif message_type == "sync_event":
            self.handle_sync_event(data)
        
        elif message_type == "get_collaboration_status":
            self.send_collaboration_status(sender)
        
        elif message_type == "show_collaboration":
            self.show()
    
    def _check_xdotool_available(self) -> bool:
        """检查xdotool是否可用"""
        try:
            result = subprocess.run(['which', 'xdotool'], 
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except Exception:
            return False
    
    def _get_whiteboard_module(self):
        """获取白板模块实例"""
        try:
            # 通过模块管理器获取白板模块
            if hasattr(self, 'module_manager') and self.module_manager:
                return self.module_manager.get_module_instance("whiteboard")
            return None
        except Exception as e:
            self.logger.error(f"获取白板模块失败: {e}")
            return None
    
    def _setup_device_discovery(self):
        """设置设备发现"""
        try:
            # 获取UDP发现模块
            udp_discovery = self._get_udp_discovery_module()
            if udp_discovery:
                # 连接设备发现信号
                udp_discovery.device_discovered.connect(self.on_device_discovered)
                udp_discovery.device_updated.connect(self.on_device_updated)
                udp_discovery.device_offline.connect(self.on_device_offline)
                
                # 获取当前设备列表
                self.available_devices = udp_discovery.get_discovered_devices()
        except Exception as e:
            self.logger.error(f"设置设备发现失败: {e}")
    
    def _get_udp_discovery_module(self):
        """获取UDP发现模块实例"""
        try:
            if hasattr(self, 'module_manager') and self.module_manager:
                return self.module_manager.get_module_instance("udp_discovery")
            return None
        except Exception as e:
            self.logger.error(f"获取UDP发现模块失败: {e}")
            return None

    def show(self):
        """显示白板协作窗口"""
        if self.collaboration_window:
            self.collaboration_window.show()
            self.collaboration_window.raise_()
            self.collaboration_window.activateWindow()

    def hide(self):
        """隐藏白板协作窗口"""
        if self.collaboration_window:
            self.collaboration_window.hide()

    def close(self):
        """关闭白板协作模块"""
        self.cleanup()

    def start_collaboration(self, group_device_id: str) -> Optional[str]:
        """开始与指定小组设备的协作"""
        try:
            # 检查设备是否可用
            if group_device_id not in self.available_devices:
                self.logger.error(f"设备 {group_device_id} 不可用")
                return None

            # 检查是否已达到最大会话数
            if len(self.active_sessions) >= self.get_config("max_sessions", 5):
                self.logger.error("已达到最大协作会话数")
                return None

            # 创建协作会话
            session_id = f"session_{int(time.time())}"
            teacher_device = "teacher_main"  # 教师设备ID

            session = CollaborationSession(session_id, teacher_device, group_device_id)
            self.active_sessions[session_id] = session

            # 获取目标设备信息
            target_device = self.available_devices[group_device_id]

            # 创建同步工作线程
            sync_worker = XDotoolSyncWorker(target_device.ip_address)
            sync_worker.sync_event.connect(self.on_sync_event)
            sync_worker.error_occurred.connect(self.on_sync_error)

            self.sync_workers[session_id] = sync_worker

            # 启动同步
            if self.get_config("enable_bidirectional_sync", True):
                sync_worker.start_sync()

            # 通知白板模块启用协作
            if self.whiteboard_module:
                self.whiteboard_module.collaboration_enabled = True
                self.whiteboard_module.collaboration_target = group_device_id

            # 发送信号
            self.collaboration_started.emit(session)

            # 通知其他模块
            self.broadcast_message("collaboration_started", {
                "session_id": session_id,
                "teacher_device": teacher_device,
                "group_device": group_device_id,
                "timestamp": datetime.now().isoformat()
            })

            self.logger.info(f"开始与设备 {group_device_id} 的协作会话: {session_id}")
            return session_id

        except Exception as e:
            self.logger.error(f"启动协作失败: {str(e)}")
            return None

    def stop_collaboration(self, session_id: str):
        """停止协作会话"""
        try:
            if session_id not in self.active_sessions:
                self.logger.warning(f"协作会话 {session_id} 不存在")
                return

            session = self.active_sessions[session_id]
            session.is_active = False

            # 停止同步工作线程
            if session_id in self.sync_workers:
                self.sync_workers[session_id].stop_sync()
                del self.sync_workers[session_id]

            # 通知白板模块禁用协作
            if self.whiteboard_module:
                self.whiteboard_module.collaboration_enabled = False
                self.whiteboard_module.collaboration_target = None

            # 移除会话
            del self.active_sessions[session_id]

            # 发送信号
            self.collaboration_stopped.emit(session_id)

            # 通知其他模块
            self.broadcast_message("collaboration_stopped", {
                "session_id": session_id,
                "timestamp": datetime.now().isoformat()
            })

            self.logger.info(f"停止协作会话: {session_id}")

        except Exception as e:
            self.logger.error(f"停止协作失败: {str(e)}")

    def handle_sync_event(self, event_data: Dict[str, Any]):
        """处理同步事件"""
        try:
            event_type = event_data.get("type")

            if event_type == "mouse_move":
                self._handle_mouse_move(event_data)
            elif event_type == "mouse_click":
                self._handle_mouse_click(event_data)
            elif event_type == "mouse_drag":
                self._handle_mouse_drag(event_data)
            elif event_type == "key_press":
                self._handle_key_press(event_data)
            elif event_type == "drawing_sync":
                self._handle_drawing_sync(event_data)

            # 记录同步事件
            for session in self.active_sessions.values():
                if session.is_active:
                    session.sync_events.append(event_data)

            # 发送信号
            self.sync_event_received.emit(event_data)

        except Exception as e:
            self.logger.error(f"处理同步事件失败: {str(e)}")

    def _handle_mouse_move(self, event_data: Dict[str, Any]):
        """处理鼠标移动事件"""
        x = event_data.get("x", 0)
        y = event_data.get("y", 0)

        # 同步到所有活跃会话的目标设备
        for session_id, session in self.active_sessions.items():
            if session.is_active and session_id in self.sync_workers:
                worker = self.sync_workers[session_id]
                worker.send_mouse_event(x, y, "move")

    def _handle_mouse_click(self, event_data: Dict[str, Any]):
        """处理鼠标点击事件"""
        x = event_data.get("x", 0)
        y = event_data.get("y", 0)

        for session_id, session in self.active_sessions.items():
            if session.is_active and session_id in self.sync_workers:
                worker = self.sync_workers[session_id]
                worker.send_mouse_event(x, y, "click")

    def _handle_mouse_drag(self, event_data: Dict[str, Any]):
        """处理鼠标拖拽事件"""
        x = event_data.get("x", 0)
        y = event_data.get("y", 0)
        action = event_data.get("action", "drag")  # drag, release

        for session_id, session in self.active_sessions.items():
            if session.is_active and session_id in self.sync_workers:
                worker = self.sync_workers[session_id]
                worker.send_mouse_event(x, y, action)

    def _handle_key_press(self, event_data: Dict[str, Any]):
        """处理键盘按键事件"""
        key = event_data.get("key", "")
        action = event_data.get("action", "press")  # press, type

        if self.get_config("sync_keyboard_events", True):
            for session_id, session in self.active_sessions.items():
                if session.is_active and session_id in self.sync_workers:
                    worker = self.sync_workers[session_id]
                    worker.send_key_event(key, action)

    def _handle_drawing_sync(self, event_data: Dict[str, Any]):
        """处理绘图同步事件"""
        # 这里可以实现更复杂的绘图同步逻辑
        # 例如同步画笔颜色、粗细、形状等
        pass

    def on_sync_event(self, event_data: Dict[str, Any]):
        """同步事件处理"""
        self.handle_sync_event(event_data)

    def on_sync_error(self, error_message: str):
        """同步错误处理"""
        self.logger.error(f"同步错误: {error_message}")

    def on_device_discovered(self, device_info: DeviceInfo):
        """设备发现事件处理"""
        self.available_devices[device_info.device_id] = device_info
        self.logger.info(f"发现新设备: {device_info.device_name}")

    def on_device_updated(self, device_info: DeviceInfo):
        """设备更新事件处理"""
        self.available_devices[device_info.device_id] = device_info

    def on_device_offline(self, device_id: str):
        """设备离线事件处理"""
        if device_id in self.available_devices:
            del self.available_devices[device_id]

        # 停止与该设备的协作会话
        sessions_to_stop = []
        for session_id, session in self.active_sessions.items():
            if session.group_device == device_id:
                sessions_to_stop.append(session_id)

        for session_id in sessions_to_stop:
            self.stop_collaboration(session_id)

    def get_available_groups(self) -> List[DeviceInfo]:
        """获取可用的小组设备列表"""
        return [device for device in self.available_devices.values()
                if device.device_type == "group" and device.status == "online"]

    def get_collaboration_status(self) -> Dict[str, Any]:
        """获取协作状态"""
        return {
            "active_sessions": len(self.active_sessions),
            "available_devices": len(self.available_devices),
            "sessions": [session.to_dict() for session in self.active_sessions.values()]
        }

    def send_collaboration_status(self, requester: str):
        """发送协作状态"""
        status = self.get_collaboration_status()
        self.send_message(requester, "collaboration_status", status)

class WhiteboardCollaborationWindow(QWidget):
    """白板协作窗口"""

    def __init__(self, module: WhiteboardCollaborationModule):
        super().__init__()
        self.module = module
        self.init_ui()

        # 连接模块信号
        self.module.collaboration_started.connect(self.on_collaboration_started)
        self.module.collaboration_stopped.connect(self.on_collaboration_stopped)
        self.module.sync_event_received.connect(self.on_sync_event_received)

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("白板协作 - 智慧课堂教学工具")
        self.setGeometry(100, 100, 1000, 700)

        # 主布局
        main_layout = QVBoxLayout()

        # 标题栏
        title_layout = QHBoxLayout()
        title_label = QLabel("白板协作")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新设备")
        self.refresh_btn.clicked.connect(self.refresh_devices)
        title_layout.addWidget(self.refresh_btn)

        main_layout.addLayout(title_layout)

        # 主要内容区域
        content_layout = QHBoxLayout()

        # 左侧：设备选择和控制
        left_panel = self.create_control_panel()
        content_layout.addWidget(left_panel, 1)

        # 右侧：协作状态和历史
        right_panel = self.create_status_panel()
        content_layout.addWidget(right_panel, 1)

        main_layout.addLayout(content_layout)

        self.setLayout(main_layout)

        # 设置样式
        self.setStyleSheet("""
            QWidget {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #c0c0c0;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QListWidget {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 3px;
            }
        """)

    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QWidget()
        layout = QVBoxLayout()

        # 设备选择区域
        device_group = QGroupBox("选择小组设备")
        device_layout = QVBoxLayout()

        # 设备列表
        self.device_list = QListWidget()
        self.device_list.itemSelectionChanged.connect(self.on_device_selection_changed)
        device_layout.addWidget(self.device_list)

        device_group.setLayout(device_layout)
        layout.addWidget(device_group)

        # 协作控制区域
        control_group = QGroupBox("协作控制")
        control_layout = QVBoxLayout()

        # 开始协作按钮
        self.start_btn = QPushButton("开始协作")
        self.start_btn.setFont(QFont("Arial", 12, QFont.Bold))
        self.start_btn.setMinimumHeight(50)
        self.start_btn.clicked.connect(self.start_collaboration)
        self.start_btn.setEnabled(False)
        self.start_btn.setStyleSheet("QPushButton { background-color: #28a745; }")
        control_layout.addWidget(self.start_btn)

        # 停止协作按钮
        self.stop_btn = QPushButton("停止协作")
        self.stop_btn.setFont(QFont("Arial", 12, QFont.Bold))
        self.stop_btn.setMinimumHeight(50)
        self.stop_btn.clicked.connect(self.stop_collaboration)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("QPushButton { background-color: #dc3545; }")
        control_layout.addWidget(self.stop_btn)

        control_group.setLayout(control_layout)
        layout.addWidget(control_group)

        # 协作设置区域
        settings_group = QGroupBox("协作设置")
        settings_layout = QVBoxLayout()

        # 双向同步选项
        self.bidirectional_cb = QCheckBox("启用双向同步")
        self.bidirectional_cb.setChecked(True)
        self.bidirectional_cb.toggled.connect(self.update_settings)
        settings_layout.addWidget(self.bidirectional_cb)

        # 鼠标同步选项
        self.mouse_sync_cb = QCheckBox("同步鼠标事件")
        self.mouse_sync_cb.setChecked(True)
        self.mouse_sync_cb.toggled.connect(self.update_settings)
        settings_layout.addWidget(self.mouse_sync_cb)

        # 键盘同步选项
        self.keyboard_sync_cb = QCheckBox("同步键盘事件")
        self.keyboard_sync_cb.setChecked(True)
        self.keyboard_sync_cb.toggled.connect(self.update_settings)
        settings_layout.addWidget(self.keyboard_sync_cb)

        # 同步间隔设置
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("同步间隔:"))
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(1, 100)
        self.interval_spin.setValue(10)
        self.interval_spin.setSuffix(" ms")
        self.interval_spin.valueChanged.connect(self.update_settings)
        interval_layout.addWidget(self.interval_spin)
        interval_layout.addStretch()
        settings_layout.addLayout(interval_layout)

        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)

        layout.addStretch()
        panel.setLayout(layout)
        return panel

    def create_status_panel(self) -> QWidget:
        """创建状态面板"""
        panel = QWidget()
        layout = QVBoxLayout()

        # 协作状态区域
        status_group = QGroupBox("协作状态")
        status_layout = QVBoxLayout()

        # 状态标签
        self.status_label = QLabel("未开始协作")
        self.status_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: white;
                border: 2px solid #007acc;
                border-radius: 8px;
                padding: 15px;
                color: #007acc;
            }
        """)
        status_layout.addWidget(self.status_label)

        # 协作信息
        info_layout = QGridLayout()

        info_layout.addWidget(QLabel("当前协作设备:"), 0, 0)
        self.current_device_label = QLabel("无")
        info_layout.addWidget(self.current_device_label, 0, 1)

        info_layout.addWidget(QLabel("协作开始时间:"), 1, 0)
        self.start_time_label = QLabel("无")
        info_layout.addWidget(self.start_time_label, 1, 1)

        info_layout.addWidget(QLabel("同步事件数:"), 2, 0)
        self.sync_count_label = QLabel("0")
        info_layout.addWidget(self.sync_count_label, 2, 1)

        status_layout.addLayout(info_layout)
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)

        # 活跃会话列表
        sessions_group = QGroupBox("活跃会话")
        sessions_layout = QVBoxLayout()

        self.sessions_table = QTableWidget()
        self.sessions_table.setColumnCount(4)
        self.sessions_table.setHorizontalHeaderLabels(["会话ID", "目标设备", "开始时间", "状态"])

        header = self.sessions_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeToContents)

        sessions_layout.addWidget(self.sessions_table)
        sessions_group.setLayout(sessions_layout)
        layout.addWidget(sessions_group)

        # 同步事件日志
        log_group = QGroupBox("同步事件日志")
        log_layout = QVBoxLayout()

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        log_group.setLayout(log_layout)
        layout.addWidget(log_group)

        panel.setLayout(layout)
        return panel

    def refresh_devices(self):
        """刷新设备列表"""
        self.device_list.clear()

        # 获取可用的小组设备
        group_devices = self.module.get_available_groups()

        for device in group_devices:
            item = QListWidgetItem(f"{device.device_name} ({device.ip_address})")
            item.setData(Qt.UserRole, device.device_id)

            # 设置设备状态图标
            if device.status == "online":
                item.setBackground(QColor("#d4edda"))
            else:
                item.setBackground(QColor("#f8d7da"))

            self.device_list.addItem(item)

        # 更新按钮状态
        self.update_button_states()

    def on_device_selection_changed(self):
        """设备选择改变处理"""
        self.update_button_states()

    def update_button_states(self):
        """更新按钮状态"""
        has_selection = len(self.device_list.selectedItems()) > 0
        has_active_session = len(self.module.active_sessions) > 0

        self.start_btn.setEnabled(has_selection and not has_active_session)
        self.stop_btn.setEnabled(has_active_session)

    def start_collaboration(self):
        """开始协作"""
        selected_items = self.device_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请先选择一个小组设备")
            return

        device_id = selected_items[0].data(Qt.UserRole)
        device_name = selected_items[0].text()

        # 确认对话框
        reply = QMessageBox.question(
            self, "确认", f"确定要与设备 {device_name} 开始协作吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            session_id = self.module.start_collaboration(device_id)
            if session_id:
                QMessageBox.information(self, "成功", f"协作会话 {session_id} 已开始")
            else:
                QMessageBox.critical(self, "错误", "启动协作失败")

    def stop_collaboration(self):
        """停止协作"""
        if not self.module.active_sessions:
            QMessageBox.warning(self, "警告", "当前没有活跃的协作会话")
            return

        # 如果有多个会话，让用户选择
        if len(self.module.active_sessions) > 1:
            session_ids = list(self.module.active_sessions.keys())
            session_id, ok = QInputDialog.getItem(
                self, "选择会话", "请选择要停止的协作会话:",
                session_ids, 0, False
            )
            if not ok:
                return
        else:
            session_id = list(self.module.active_sessions.keys())[0]

        # 确认对话框
        reply = QMessageBox.question(
            self, "确认", f"确定要停止协作会话 {session_id} 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.module.stop_collaboration(session_id)
            QMessageBox.information(self, "成功", f"协作会话 {session_id} 已停止")

    def update_settings(self):
        """更新设置"""
        self.module.config["enable_bidirectional_sync"] = self.bidirectional_cb.isChecked()
        self.module.config["sync_mouse_events"] = self.mouse_sync_cb.isChecked()
        self.module.config["sync_keyboard_events"] = self.keyboard_sync_cb.isChecked()
        self.module.config["sync_interval_ms"] = self.interval_spin.value()

    def update_display(self):
        """更新显示"""
        # 更新协作状态
        if self.module.active_sessions:
            session = list(self.module.active_sessions.values())[0]
            self.status_label.setText("协作进行中")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: white;
                    border: 2px solid #28a745;
                    border-radius: 8px;
                    padding: 15px;
                    color: #28a745;
                }
            """)

            # 更新协作信息
            device_name = "未知设备"
            if session.group_device in self.module.available_devices:
                device_name = self.module.available_devices[session.group_device].device_name

            self.current_device_label.setText(device_name)
            self.start_time_label.setText(session.start_time.strftime("%H:%M:%S"))
            self.sync_count_label.setText(str(len(session.sync_events)))
        else:
            self.status_label.setText("未开始协作")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: white;
                    border: 2px solid #007acc;
                    border-radius: 8px;
                    padding: 15px;
                    color: #007acc;
                }
            """)

            self.current_device_label.setText("无")
            self.start_time_label.setText("无")
            self.sync_count_label.setText("0")

        # 更新会话表格
        self.update_sessions_table()

        # 更新按钮状态
        self.update_button_states()

    def update_sessions_table(self):
        """更新会话表格"""
        sessions = list(self.module.active_sessions.values())
        self.sessions_table.setRowCount(len(sessions))

        for row, session in enumerate(sessions):
            self.sessions_table.setItem(row, 0, QTableWidgetItem(session.session_id))

            # 获取设备名称
            device_name = session.group_device
            if session.group_device in self.module.available_devices:
                device_name = self.module.available_devices[session.group_device].device_name
            self.sessions_table.setItem(row, 1, QTableWidgetItem(device_name))

            # 开始时间
            start_time = session.start_time.strftime("%H:%M:%S")
            self.sessions_table.setItem(row, 2, QTableWidgetItem(start_time))

            # 状态
            status = "活跃" if session.is_active else "已停止"
            status_item = QTableWidgetItem(status)
            if session.is_active:
                status_item.setBackground(QColor("#d4edda"))
            else:
                status_item.setBackground(QColor("#f8d7da"))
            self.sessions_table.setItem(row, 3, status_item)

    def add_log_entry(self, message: str):
        """添加日志条目"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

        # 限制日志行数
        document = self.log_text.document()
        if document.blockCount() > 100:
            cursor = self.log_text.textCursor()
            cursor.movePosition(cursor.Start)
            cursor.select(cursor.BlockUnderCursor)
            cursor.removeSelectedText()
            cursor.deleteChar()  # 删除换行符

    def on_collaboration_started(self, session: CollaborationSession):
        """协作开始事件处理"""
        device_name = session.group_device
        if session.group_device in self.module.available_devices:
            device_name = self.module.available_devices[session.group_device].device_name

        self.add_log_entry(f"开始与设备 {device_name} 的协作")
        self.update_display()

    def on_collaboration_stopped(self, session_id: str):
        """协作停止事件处理"""
        self.add_log_entry(f"协作会话 {session_id} 已停止")
        self.update_display()

    def on_sync_event_received(self, event_data: Dict[str, Any]):
        """同步事件接收处理"""
        event_type = event_data.get("type", "unknown")
        self.add_log_entry(f"同步事件: {event_type}")
        self.update_display()

    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)
        self.refresh_devices()
        self.update_display()
