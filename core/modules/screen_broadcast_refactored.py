"""
重构后的屏幕广播模块 - 基于新的模块化架构
支持双模式：DLNA投屏和FFmpeg+MediaMTX广播
"""
import os
import sys
import subprocess
import threading
import time
import socket
import platform
import json
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
from .base_module import CommonModule

class ScreenBroadcastModule(CommonModule):
    """重构后的屏幕广播模块"""
    
    def __init__(self):
        super().__init__(
            module_name="screen_broadcast",
            display_name="屏幕广播",
            version="2.0.0"
        )
        
        # 广播相关属性
        self.broadcast_window = None
        self.is_broadcasting = False
        self.current_mode = "ffmpeg_mediamtx"  # ffmpeg_mediamtx, dlna
        self.broadcast_history = []
        
        # FFmpeg + MediaMTX 模式
        self.ffmpeg_process = None
        self.mediamtx_process = None
        self.mediamtx_port = 8554
        self.stream_key = "desktop"
        self.rtmp_url = None
        
        # DLNA 模式
        self.dlna_streamer = None
        self.dlna_devices = []
        self.casting_devices = []
        
        # 广播设置
        self.target_ip = ""
        self.target_port = 8080
        self.broadcast_quality = "medium"  # low, medium, high
        self.frame_rate = 25
        self.enable_audio = False
        
        # 质量预设
        self.quality_presets = {
            "low": {"bitrate": "1M", "scale": "1280:720", "preset": "ultrafast"},
            "medium": {"bitrate": "2M", "scale": "1920:1080", "preset": "fast"},
            "high": {"bitrate": "4M", "scale": "1920:1080", "preset": "medium"}
        }
        
        # 状态监控
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._check_broadcast_status)
        
        # 广播统计
        self.start_time = None
        self.bytes_sent = 0
        self.viewers_count = 0

    def get_widget(self):
        """获取模块的UI窗口"""
        return self.broadcast_window
    
    def _initialize_module(self) -> bool:
        """初始化屏幕广播模块"""
        try:
            # 检查依赖
            if not self._check_dependencies():
                self.logger.warning("某些依赖不可用，部分功能可能受限")
            
            # 创建广播窗口
            self.broadcast_window = ScreenBroadcastWindow(self)
            
            # 设置模块配置
            self.config.update({
                "default_mode": "ffmpeg_mediamtx",
                "mediamtx_port": 8554,
                "default_quality": "medium",
                "default_framerate": 25,
                "enable_audio": False,
                "auto_start_mediamtx": True,
                "broadcast_timeout": 3600  # 1小时
            })
            
            # 加载广播历史
            self._load_broadcast_history()
            
            # 初始化MediaMTX（如果启用）
            if self.config.get("auto_start_mediamtx", True):
                self._initialize_mediamtx()
            
            self.logger.info("屏幕广播模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"屏幕广播模块初始化失败: {str(e)}")
            return False
    
    def _cleanup_module(self):
        """清理屏幕广播模块资源"""
        try:
            # 停止广播
            if self.is_broadcasting:
                self.stop_broadcast()
            
            # 停止状态监控
            if self.status_timer.isActive():
                self.status_timer.stop()
            
            # 停止MediaMTX
            self._stop_mediamtx()
            
            # 保存广播历史
            self._save_broadcast_history()
            
            # 关闭广播窗口
            if self.broadcast_window:
                self.broadcast_window.close()
                self.broadcast_window = None
            
            self.logger.info("屏幕广播模块资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理屏幕广播模块资源时出错: {str(e)}")
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "start_broadcast":
            mode = data.get("mode", self.current_mode)
            settings = data.get("settings", {})
            self.start_broadcast(mode, settings)
        elif message_type == "stop_broadcast":
            self.stop_broadcast()
        elif message_type == "get_broadcast_status":
            self._send_broadcast_status(sender)
        elif message_type == "get_broadcast_url":
            self._send_broadcast_url(sender)
    
    def show(self):
        """显示屏幕广播窗口"""
        if self.broadcast_window:
            self.broadcast_window.show()
            self.broadcast_window.raise_()
            self.broadcast_window.activateWindow()
    
    def hide(self):
        """隐藏屏幕广播窗口"""
        if self.broadcast_window:
            self.broadcast_window.hide()
    
    def close(self):
        """关闭屏幕广播模块"""
        self.cleanup()
    
    def _check_dependencies(self) -> bool:
        """检查依赖项"""
        dependencies = {
            "ffmpeg": self._check_ffmpeg(),
            "mediamtx": self._check_mediamtx()
        }
        
        all_available = all(dependencies.values())
        
        for dep, available in dependencies.items():
            if available:
                self.logger.info(f"{dep} 可用")
            else:
                self.logger.warning(f"{dep} 不可用")
        
        return all_available
    
    def _check_ffmpeg(self) -> bool:
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(
                ["ffmpeg", "-version"], 
                capture_output=True, 
                text=True, 
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False
    
    def _check_mediamtx(self) -> bool:
        """检查MediaMTX是否可用"""
        try:
            # 检查是否有mediamtx可执行文件
            result = subprocess.run(
                ["mediamtx", "--help"], 
                capture_output=True, 
                text=True, 
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False
    
    def _initialize_mediamtx(self):
        """初始化MediaMTX服务器"""
        try:
            if self._check_mediamtx():
                self._start_mediamtx()
            else:
                self.logger.warning("MediaMTX不可用，将使用内置流媒体服务")
        except Exception as e:
            self.logger.error(f"初始化MediaMTX失败: {str(e)}")
    
    def _start_mediamtx(self):
        """启动MediaMTX服务器并等待其就绪"""
        try:
            if self.mediamtx_process and self.mediamtx_process.poll() is None:
                self.logger.info("MediaMTX已在运行")
                return

            config_content = self._generate_mediamtx_config()
            config_path = "mediamtx.yml"
            with open(config_path, 'w') as f:
                f.write(config_content)

            self.mediamtx_process = subprocess.Popen(
                ["mediamtx", config_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if platform.system() == "Windows" else 0
            )

            # 等待MediaMTX启动并监听端口
            max_wait_time = 10  # 最多等待10秒
            start_time = time.time()
            is_ready = False
            while time.time() - start_time < max_wait_time:
                try:
                    with socket.create_connection(("localhost", 1935), timeout=1):
                        is_ready = True
                        break
                except (ConnectionRefusedError, socket.timeout):
                    time.sleep(0.5)
            
            if is_ready:
                self.logger.info(f"MediaMTX已成功启动并监听端口: 1935")
            else:
                self.logger.error("MediaMTX启动超时或失败")
                self._stop_mediamtx() # 清理失败的进程

        except Exception as e:
            self.logger.error(f"启动MediaMTX时发生异常: {str(e)}")

    def _stop_mediamtx(self):
        """停止MediaMTX服务器"""
        try:
            if self.mediamtx_process:
                self.mediamtx_process.terminate()
                self.mediamtx_process.wait(timeout=5)
                self.mediamtx_process = None
                self.logger.info("MediaMTX已停止")
        except Exception as e:
            self.logger.error(f"停止MediaMTX失败: {str(e)}")
    
    def _generate_mediamtx_config(self) -> str:
        """生成MediaMTX配置"""
        return f"""
# MediaMTX configuration for screen broadcasting
rtspAddress: :{self.mediamtx_port}
rtmpAddress: :1935
hlsAddress: :8888
webrtcAddress: :8889

paths:
  {self.stream_key}:
    source: publisher
    publishUser: ""
    publishPass: ""
    readUser: ""
    readPass: ""
"""
    
    def start_broadcast(self, mode: str = None, settings: Dict[str, Any] = None) -> bool:
        """开始屏幕广播"""
        if self.is_broadcasting:
            self.logger.warning("广播已在进行中")
            return False
        
        try:
            if mode:
                self.current_mode = mode
            
            # 确保MediaMTX已启动
            if self.current_mode == "ffmpeg_mediamtx":
                self._initialize_mediamtx()

            if settings:
                self._apply_settings(settings)
            
            success = False
            
            if self.current_mode == "ffmpeg_mediamtx":
                success = self._start_ffmpeg_broadcast()
            elif self.current_mode == "dlna":
                success = self._start_dlna_broadcast()
            else:
                self.logger.error(f"不支持的广播模式: {self.current_mode}")
                return False
            
            if success:
                self.is_broadcasting = True
                self.start_time = time.time()
                
                # 启动状态监控
                self.status_timer.start(5000)  # 每5秒检查一次
                
                # 添加到历史记录
                self._add_to_history("started")
                
                # 通知其他模块
                self.broadcast_message("broadcast_started", {
                    "mode": self.current_mode,
                    "settings": self._get_current_settings(),
                    "timestamp": datetime.now().isoformat()
                })
                
                self.logger.info(f"屏幕广播已开始，模式: {self.current_mode}")
                return True
            else:
                self.logger.error("启动屏幕广播失败")
                return False
                
        except Exception as e:
            self.logger.error(f"启动屏幕广播失败: {str(e)}")
            return False
    
    def stop_broadcast(self) -> bool:
        """停止屏幕广播"""
        if not self.is_broadcasting:
            self.logger.warning("当前没有进行广播")
            return False
        
        try:
            if self.current_mode == "ffmpeg_mediamtx":
                self._stop_ffmpeg_broadcast()
            elif self.current_mode == "dlna":
                self._stop_dlna_broadcast()
            
            self.is_broadcasting = False
            
            # 停止状态监控
            self.status_timer.stop()
            
            # 计算广播时长
            duration = int(time.time() - self.start_time) if self.start_time else 0
            
            # 添加到历史记录
            self._add_to_history("stopped", duration)
            
            # 通知其他模块
            self.broadcast_message("broadcast_stopped", {
                "mode": self.current_mode,
                "duration": duration,
                "timestamp": datetime.now().isoformat()
            })
            
            self.logger.info(f"屏幕广播已停止，时长: {duration}秒")
            return True
            
        except Exception as e:
            self.logger.error(f"停止屏幕广播失败: {str(e)}")
            return False
    
    def _start_ffmpeg_broadcast(self) -> bool:
        """启动FFmpeg+MediaMTX广播"""
        try:
            # 构建FFmpeg命令
            command = self._build_ffmpeg_command()
            
            if not command:
                return False
            
            # 启动FFmpeg进程
            kwargs = {}
            if platform.system() == "Windows":
                kwargs["creationflags"] = subprocess.CREATE_NO_WINDOW
            else:
                kwargs["preexec_fn"] = os.setsid
            
            self.ffmpeg_process = subprocess.Popen(
                command,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                **kwargs
            )

            # 启动后台线程来记录FFmpeg的输出
            threading.Thread(target=self._log_ffmpeg_output, args=(self.ffmpeg_process.stdout, 'stdout'), daemon=True).start()
            threading.Thread(target=self._log_ffmpeg_output, args=(self.ffmpeg_process.stderr, 'stderr'), daemon=True).start()
            
            # 等待流建立
            time.sleep(3)
            
            if self.ffmpeg_process.poll() is None:
                self.logger.info("FFmpeg广播流已建立")
                return True
            else:
                # 读取并记录FFmpeg的错误输出
                error_output = self.ffmpeg_process.stderr.read().decode('utf-8', errors='ignore')
                self.logger.error(f"FFmpeg进程启动失败。错误详情: {error_output.strip()}")
                return False
                
        except Exception as e:
            self.logger.error(f"启动FFmpeg广播失败: {str(e)}")
            return False
    
    def _stop_ffmpeg_broadcast(self):
        """停止FFmpeg广播"""
        try:
            if self.ffmpeg_process:
                # 使用SIGINT优雅地请求终止，而不是强制杀死
                self.ffmpeg_process.send_signal(subprocess.signal.SIGINT)
                try:
                    self.ffmpeg_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.ffmpeg_process.kill() # 如果5秒后仍未退出，则强制终止
                self.ffmpeg_process = None
                self.logger.info("FFmpeg广播已停止")
        except Exception as e:
            self.logger.error(f"停止FFmpeg广播失败: {str(e)}")

    def _log_ffmpeg_output(self, stream, stream_name):
        """在后台线程中读取并记录FFmpeg的输出"""
        self.logger.info(f"Starting to log FFmpeg {stream_name}")
        try:
            for line in iter(stream.readline, b''):
                if not line:
                    break
                log_line = line.decode('utf-8', errors='ignore').strip()
                if log_line:
                    self.logger.debug(f"[FFmpeg-{stream_name}] {log_line}")
            stream.close()
        except Exception as e:
            self.logger.error(f"记录FFmpeg {stream_name} 输出时出错: {e}")
        self.logger.info(f"FFmpeg {stream_name} logging finished")

    def _start_dlna_broadcast(self) -> bool:
        """启动DLNA广播"""
        try:
            # 这里可以集成现有的DLNA模块
            from .screen_dlan_mod import ScreenDLAN
            
            if not self.dlna_streamer:
                self.dlna_streamer = ScreenDLAN()
            
            # 启动DLNA流
            self.dlna_streamer.start_casting()
            
            self.logger.info("DLNA广播已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"启动DLNA广播失败: {str(e)}")
            return False
    
    def _stop_dlna_broadcast(self):
        """停止DLNA广播"""
        try:
            if self.dlna_streamer:
                self.dlna_streamer.stop_casting()
                self.logger.info("DLNA广播已停止")
        except Exception as e:
            self.logger.error(f"停止DLNA广播失败: {str(e)}")
    
    def _build_ffmpeg_command(self) -> List[str]:
        """构建FFmpeg命令"""
        try:
            command = ["ffmpeg", "-report"]
            
            # 输入设置
            if platform.system() == "Windows":
                command.extend(["-f", "gdigrab", "-i", "desktop"])
            else:  # Linux
                command.extend(["-f", "x11grab", "-i", ":0.0"])
            
            # 帧率设置
            command.extend(["-framerate", str(self.frame_rate)])
            
            # 视频编码设置
            quality = self.quality_presets.get(self.broadcast_quality, self.quality_presets["medium"])
            command.extend([
                "-vf", f"scale={quality['scale']},format=yuv420p",
                "-c:v", "libx264",
                "-preset", quality["preset"],
                "-b:v", quality["bitrate"],
                "-maxrate", quality["bitrate"],
                "-bufsize", f"{int(quality['bitrate'][:-1]) * 2}M"
            ])
            
            # 音频设置
            if self.enable_audio:
                if platform.system() == "Windows":
                    command.extend(["-f", "dshow", "-i", "audio=virtual-audio-capturer"])
                else:
                    command.extend(["-f", "pulse", "-i", "default"])
                command.extend(["-c:a", "aac", "-b:a", "128k"])
            else:
                command.extend(["-an"])
            
            # 输出设置
            if self.target_ip:
                # 直接推流到目标地址
                rtmp_url = f"rtmp://{self.target_ip}:{self.target_port}/live/{self.stream_key}"
            else:
                # 推流到本地MediaMTX
                rtmp_url = f"rtmp://localhost:1935/{self.stream_key}"
            
            command.extend(["-f", "flv", rtmp_url])
            
            self.rtmp_url = rtmp_url
            return command
            
        except Exception as e:
            self.logger.error(f"构建FFmpeg命令失败: {str(e)}")
            return []
    
    def _apply_settings(self, settings: Dict[str, Any]):
        """应用广播设置"""
        if "quality" in settings:
            self.broadcast_quality = settings["quality"]
        if "framerate" in settings:
            self.frame_rate = settings["framerate"]
        if "audio" in settings:
            self.enable_audio = settings["audio"]
        if "target_ip" in settings:
            self.target_ip = settings["target_ip"]
        if "target_port" in settings:
            self.target_port = settings["target_port"]
    
    def _get_current_settings(self) -> Dict[str, Any]:
        """获取当前广播设置"""
        return {
            "mode": self.current_mode,
            "quality": self.broadcast_quality,
            "framerate": self.frame_rate,
            "audio": self.enable_audio,
            "target_ip": self.target_ip,
            "target_port": self.target_port
        }
    
    def _check_broadcast_status(self):
        """检查广播状态"""
        if not self.is_broadcasting:
            return
        
        try:
            if self.current_mode == "ffmpeg_mediamtx" and self.ffmpeg_process:
                if self.ffmpeg_process.poll() is not None:
                    self.logger.warning("FFmpeg进程意外终止")
                    self.stop_broadcast()
            
            # 更新统计信息
            if self.start_time:
                elapsed = int(time.time() - self.start_time)
                
                # 检查超时
                max_time = self.config.get("broadcast_timeout", 3600)
                if elapsed >= max_time:
                    self.logger.warning(f"达到最大广播时间 {max_time} 秒，自动停止")
                    self.stop_broadcast()
                
        except Exception as e:
            self.logger.error(f"检查广播状态失败: {str(e)}")
    
    def get_broadcast_url(self) -> str:
        """获取广播URL"""
        if not self.is_broadcasting:
            return None
        
        try:
            local_ip = self._get_local_ip()
            
            if self.current_mode == "ffmpeg_mediamtx":
                # 返回RTSP或HLS URL
                rtsp_url = f"rtsp://{local_ip}:{self.mediamtx_port}/{self.stream_key}"
                hls_url = f"http://{local_ip}:8888/{self.stream_key}/index.m3u8"
                return {"rtsp": rtsp_url, "hls": hls_url}
            elif self.current_mode == "dlna":
                if self.dlna_streamer:
                    return f"http://{local_ip}:{self.dlna_streamer.streamer.port}/stream.m3u8"
            
        except Exception as e:
            self.logger.error(f"获取广播URL失败: {str(e)}")
        
        return None
    
    def _get_local_ip(self) -> str:
        """获取本地IP地址"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("**************", 1))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            return "127.0.0.1"
    
    def get_broadcast_status(self) -> Dict[str, Any]:
        """获取广播状态"""
        status = {
            "is_broadcasting": self.is_broadcasting,
            "mode": self.current_mode,
            "settings": self._get_current_settings(),
            "start_time": self.start_time,
            "elapsed_time": int(time.time() - self.start_time) if self.start_time else 0,
            "viewers_count": self.viewers_count,
            "bytes_sent": self.bytes_sent
        }
        
        if self.is_broadcasting:
            status["broadcast_url"] = self.get_broadcast_url()
        
        return status
    
    def _send_broadcast_status(self, requester: str):
        """发送广播状态给请求者"""
        status = self.get_broadcast_status()
        self.send_message(requester, "broadcast_status", status)
    
    def _send_broadcast_url(self, requester: str):
        """发送广播URL给请求者"""
        url = self.get_broadcast_url()
        self.send_message(requester, "broadcast_url", {"url": url})
    
    def _add_to_history(self, status: str, duration: int = 0):
        """添加到广播历史"""
        record = {
            "mode": self.current_mode,
            "status": status,
            "start_time": datetime.fromtimestamp(self.start_time).isoformat() if self.start_time else "",
            "duration": duration,
            "settings": self._get_current_settings(),
            "timestamp": datetime.now().isoformat()
        }
        
        self.broadcast_history.append(record)
        
        # 限制历史记录数量
        if len(self.broadcast_history) > 100:
            self.broadcast_history = self.broadcast_history[-100:]
    
    def get_broadcast_history(self) -> List[Dict[str, Any]]:
        """获取广播历史"""
        return self.broadcast_history.copy()
    
    def _load_broadcast_history(self):
        """加载广播历史"""
        try:
            history_file = "broadcast_history.json"
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    self.broadcast_history = json.load(f)
        except Exception as e:
            self.logger.error(f"加载广播历史失败: {str(e)}")
            self.broadcast_history = []
    
    def _save_broadcast_history(self):
        """保存广播历史"""
        try:
            history_file = "broadcast_history.json"
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(self.broadcast_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存广播历史失败: {str(e)}")

class ScreenBroadcastWindow(QMainWindow):
    """屏幕广播窗口"""

    def __init__(self, module: ScreenBroadcastModule):
        super().__init__()
        self.module = module

        # 窗口设置
        self.setWindowTitle("屏幕广播工具")
        self.setGeometry(100, 100, 500, 600)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)

        # 创建UI
        self.setup_ui()

        # 连接信号
        self.setup_signals()

        # 定时器更新状态
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status_display)
        self.update_timer.start(1000)  # 每秒更新
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 广播模式选择
        mode_group = QGroupBox("广播模式")
        mode_layout = QHBoxLayout(mode_group)

        self.ffmpeg_radio = QRadioButton("FFmpeg + MediaMTX")
        self.ffmpeg_radio.setChecked(True)
        self.dlna_radio = QRadioButton("DLNA投屏")

        mode_layout.addWidget(self.ffmpeg_radio)
        mode_layout.addWidget(self.dlna_radio)

        main_layout.addWidget(mode_group)

        # 广播设置
        settings_group = QGroupBox("广播设置")
        settings_layout = QFormLayout(settings_group)

        # 目标地址
        self.target_ip_edit = QLineEdit()
        self.target_ip_edit.setPlaceholderText("留空使用本地MediaMTX服务器")
        settings_layout.addRow("目标IP:", self.target_ip_edit)

        # 目标端口
        self.target_port_edit = QLineEdit("1935")
        settings_layout.addRow("目标端口:", self.target_port_edit)

        # 视频质量
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["low", "medium", "high"])
        self.quality_combo.setCurrentText(self.module.broadcast_quality)
        settings_layout.addRow("视频质量:", self.quality_combo)

        # 帧率
        self.framerate_combo = QComboBox()
        self.framerate_combo.addItems(["15", "25", "30", "60"])
        self.framerate_combo.setCurrentText(str(self.module.frame_rate))
        settings_layout.addRow("帧率:", self.framerate_combo)

        # 音频
        self.audio_cb = QCheckBox("包含音频")
        self.audio_cb.setChecked(self.module.enable_audio)
        settings_layout.addRow("", self.audio_cb)

        main_layout.addWidget(settings_group)

        # 广播控制
        control_group = QGroupBox("广播控制")
        control_layout = QVBoxLayout(control_group)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.start_btn = QPushButton("开始广播")
        self.start_btn.setStyleSheet(self._get_button_style("#4CAF50"))
        self.start_btn.setMinimumHeight(40)
        button_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("停止广播")
        self.stop_btn.setStyleSheet(self._get_button_style("#F44336"))
        self.stop_btn.setMinimumHeight(40)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)

        control_layout.addLayout(button_layout)

        # 状态显示
        self.status_label = QLabel("准备就绪")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #666;
                background-color: #F5F5F5;
                border: 1px solid #DDD;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        control_layout.addWidget(self.status_label)

        # 广播时间
        self.time_label = QLabel("00:00:00")
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2E86AB;
            }
        """)
        control_layout.addWidget(self.time_label)

        main_layout.addWidget(control_group)

        # 广播信息
        info_group = QGroupBox("广播信息")
        info_layout = QFormLayout(info_group)

        self.url_label = QLabel("未广播")
        self.url_label.setWordWrap(True)
        self.url_label.setStyleSheet("font-family: monospace; font-size: 10px;")
        info_layout.addRow("广播地址:", self.url_label)

        self.viewers_label = QLabel("0")
        info_layout.addRow("观看人数:", self.viewers_label)

        # 复制URL按钮
        self.copy_url_btn = QPushButton("复制URL")
        self.copy_url_btn.setEnabled(False)
        info_layout.addRow("", self.copy_url_btn)

        main_layout.addWidget(info_group)

        # 广播历史
        history_group = QGroupBox("广播历史")
        history_layout = QVBoxLayout(history_group)

        self.history_list = QListWidget()
        self.history_list.setMaximumHeight(100)
        history_layout.addWidget(self.history_list)

        # 历史操作按钮
        history_buttons = QHBoxLayout()
        self.refresh_history_btn = QPushButton("刷新")
        self.clear_history_btn = QPushButton("清空")

        history_buttons.addWidget(self.refresh_history_btn)
        history_buttons.addWidget(self.clear_history_btn)
        history_buttons.addStretch()

        history_layout.addLayout(history_buttons)
        main_layout.addWidget(history_group)

        # 刷新历史
        self.refresh_history()

    def setup_signals(self):
        """设置信号连接"""
        # 广播控制
        self.start_btn.clicked.connect(self.start_broadcast)
        self.stop_btn.clicked.connect(self.stop_broadcast)

        # 设置变更
        self.ffmpeg_radio.toggled.connect(self.mode_changed)
        self.dlna_radio.toggled.connect(self.mode_changed)
        self.target_ip_edit.textChanged.connect(self.update_settings)
        self.target_port_edit.textChanged.connect(self.update_settings)
        self.quality_combo.currentTextChanged.connect(self.update_settings)
        self.framerate_combo.currentTextChanged.connect(self.update_settings)
        self.audio_cb.toggled.connect(self.update_settings)

        # 其他按钮
        self.copy_url_btn.clicked.connect(self.copy_broadcast_url)
        self.refresh_history_btn.clicked.connect(self.refresh_history)
        self.clear_history_btn.clicked.connect(self.clear_history)

    def _get_button_style(self, color: str) -> str:
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background-color: {color}DD;
            }}
            QPushButton:pressed {{
                background-color: {color}BB;
            }}
            QPushButton:disabled {{
                background-color: #CCCCCC;
                color: #666666;
            }}
        """

    def mode_changed(self):
        """广播模式改变"""
        if self.ffmpeg_radio.isChecked():
            self.module.current_mode = "ffmpeg_mediamtx"
        else:
            self.module.current_mode = "dlna"

        # 根据模式调整UI
        is_ffmpeg_mode = self.module.current_mode == "ffmpeg_mediamtx"
        self.target_ip_edit.setEnabled(is_ffmpeg_mode)
        self.target_port_edit.setEnabled(is_ffmpeg_mode)
        self.quality_combo.setEnabled(is_ffmpeg_mode)
        self.framerate_combo.setEnabled(is_ffmpeg_mode)

    def update_settings(self):
        """更新设置"""
        try:
            self.module.target_ip = self.target_ip_edit.text().strip()
            self.module.target_port = int(self.target_port_edit.text()) if self.target_port_edit.text() else 1935
            self.module.broadcast_quality = self.quality_combo.currentText()
            self.module.frame_rate = int(self.framerate_combo.currentText())
            self.module.enable_audio = self.audio_cb.isChecked()
        except ValueError:
            pass  # 忽略无效输入

    def start_broadcast(self):
        """开始广播"""
        # 更新设置
        self.update_settings()

        if self.module.start_broadcast():
            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.status_label.setText("正在广播...")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: white;
                    background-color: #4CAF50;
                    border: 1px solid #4CAF50;
                    border-radius: 5px;
                    padding: 10px;
                }
            """)

            # 禁用设置控件
            self._set_settings_enabled(False)

            # 启用复制URL按钮
            self.copy_url_btn.setEnabled(True)
        else:
            QMessageBox.critical(self, "错误", "启动广播失败，请检查设置和依赖项")

    def stop_broadcast(self):
        """停止广播"""
        if self.module.stop_broadcast():
            # 更新UI状态
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.status_label.setText("广播已停止")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: #666;
                    background-color: #F5F5F5;
                    border: 1px solid #DDD;
                    border-radius: 5px;
                    padding: 10px;
                }
            """)
            self.time_label.setText("00:00:00")
            self.url_label.setText("未广播")

            # 启用设置控件
            self._set_settings_enabled(True)

            # 禁用复制URL按钮
            self.copy_url_btn.setEnabled(False)

            # 刷新历史
            self.refresh_history()

    def _set_settings_enabled(self, enabled: bool):
        """设置控件启用状态"""
        self.ffmpeg_radio.setEnabled(enabled)
        self.dlna_radio.setEnabled(enabled)

        # 根据模式决定是否启用
        if enabled and self.module.current_mode == "ffmpeg_mediamtx":
            self.target_ip_edit.setEnabled(True)
            self.target_port_edit.setEnabled(True)
            self.quality_combo.setEnabled(True)
            self.framerate_combo.setEnabled(True)
        else:
            self.target_ip_edit.setEnabled(False)
            self.target_port_edit.setEnabled(False)
            self.quality_combo.setEnabled(False)
            self.framerate_combo.setEnabled(False)

        self.audio_cb.setEnabled(enabled)

    def update_status_display(self):
        """更新状态显示"""
        if self.module.is_broadcasting:
            # 更新时间显示
            if self.module.start_time:
                elapsed = int(time.time() - self.module.start_time)
                hours = elapsed // 3600
                minutes = (elapsed % 3600) // 60
                seconds = elapsed % 60
                time_str = f"{hours:02}:{minutes:02}:{seconds:02}"
                self.time_label.setText(time_str)

            # 更新URL显示
            url = self.module.get_broadcast_url()
            if url:
                if isinstance(url, dict):
                    # FFmpeg模式返回多个URL
                    url_text = f"RTSP: {url.get('rtsp', 'N/A')}\nHLS: {url.get('hls', 'N/A')}"
                else:
                    # DLNA模式返回单个URL
                    url_text = str(url)
                self.url_label.setText(url_text)

            # 更新观看人数（这里是模拟数据）
            self.viewers_label.setText(str(self.module.viewers_count))

    def copy_broadcast_url(self):
        """复制广播URL"""
        url = self.module.get_broadcast_url()
        if url:
            if isinstance(url, dict):
                # 复制RTSP URL
                url_text = url.get('rtsp', '')
            else:
                url_text = str(url)

            if url_text:
                clipboard = QApplication.clipboard()
                clipboard.setText(url_text)
                QMessageBox.information(self, "成功", "广播地址已复制到剪贴板")

    def refresh_history(self):
        """刷新广播历史"""
        self.history_list.clear()
        history = self.module.get_broadcast_history()

        for record in reversed(history[-10:]):  # 显示最近10条记录
            mode = record.get("mode", "未知")
            status = record.get("status", "未知")
            duration = record.get("duration", 0)
            timestamp = record.get("timestamp", "")

            # 格式化时长
            if duration > 0:
                hours = duration // 3600
                minutes = (duration % 3600) // 60
                seconds = duration % 60
                duration_str = f"{hours:02}:{minutes:02}:{seconds:02}"
            else:
                duration_str = "00:00:00"

            item_text = f"{mode} - {status} - {duration_str} - {timestamp[:19]}"
            self.history_list.addItem(item_text)

    def clear_history(self):
        """清空广播历史"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空广播历史吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.module.broadcast_history.clear()
            self.module._save_broadcast_history()
            self.refresh_history()

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.module.is_broadcasting:
            reply = QMessageBox.question(
                self, "确认", "广播正在进行中，确定要关闭窗口吗？\n广播将会停止。",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.module.stop_broadcast()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

# 为了兼容性，保留原始类名
ScreenBroadcast = ScreenBroadcastModule
