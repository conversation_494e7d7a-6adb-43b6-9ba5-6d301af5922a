"""
UI优化器测试
"""
import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui_optimizer import (
    UIOptimizer, UITheme, StyleSheetGenerator, ResponsiveLayout
)

class TestUITheme(unittest.TestCase):
    """测试UI主题类"""
    
    def test_get_color(self):
        """测试获取颜色"""
        # 测试存在的颜色
        primary_color = UITheme.get_color('primary')
        self.assertEqual(primary_color, '#007acc')
        
        # 测试不存在的颜色（应返回默认颜色）
        unknown_color = UITheme.get_color('unknown_color')
        self.assertEqual(unknown_color, '#007acc')  # 默认为primary
    
    def test_get_font(self):
        """测试获取字体"""
        from PyQt5.QtGui import QFont
        
        # 测试默认字体
        default_font = UITheme.get_font()
        self.assertIsInstance(default_font, QFont)
        self.assertEqual(default_font.pointSize(), 12)
        
        # 测试大字体
        large_font = UITheme.get_font('large')
        self.assertEqual(large_font.pointSize(), 14)
        
        # 测试等宽字体
        mono_font = UITheme.get_font('default', 'monospace')
        self.assertIn('Consolas', mono_font.family())
    
    def test_get_size(self):
        """测试获取尺寸"""
        # 测试存在的尺寸
        border_radius = UITheme.get_size('border_radius')
        self.assertEqual(border_radius, 4)
        
        # 测试不存在的尺寸（应返回0）
        unknown_size = UITheme.get_size('unknown_size')
        self.assertEqual(unknown_size, 0)

class TestStyleSheetGenerator(unittest.TestCase):
    """测试样式表生成器"""
    
    def test_generate_button_style(self):
        """测试生成按钮样式"""
        style = StyleSheetGenerator.generate_button_style()
        
        # 验证样式包含必要的CSS属性
        self.assertIn('QPushButton', style)
        self.assertIn('background-color', style)
        self.assertIn('color', style)
        self.assertIn('border', style)
        self.assertIn('border-radius', style)
        self.assertIn('padding', style)
        self.assertIn(':hover', style)
        self.assertIn(':pressed', style)
        self.assertIn(':disabled', style)
    
    def test_generate_button_style_with_custom_colors(self):
        """测试生成自定义颜色的按钮样式"""
        style = StyleSheetGenerator.generate_button_style(
            bg_color='#ff0000',
            text_color='#ffffff',
            hover_bg_color='#cc0000'
        )
        
        # 验证自定义颜色被应用
        self.assertIn('#ff0000', style)
        self.assertIn('#ffffff', style)
        self.assertIn('#cc0000', style)
    
    def test_generate_input_style(self):
        """测试生成输入框样式"""
        style = StyleSheetGenerator.generate_input_style()
        
        # 验证样式包含必要的CSS属性
        self.assertIn('QLineEdit', style)
        self.assertIn('QTextEdit', style)
        self.assertIn('QComboBox', style)
        self.assertIn('background-color', style)
        self.assertIn('border', style)
        self.assertIn(':focus', style)
    
    def test_generate_panel_style(self):
        """测试生成面板样式"""
        style = StyleSheetGenerator.generate_panel_style()
        
        # 验证样式包含必要的CSS属性
        self.assertIn('QGroupBox', style)
        self.assertIn('QFrame', style)
        self.assertIn('::title', style)
        self.assertIn('background-color', style)
        self.assertIn('border', style)
    
    def test_generate_table_style(self):
        """测试生成表格样式"""
        style = StyleSheetGenerator.generate_table_style()
        
        # 验证样式包含必要的CSS属性
        self.assertIn('QTableWidget', style)
        self.assertIn('QTableView', style)
        self.assertIn('QHeaderView', style)
        self.assertIn('alternate-background-color', style)
        self.assertIn('selection-background-color', style)

class TestResponsiveLayout(unittest.TestCase):
    """测试响应式布局管理器"""
    
    def test_get_screen_size_category(self):
        """测试获取屏幕尺寸类别"""
        # 测试超小屏幕
        category = ResponsiveLayout.get_screen_size_category(500)
        self.assertEqual(category, 'xs')
        
        # 测试小屏幕
        category = ResponsiveLayout.get_screen_size_category(700)
        self.assertEqual(category, 'sm')
        
        # 测试中等屏幕
        category = ResponsiveLayout.get_screen_size_category(1000)
        self.assertEqual(category, 'md')
        
        # 测试大屏幕
        category = ResponsiveLayout.get_screen_size_category(1300)
        self.assertEqual(category, 'lg')
        
        # 测试超大屏幕
        category = ResponsiveLayout.get_screen_size_category(1500)
        self.assertEqual(category, 'xl')
    
    def test_adjust_layout_for_screen_size(self):
        """测试根据屏幕尺寸调整布局"""
        from PyQt5.QtWidgets import QWidget, QVBoxLayout, QApplication
        
        # 创建QApplication实例（如果不存在）
        if not QApplication.instance():
            app = QApplication([])
        
        # 创建测试窗口
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # 测试小屏幕布局调整
        ResponsiveLayout.adjust_layout_for_screen_size(widget, 'xs')
        
        # 验证布局边距被调整
        margins = layout.contentsMargins()
        self.assertEqual(margins.left(), 4)
        self.assertEqual(margins.top(), 4)
        
        # 测试大屏幕布局调整
        ResponsiveLayout.adjust_layout_for_screen_size(widget, 'xl')
        
        # 验证布局边距被调整
        margins = layout.contentsMargins()
        self.assertEqual(margins.left(), 16)
        self.assertEqual(margins.top(), 16)

class TestUIOptimizer(unittest.TestCase):
    """测试UI优化器"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建QApplication实例（如果不存在）
        from PyQt5.QtWidgets import QApplication
        if not QApplication.instance():
            self.app = QApplication([])
        else:
            self.app = QApplication.instance()
        
        self.optimizer = UIOptimizer()
    
    def test_optimizer_initialization(self):
        """测试优化器初始化"""
        self.assertEqual(self.optimizer.current_theme, 'default')
        self.assertTrue(self.optimizer.responsive_enabled)
        self.assertTrue(self.optimizer.animation_enabled)
        self.assertFalse(self.optimizer.accessibility_enabled)
    
    @patch('PyQt5.QtWidgets.QApplication.setStyleSheet')
    @patch('PyQt5.QtWidgets.QApplication.setFont')
    def test_apply_theme_to_application(self, mock_set_font, mock_set_stylesheet):
        """测试为应用程序应用主题"""
        # 应用主题
        self.optimizer.apply_theme_to_application('dark')
        
        # 验证样式表和字体被设置
        mock_set_stylesheet.assert_called_once()
        mock_set_font.assert_called_once()
        
        # 验证主题已更改
        self.assertEqual(self.optimizer.current_theme, 'dark')
    
    def test_optimize_widget_ui(self):
        """测试优化单个窗口的UI"""
        from PyQt5.QtWidgets import QMainWindow, QPushButton, QVBoxLayout, QWidget
        
        # 创建测试窗口
        window = QMainWindow()
        central_widget = QWidget()
        layout = QVBoxLayout()
        button = QPushButton("测试按钮")
        layout.addWidget(button)
        central_widget.setLayout(layout)
        window.setCentralWidget(central_widget)
        
        # 优化UI
        self.optimizer.optimize_widget_ui(window)
        
        # 验证优化已应用（这里主要验证没有异常抛出）
        self.assertIsNotNone(window)
    
    def test_enable_responsive_design(self):
        """测试启用/禁用响应式设计"""
        # 启用响应式设计
        self.optimizer.enable_responsive_design(True)
        self.assertTrue(self.optimizer.responsive_enabled)
        
        # 禁用响应式设计
        self.optimizer.enable_responsive_design(False)
        self.assertFalse(self.optimizer.responsive_enabled)
    
    def test_enable_accessibility(self):
        """测试启用/禁用无障碍功能"""
        # 启用无障碍功能
        self.optimizer.enable_accessibility(True)
        self.assertTrue(self.optimizer.accessibility_enabled)
        
        # 禁用无障碍功能
        self.optimizer.enable_accessibility(False)
        self.assertFalse(self.optimizer.accessibility_enabled)
    
    def test_get_ui_metrics(self):
        """测试获取UI指标"""
        metrics = self.optimizer.get_ui_metrics()
        
        # 验证指标包含必要的信息
        self.assertIn('screen_count', metrics)
        self.assertIn('primary_screen_size', metrics)
        self.assertIn('widget_count', metrics)
        self.assertIn('visible_widgets', metrics)
        self.assertIn('current_theme', metrics)
        self.assertIn('responsive_enabled', metrics)
        self.assertIn('accessibility_enabled', metrics)
        
        # 验证指标值的类型
        self.assertIsInstance(metrics['screen_count'], int)
        self.assertIsInstance(metrics['widget_count'], int)
        self.assertIsInstance(metrics['visible_widgets'], int)
        self.assertIsInstance(metrics['responsive_enabled'], bool)
        self.assertIsInstance(metrics['accessibility_enabled'], bool)
    
    def test_optimize_font_sizes(self):
        """测试优化字体大小"""
        from PyQt5.QtWidgets import QWidget, QLabel, QVBoxLayout
        
        # 创建测试窗口
        widget = QWidget()
        layout = QVBoxLayout()
        label = QLabel("测试标签")
        layout.addWidget(label)
        widget.setLayout(layout)
        
        # 获取原始字体大小
        original_size = label.font().pointSize()
        
        # 优化字体大小（小屏幕）
        self.optimizer._optimize_font_sizes(widget, 'xs')
        
        # 验证字体大小被调整
        new_size = label.font().pointSize()
        self.assertNotEqual(original_size, new_size)
        self.assertEqual(new_size, UITheme.FONTS['small_size'])
    
    def test_optimize_widget_sizes(self):
        """测试优化控件尺寸"""
        from PyQt5.QtWidgets import QWidget, QPushButton, QVBoxLayout
        from PyQt5.QtCore import QSize
        
        # 创建测试窗口
        widget = QWidget()
        layout = QVBoxLayout()
        button = QPushButton("测试按钮")
        layout.addWidget(button)
        widget.setLayout(layout)
        
        # 优化控件尺寸（小屏幕）
        self.optimizer._optimize_widget_sizes(widget, 'xs')
        
        # 验证按钮最小尺寸被调整
        min_size = button.minimumSize()
        self.assertEqual(min_size, QSize(44, 44))
    
    def test_apply_accessibility_features(self):
        """测试应用无障碍功能"""
        from PyQt5.QtWidgets import QWidget, QPushButton, QVBoxLayout
        
        # 创建测试窗口
        widget = QWidget()
        layout = QVBoxLayout()
        button = QPushButton("测试按钮")
        layout.addWidget(button)
        widget.setLayout(layout)
        
        # 应用无障碍功能
        self.optimizer._apply_accessibility_features(widget)
        
        # 验证工具提示被设置
        self.assertIsNotNone(button.toolTip())

class TestUIIntegration(unittest.TestCase):
    """UI集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        from PyQt5.QtWidgets import QApplication
        if not QApplication.instance():
            self.app = QApplication([])
        else:
            self.app = QApplication.instance()
        
        self.optimizer = UIOptimizer()
    
    def test_theme_application_integration(self):
        """测试主题应用集成"""
        from PyQt5.QtWidgets import QMainWindow, QPushButton, QVBoxLayout, QWidget
        
        # 创建测试窗口
        window = QMainWindow()
        central_widget = QWidget()
        layout = QVBoxLayout()
        button = QPushButton("测试按钮")
        layout.addWidget(button)
        central_widget.setLayout(layout)
        window.setCentralWidget(central_widget)
        
        # 应用主题
        self.optimizer.apply_theme_to_application('default')
        
        # 优化窗口UI
        self.optimizer.optimize_widget_ui(window)
        
        # 验证主题已应用
        self.assertEqual(self.optimizer.current_theme, 'default')
    
    def test_responsive_design_integration(self):
        """测试响应式设计集成"""
        from PyQt5.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QPushButton
        
        # 创建测试窗口
        window = QMainWindow()
        central_widget = QWidget()
        layout = QVBoxLayout()
        button = QPushButton("测试按钮")
        layout.addWidget(button)
        central_widget.setLayout(layout)
        window.setCentralWidget(central_widget)
        
        # 启用响应式设计
        self.optimizer.enable_responsive_design(True)
        
        # 优化窗口UI
        self.optimizer.optimize_widget_ui(window)
        
        # 验证响应式设计已启用
        self.assertTrue(self.optimizer.responsive_enabled)
    
    def test_accessibility_integration(self):
        """测试无障碍功能集成"""
        from PyQt5.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QPushButton
        
        # 创建测试窗口
        window = QMainWindow()
        central_widget = QWidget()
        layout = QVBoxLayout()
        button = QPushButton("测试按钮")
        layout.addWidget(button)
        central_widget.setLayout(layout)
        window.setCentralWidget(central_widget)
        
        # 启用无障碍功能
        self.optimizer.enable_accessibility(True)
        
        # 优化窗口UI
        self.optimizer.optimize_widget_ui(window)
        
        # 验证无障碍功能已启用
        self.assertTrue(self.optimizer.accessibility_enabled)
    
    def test_performance_with_multiple_widgets(self):
        """测试多窗口性能"""
        from PyQt5.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QPushButton
        import time
        
        # 创建多个测试窗口
        windows = []
        for i in range(10):
            window = QMainWindow()
            central_widget = QWidget()
            layout = QVBoxLayout()
            
            # 添加多个控件
            for j in range(20):
                button = QPushButton(f"按钮 {i}-{j}")
                layout.addWidget(button)
            
            central_widget.setLayout(layout)
            window.setCentralWidget(central_widget)
            windows.append(window)
        
        # 测量优化时间
        start_time = time.time()
        
        for window in windows:
            self.optimizer.optimize_widget_ui(window)
        
        end_time = time.time()
        optimization_time = end_time - start_time
        
        # 验证优化时间合理（应在1秒内）
        self.assertLess(optimization_time, 1.0, f"UI优化时间过长: {optimization_time:.2f}秒")

if __name__ == '__main__':
    # 运行UI测试
    unittest.main(verbosity=2)
