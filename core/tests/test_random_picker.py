"""
随机点名模块测试
"""
import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.random_picker import RandomPickerModule, Student, Group, PickRecord

class TestStudent(unittest.TestCase):
    """测试学生类"""
    
    def test_student_creation(self):
        """测试学生创建"""
        student = Student("student1", "张三", "zhangsan", "group1")
        
        self.assertEqual(student.student_id, "student1")
        self.assertEqual(student.name, "张三")
        self.assertEqual(student.username, "zhangsan")
        self.assertEqual(student.group_id, "group1")
        self.assertEqual(student.pick_count, 0)
        self.assertIsNone(student.last_picked)
    
    def test_student_to_dict(self):
        """测试学生转换为字典"""
        student = Student("student1", "张三", "zhangsan", "group1")
        student.pick_count = 2
        student.last_picked = datetime.now()
        
        student_dict = student.to_dict()
        
        self.assertIn("student_id", student_dict)
        self.assertIn("name", student_dict)
        self.assertIn("username", student_dict)
        self.assertIn("group_id", student_dict)
        self.assertIn("pick_count", student_dict)
        self.assertIn("last_picked", student_dict)
    
    def test_student_from_dict(self):
        """测试从字典创建学生"""
        data = {
            "student_id": "student1",
            "name": "张三",
            "username": "zhangsan",
            "group_id": "group1",
            "pick_count": 2,
            "last_picked": datetime.now().isoformat()
        }
        
        student = Student.from_dict(data)
        
        self.assertEqual(student.student_id, "student1")
        self.assertEqual(student.name, "张三")
        self.assertEqual(student.pick_count, 2)
        self.assertIsNotNone(student.last_picked)

class TestGroup(unittest.TestCase):
    """测试小组类"""
    
    def test_group_creation(self):
        """测试小组创建"""
        students = [
            Student("student1", "张三", "zhangsan"),
            Student("student2", "李四", "lisi")
        ]
        group = Group("group1", "第一组", students)
        
        self.assertEqual(group.group_id, "group1")
        self.assertEqual(group.name, "第一组")
        self.assertEqual(len(group.members), 2)
        self.assertEqual(group.pick_count, 0)
        self.assertIsNone(group.last_picked)

class TestPickRecord(unittest.TestCase):
    """测试点名记录类"""
    
    def test_record_creation(self):
        """测试记录创建"""
        record = PickRecord("student", "student1", "张三", "course1")
        
        self.assertEqual(record.pick_type, "student")
        self.assertEqual(record.target_id, "student1")
        self.assertEqual(record.target_name, "张三")
        self.assertEqual(record.course_id, "course1")
        self.assertIsInstance(record.timestamp, datetime)
    
    def test_record_to_dict(self):
        """测试记录转换为字典"""
        record = PickRecord("student", "student1", "张三", "course1")
        record_dict = record.to_dict()
        
        self.assertIn("pick_type", record_dict)
        self.assertIn("target_id", record_dict)
        self.assertIn("target_name", record_dict)
        self.assertIn("course_id", record_dict)
        self.assertIn("timestamp", record_dict)

class TestRandomPickerModule(unittest.TestCase):
    """测试随机点名模块"""
    
    def setUp(self):
        """设置测试环境"""
        # Mock认证管理器
        self.mock_auth_manager = Mock()
        self.mock_auth_manager.is_authenticated.return_value = True
        self.mock_auth_manager.get_current_user.return_value = {
            "id": "teacher1",
            "name": "测试教师",
            "role": "teacher"
        }
        
        # Mock API客户端
        self.mock_api_client = Mock()
        
        # 创建随机点名模块
        self.picker = RandomPickerModule()
        self.picker.auth_manager = self.mock_auth_manager
        self.picker.api_client = self.mock_api_client
    
    @patch('modules.random_picker.RandomPickerWindow')
    def test_module_initialization(self, mock_window):
        """测试模块初始化"""
        # Mock API响应
        self.mock_api_client.get.return_value = {
            'success': True,
            'courses': [
                {'id': 'course1', 'name': '数学课'},
                {'id': 'course2', 'name': '语文课'}
            ]
        }
        
        # 初始化模块
        result = self.picker._initialize_module()
        
        # 验证初始化成功
        self.assertTrue(result)
        self.assertIsNotNone(self.picker.picker_window)
        self.assertEqual(len(self.picker.courses), 2)
    
    def test_module_initialization_without_auth(self):
        """测试未认证时的模块初始化"""
        # 设置未认证状态
        self.mock_auth_manager.is_authenticated.return_value = False
        
        # 初始化模块
        result = self.picker._initialize_module()
        
        # 验证初始化失败
        self.assertFalse(result)
    
    def test_set_current_course(self):
        """测试设置当前课程"""
        # Mock API响应
        self.mock_api_client.get.side_effect = [
            {
                'success': True,
                'students': [
                    {'id': 'student1', 'name': '张三', 'username': 'zhangsan'},
                    {'id': 'student2', 'name': '李四', 'username': 'lisi'}
                ]
            },
            {
                'success': True,
                'groups': [
                    {'id': 'group1', 'name': '第一组', 'member_ids': ['student1']},
                    {'id': 'group2', 'name': '第二组', 'member_ids': ['student2']}
                ]
            }
        ]
        
        # 设置当前课程
        self.picker.set_current_course("course1")
        
        # 验证课程设置成功
        self.assertEqual(self.picker.current_course_id, "course1")
        self.assertEqual(len(self.picker.students), 2)
        self.assertEqual(len(self.picker.groups), 2)
    
    def test_pick_random_student(self):
        """测试随机选择学生"""
        # 添加测试学生
        student1 = Student("student1", "张三", "zhangsan")
        student2 = Student("student2", "李四", "lisi")
        self.picker.students = {"student1": student1, "student2": student2}
        self.picker.current_course_id = "course1"
        
        # 随机选择学生
        selected_student = self.picker.pick_random_student()
        
        # 验证选择结果
        self.assertIsNotNone(selected_student)
        self.assertIn(selected_student.student_id, self.picker.students)
        self.assertEqual(selected_student.pick_count, 1)
        self.assertIsNotNone(selected_student.last_picked)
        
        # 验证记录已添加
        self.assertEqual(len(self.picker.pick_records), 1)
        self.assertEqual(self.picker.pick_records[0].pick_type, "student")
    
    def test_pick_random_group(self):
        """测试随机选择小组"""
        # 添加测试小组
        students = [Student("student1", "张三", "zhangsan")]
        group1 = Group("group1", "第一组", students)
        group2 = Group("group2", "第二组", students)
        self.picker.groups = {"group1": group1, "group2": group2}
        self.picker.current_course_id = "course1"
        
        # 随机选择小组
        selected_group = self.picker.pick_random_group()
        
        # 验证选择结果
        self.assertIsNotNone(selected_group)
        self.assertIn(selected_group.group_id, self.picker.groups)
        self.assertEqual(selected_group.pick_count, 1)
        self.assertIsNotNone(selected_group.last_picked)
        
        # 验证记录已添加
        self.assertEqual(len(self.picker.pick_records), 1)
        self.assertEqual(self.picker.pick_records[0].pick_type, "group")
    
    def test_get_available_students(self):
        """测试获取可选择的学生列表"""
        # 添加测试学生
        student1 = Student("student1", "张三", "zhangsan")
        student2 = Student("student2", "李四", "lisi")
        
        # 设置一个学生最近被点名
        student1.last_picked = datetime.now() - timedelta(minutes=30)
        student2.last_picked = datetime.now() - timedelta(hours=2)
        
        self.picker.students = {"student1": student1, "student2": student2}
        self.picker.config["exclude_recent_picks"] = True
        self.picker.config["recent_pick_hours"] = 1
        
        # 获取可选择的学生
        available = self.picker.get_available_students()
        
        # 验证结果（只有student2可选择）
        self.assertEqual(len(available), 1)
        self.assertEqual(available[0].student_id, "student2")
    
    def test_weighted_random_choice(self):
        """测试加权随机选择"""
        # 创建测试学生，设置不同的点名次数
        student1 = Student("student1", "张三", "zhangsan")
        student1.pick_count = 0
        
        student2 = Student("student2", "李四", "lisi")
        student2.pick_count = 2
        
        students = [student1, student2]
        
        # 多次测试加权选择（点名次数少的应该更容易被选中）
        selections = []
        for _ in range(100):
            selected = self.picker.weighted_random_choice(students)
            selections.append(selected.student_id)
        
        # 验证student1被选中的次数应该更多
        student1_count = selections.count("student1")
        student2_count = selections.count("student2")
        self.assertGreater(student1_count, student2_count)
    
    def test_pick_statistics(self):
        """测试点名统计"""
        # 添加测试数据
        student1 = Student("student1", "张三", "zhangsan")
        student2 = Student("student2", "李四", "lisi")
        self.picker.students = {"student1": student1, "student2": student2}
        
        group1 = Group("group1", "第一组", [student1])
        self.picker.groups = {"group1": group1}
        
        self.picker.current_course_id = "course1"
        
        # 添加点名记录
        record1 = PickRecord("student", "student1", "张三", "course1")
        record2 = PickRecord("group", "group1", "第一组", "course1")
        self.picker.pick_records = [record1, record2]
        
        # 获取统计信息
        stats = self.picker.get_pick_statistics()
        
        # 验证统计结果
        self.assertEqual(stats["total_picks"], 2)
        self.assertEqual(stats["student_picks"], 1)
        self.assertEqual(stats["group_picks"], 1)
        self.assertEqual(stats["students_picked"], 1)
        self.assertEqual(stats["groups_picked"], 1)
        self.assertEqual(stats["total_students"], 2)
        self.assertEqual(stats["total_groups"], 1)
        self.assertEqual(stats["student_coverage"], 0.5)
        self.assertEqual(stats["group_coverage"], 1.0)
    
    def test_message_handling(self):
        """测试消息处理"""
        # 设置测试数据
        self.picker.current_course_id = "course1"
        student = Student("student1", "张三", "zhangsan")
        self.picker.students = {"student1": student}
        
        # 测试学生点名消息
        self.picker._handle_message("test_sender", "pick_student", {"course_id": "course1"})
        
        # 验证学生被点名
        self.assertEqual(student.pick_count, 1)
        
        # 测试获取统计消息
        self.picker._handle_message("test_sender", "get_pick_stats", {})
        
        # 测试显示界面消息
        self.picker._handle_message("test_sender", "show_picker", {})

if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
