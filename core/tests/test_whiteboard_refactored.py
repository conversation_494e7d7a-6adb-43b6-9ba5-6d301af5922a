"""
白板模块单元测试
"""
import unittest
import sys
import os
import tempfile
import json
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QPoint
from PyQt5.QtGui import QColor

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.whiteboard_refactored import WhiteboardModule, WhiteboardWindow

class TestWhiteboardModule(unittest.TestCase):
    """白板模块测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.module = WhiteboardModule()
        
        # 模拟通信管理器
        self.module.comm_manager = Mock()
        self.module.message_bus = Mock()
        
        # 初始化模块
        self.module.initialize()
    
    def tearDown(self):
        """每个测试后的清理"""
        if self.module:
            self.module.cleanup()
    
    def test_module_initialization(self):
        """测试模块初始化"""
        self.assertTrue(self.module.is_initialized)
        self.assertEqual(self.module.module_name, "whiteboard")
        self.assertEqual(self.module.display_name, "电子白板")
        self.assertIsNotNone(self.module.whiteboard_window)
    
    def test_shape_management(self):
        """测试形状管理"""
        # 测试添加形状
        shape = {
            "shape": "line",
            "start": {"x": 10, "y": 10},
            "end": {"x": 50, "y": 50},
            "color": "#FF0000",
            "fine": 5
        }
        
        self.module.shapes.append(shape)
        self.assertEqual(len(self.module.shapes), 1)
        self.assertEqual(self.module.shapes[0]["shape"], "line")
    
    def test_clear_board(self):
        """测试清空白板"""
        # 添加一些形状
        self.module.shapes = [
            {"shape": "line", "start": {"x": 0, "y": 0}, "end": {"x": 10, "y": 10}},
            {"shape": "circle", "start": {"x": 20, "y": 20}, "end": {"x": 30, "y": 30}}
        ]
        
        # 清空白板
        self.module.clear_board()
        
        # 验证形状已清空
        self.assertEqual(len(self.module.shapes), 0)
    
    def test_save_and_load_board(self):
        """测试保存和加载白板"""
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name
        
        try:
            # 添加测试形状
            test_shape = {
                "shape": "rectangle",
                "start": {"x": 10, "y": 10},
                "end": {"x": 50, "y": 50},
                "color": "#00FF00",
                "fine": 3
            }
            self.module.shapes.append(test_shape)
            
            # 保存白板
            saved_file = self.module.save_board(temp_file)
            self.assertEqual(saved_file, temp_file)
            
            # 清空当前形状
            self.module.shapes.clear()
            self.assertEqual(len(self.module.shapes), 0)
            
            # 加载白板
            result = self.module.load_board(temp_file)
            self.assertTrue(result)
            self.assertEqual(len(self.module.shapes), 1)
            self.assertEqual(self.module.shapes[0]["shape"], "rectangle")
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    def test_recording_functionality(self):
        """测试录制功能"""
        # 模拟subprocess
        with patch('subprocess.Popen') as mock_popen:
            mock_process = Mock()
            mock_popen.return_value = mock_process
            
            # 测试开始录制
            self.module.start_recording()
            self.assertTrue(self.module.is_recording)
            self.assertIsNotNone(self.module.output_file)
            
            # 测试停止录制
            self.module.stop_recording()
            self.assertFalse(self.module.is_recording)
    
    def test_message_handling(self):
        """测试消息处理"""
        # 测试PPT控制消息
        ppt_data = {"action": "next_slide"}
        with patch.object(self.module.ppt, 'next_slide') as mock_next:
            self.module.handle_ppt_control(ppt_data)
            mock_next.assert_called_once()
        
        # 测试清空白板消息
        self.module.shapes = [{"shape": "test"}]
        self.module._handle_message("test_sender", "clear_board", {})
        self.assertEqual(len(self.module.shapes), 0)
    
    def test_auto_save(self):
        """测试自动保存功能"""
        # 添加形状
        self.module.shapes = [{"shape": "test", "data": "test_data"}]
        
        # 模拟自动保存
        with patch.object(self.module, 'save_board') as mock_save:
            self.module._auto_save()
            mock_save.assert_called_once()

class TestWhiteboardWindow(unittest.TestCase):
    """白板窗口测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.module = WhiteboardModule()
        self.module.comm_manager = Mock()
        self.module.message_bus = Mock()
        self.module.initialize()
        self.window = self.module.whiteboard_window
    
    def tearDown(self):
        """每个测试后的清理"""
        if self.module:
            self.module.cleanup()
    
    def test_window_initialization(self):
        """测试窗口初始化"""
        self.assertIsNotNone(self.window)
        self.assertIsNotNone(self.window.pixmap)
        self.assertIsNotNone(self.window.draw_menu)
        self.assertIsNotNone(self.window.ppt_menu)
    
    def test_pen_settings(self):
        """测试画笔设置"""
        # 测试设置画笔粗细
        self.window.set_pen_width(10)
        self.assertEqual(self.module.fine, 10)
        
        # 测试设置形状
        self.window.set_shape("circle")
        self.assertEqual(self.module.current_shape, "circle")
        
        # 测试设置颜色
        test_color = QColor(0, 255, 0)
        self.module.pen_color = test_color
        self.assertEqual(self.module.pen_color, test_color)
    
    def test_drawing_operations(self):
        """测试绘图操作"""
        # 测试撤销功能
        initial_shapes = [
            {"shape": "line", "start": {"x": 0, "y": 0}, "end": {"x": 10, "y": 10}},
            {"shape": "circle", "start": {"x": 20, "y": 20}, "end": {"x": 30, "y": 30}}
        ]
        self.module.shapes = initial_shapes.copy()
        
        self.window.undo_last_drawing()
        self.assertEqual(len(self.module.shapes), 1)
        self.assertEqual(len(self.module.redo_stack), 1)
        
        # 测试清空功能
        self.window.clear_all()
        self.assertEqual(len(self.module.shapes), 0)
        self.assertEqual(len(self.module.undo_stack), 0)
        self.assertEqual(len(self.module.redo_stack), 0)
    
    def test_file_operations(self):
        """测试文件操作"""
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name
        
        try:
            # 添加测试数据
            test_shapes = [
                {"shape": "line", "start": {"x": 0, "y": 0}, "end": {"x": 10, "y": 10}},
                {"shape": "circle", "start": {"x": 20, "y": 20}, "end": {"x": 30, "y": 30}}
            ]
            self.module.shapes = test_shapes
            
            # 测试保存
            result = self.window.save_content(temp_file)
            self.assertEqual(result, temp_file)
            self.assertTrue(os.path.exists(temp_file))
            
            # 清空并测试加载
            self.module.shapes.clear()
            result = self.window.load_content(temp_file)
            self.assertTrue(result)
            self.assertEqual(len(self.module.shapes), 2)
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    def test_shape_drawing(self):
        """测试形状绘制"""
        # 测试绘制不同类型的形状
        shapes_to_test = [
            {
                "shape": "line",
                "start": {"x": 10, "y": 10},
                "end": {"x": 50, "y": 50},
                "color": "#FF0000",
                "fine": 5
            },
            {
                "shape": "rectangle",
                "start": {"x": 20, "y": 20},
                "end": {"x": 60, "y": 60},
                "color": "#00FF00",
                "fine": 3
            },
            {
                "shape": "circle",
                "start": {"x": 30, "y": 30},
                "end": {"x": 70, "y": 70},
                "color": "#0000FF",
                "fine": 7
            }
        ]
        
        # 模拟QPainter
        with patch('PyQt5.QtGui.QPainter') as mock_painter:
            mock_painter_instance = Mock()
            mock_painter.return_value = mock_painter_instance
            
            for shape in shapes_to_test:
                self.window.draw_shape(mock_painter_instance, shape)
                # 验证绘制方法被调用
                self.assertTrue(mock_painter_instance.setPen.called)

if __name__ == '__main__':
    unittest.main()
