#!/usr/bin/env python3
"""
字体检查工具 - 检查系统可用的中文字体
"""
import sys
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *

def check_available_fonts():
    """检查系统可用字体"""
    app = QApplication(sys.argv)
    
    # 获取字体数据库
    font_db = QFontDatabase()
    
    # 常用中文字体列表
    chinese_fonts = [
        "Microsoft YaHei",
        "Microsoft YaHei UI", 
        "SimHei",
        "SimSun",
        "KaiTi",
        "FangSong",
        "Arial Unicode MS",
        "DejaVu Sans",
        "Noto Sans CJK SC",
        "Source Han Sans SC",
        "PingFang SC"
    ]
    
    print("=" * 60)
    print("系统字体检查")
    print("=" * 60)
    
    print("\n所有可用字体族:")
    all_families = font_db.families()
    for i, family in enumerate(all_families[:20]):  # 只显示前20个
        print(f"  {i+1:2d}. {family}")
    print(f"  ... 总共 {len(all_families)} 个字体族")
    
    print(f"\n中文字体检查:")
    available_chinese = []
    
    for font_name in chinese_fonts:
        # 创建字体对象
        font = QFont(font_name)
        font_info = QFontInfo(font)
        actual_family = font_info.family()
        
        if actual_family == font_name:
            status = "✓ 可用"
            available_chinese.append(font_name)
        else:
            status = f"✗ 不可用 (替代: {actual_family})"
        
        print(f"  {font_name:<20} {status}")
    
    print(f"\n推荐字体设置:")
    if "Microsoft YaHei" in available_chinese:
        print("  推荐使用: Microsoft YaHei")
    elif "SimHei" in available_chinese:
        print("  推荐使用: SimHei")
    elif available_chinese:
        print(f"  推荐使用: {available_chinese[0]}")
    else:
        print("  警告: 未找到合适的中文字体")
    
    return available_chinese

def test_font_rendering():
    """测试字体渲染效果"""
    app = QApplication.instance()
    if not app:
        app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = QWidget()
    window.setWindowTitle("字体渲染测试")
    window.resize(500, 400)
    
    layout = QVBoxLayout(window)
    
    # 测试文本
    test_text = "智慧课堂教学工具 - 用户名 密码 登录 取消"
    
    # 测试不同字体
    fonts_to_test = [
        ("Microsoft YaHei", 12),
        ("SimHei", 12),
        ("SimSun", 12),
        ("Arial", 12),
        ("默认字体", 12)
    ]
    
    for font_name, size in fonts_to_test:
        label = QLabel(f"{font_name}: {test_text}")
        
        if font_name != "默认字体":
            font = QFont(font_name, size)
            label.setFont(font)
        
        label.setStyleSheet("padding: 5px; border: 1px solid #ccc; margin: 2px;")
        layout.addWidget(label)
    
    # 添加关闭按钮
    close_btn = QPushButton("关闭")
    close_btn.clicked.connect(window.close)
    layout.addWidget(close_btn)
    
    window.show()
    return window

def main():
    """主函数"""
    print("字体检查和测试工具")
    
    # 检查可用字体
    available_fonts = check_available_fonts()
    
    # 询问是否要显示渲染测试
    print(f"\n是否要显示字体渲染测试窗口? (y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', '是', '']:
            window = test_font_rendering()
            app = QApplication.instance()
            app.exec_()
    except (EOFError, KeyboardInterrupt):
        print("跳过渲染测试")
    
    print("\n字体检查完成。")
    return 0

if __name__ == "__main__":
    sys.exit(main())
