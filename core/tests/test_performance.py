"""
性能测试
验证系统性能优化效果和稳定性
"""
import sys
import os
import unittest
import time
import threading
from unittest.mock import Mock, patch, MagicMock
import psutil

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from performance_optimizer import (
    PerformanceOptimizer, PerformanceMetrics, NetworkOptimizer, MemoryOptimizer
)
from module_manager import ModuleManager
from auth_manager import AuthManager
from communication_manager import CommunicationManager

class TestPerformanceMetrics(unittest.TestCase):
    """测试性能指标类"""
    
    def setUp(self):
        """设置测试环境"""
        self.metrics = PerformanceMetrics()
    
    def test_metrics_initialization(self):
        """测试指标初始化"""
        self.assertEqual(len(self.metrics.cpu_usage_history), 0)
        self.assertEqual(len(self.metrics.memory_usage_history), 0)
        self.assertEqual(len(self.metrics.network_io_history), 0)
        self.assertIsInstance(self.metrics.module_performance, dict)
    
    @patch('psutil.cpu_percent')
    @patch('psutil.virtual_memory')
    @patch('psutil.net_io_counters')
    def test_update_system_metrics(self, mock_net_io, mock_memory, mock_cpu):
        """测试系统指标更新"""
        # Mock系统指标
        mock_cpu.return_value = 50.0
        mock_memory.return_value = Mock(percent=60.0)
        mock_net_io.return_value = Mock(
            bytes_sent=1000000,
            bytes_recv=2000000,
            packets_sent=1000,
            packets_recv=2000
        )
        
        # 更新指标
        self.metrics.update_system_metrics()
        
        # 验证指标已更新
        self.assertEqual(len(self.metrics.cpu_usage_history), 1)
        self.assertEqual(len(self.metrics.memory_usage_history), 1)
        self.assertEqual(len(self.metrics.network_io_history), 1)
        
        self.assertEqual(self.metrics.cpu_usage_history[0], 50.0)
        self.assertEqual(self.metrics.memory_usage_history[0], 60.0)
    
    def test_average_cpu_usage(self):
        """测试平均CPU使用率计算"""
        # 添加测试数据
        self.metrics.cpu_usage_history = [10.0, 20.0, 30.0, 40.0, 50.0]
        
        # 计算平均值
        avg = self.metrics.get_average_cpu_usage()
        self.assertEqual(avg, 30.0)  # (10+20+30+40+50)/5 = 30
    
    def test_network_throughput(self):
        """测试网络吞吐量计算"""
        # 添加测试数据
        self.metrics.network_io_history = [
            {'bytes_sent': 1000, 'bytes_recv': 2000, 'packets_sent': 10, 'packets_recv': 20},
            {'bytes_sent': 2000, 'bytes_recv': 4000, 'packets_sent': 20, 'packets_recv': 40}
        ]
        
        # 计算吞吐量
        throughput = self.metrics.get_network_throughput()
        
        # 验证计算结果 (差值/时间间隔/1024)
        expected_upload = (2000 - 1000) / 10 / 1024  # KB/s
        expected_download = (4000 - 2000) / 10 / 1024  # KB/s
        
        self.assertAlmostEqual(throughput['upload'], expected_upload, places=3)
        self.assertAlmostEqual(throughput['download'], expected_download, places=3)

class TestNetworkOptimizer(unittest.TestCase):
    """测试网络优化器"""
    
    def setUp(self):
        """设置测试环境"""
        self.optimizer = NetworkOptimizer()
    
    def test_optimizer_initialization(self):
        """测试优化器初始化"""
        self.assertEqual(self.optimizer.udp_buffer_size, 65536)
        self.assertEqual(self.optimizer.tcp_buffer_size, 131072)
        self.assertEqual(self.optimizer.discovery_interval, 5.0)
        self.assertEqual(self.optimizer.heartbeat_interval, 30.0)
    
    def test_optimize_udp_discovery_few_devices(self):
        """测试UDP发现优化 - 少量设备"""
        # Mock UDP模块
        mock_udp_module = Mock()
        mock_udp_module.discovered_devices = {'device1': Mock(), 'device2': Mock()}
        
        # 执行优化
        result = self.optimizer.optimize_udp_discovery(mock_udp_module)
        
        # 验证优化结果
        self.assertIn('discovery_interval', result)
        self.assertEqual(result['discovery_interval'], 3.0)  # 设备少，间隔短
    
    def test_optimize_udp_discovery_many_devices(self):
        """测试UDP发现优化 - 大量设备"""
        # Mock UDP模块
        mock_udp_module = Mock()
        mock_udp_module.discovered_devices = {f'device{i}': Mock() for i in range(25)}
        
        # 执行优化
        result = self.optimizer.optimize_udp_discovery(mock_udp_module)
        
        # 验证优化结果
        self.assertIn('discovery_interval', result)
        self.assertEqual(result['discovery_interval'], 10.0)  # 设备多，间隔长
    
    @patch('performance_optimizer.PerformanceMetrics')
    def test_optimize_screen_broadcast_high_cpu(self, mock_metrics_class):
        """测试屏幕广播优化 - 高CPU使用率"""
        # Mock性能指标
        mock_metrics = Mock()
        mock_metrics.get_average_cpu_usage.return_value = 85.0  # 高CPU使用率
        mock_metrics_class.return_value = mock_metrics
        
        # Mock屏幕模块
        mock_screen_module = Mock()
        mock_screen_module.get_config.return_value = 'medium'
        
        # 执行优化
        result = self.optimizer.optimize_screen_broadcast(mock_screen_module)
        
        # 验证优化结果
        self.assertIn('video_quality', result)
        self.assertEqual(result['video_quality'], 'low')  # 高CPU时降低质量
        self.assertEqual(result['fps'], 15)
        self.assertEqual(result['bitrate'], 1000)
    
    @patch('performance_optimizer.PerformanceMetrics')
    def test_optimize_screen_broadcast_low_cpu(self, mock_metrics_class):
        """测试屏幕广播优化 - 低CPU使用率"""
        # Mock性能指标
        mock_metrics = Mock()
        mock_metrics.get_average_cpu_usage.return_value = 25.0  # 低CPU使用率
        mock_metrics_class.return_value = mock_metrics
        
        # Mock屏幕模块
        mock_screen_module = Mock()
        mock_screen_module.get_config.return_value = 'medium'
        
        # 执行优化
        result = self.optimizer.optimize_screen_broadcast(mock_screen_module)
        
        # 验证优化结果
        self.assertIn('video_quality', result)
        self.assertEqual(result['video_quality'], 'high')  # 低CPU时提高质量
        self.assertEqual(result['fps'], 30)
        self.assertEqual(result['bitrate'], 3000)
    
    def test_optimize_whiteboard_sync(self):
        """测试白板同步优化"""
        # Mock白板模块
        mock_whiteboard_module = Mock()
        
        # 执行优化
        result = self.optimizer.optimize_whiteboard_sync(mock_whiteboard_module)
        
        # 验证优化结果
        self.assertIn('sync_batch_size', result)
        self.assertIn('sync_interval', result)
        self.assertIn('incremental_sync', result)
        self.assertIn('compression_enabled', result)
        
        self.assertEqual(result['sync_batch_size'], 10)
        self.assertEqual(result['sync_interval'], 100)
        self.assertTrue(result['incremental_sync'])
        self.assertTrue(result['compression_enabled'])

class TestMemoryOptimizer(unittest.TestCase):
    """测试内存优化器"""
    
    def setUp(self):
        """设置测试环境"""
        self.optimizer = MemoryOptimizer()
    
    def test_optimizer_initialization(self):
        """测试优化器初始化"""
        self.assertEqual(self.optimizer.gc_threshold, 80.0)
        self.assertEqual(self.optimizer.cache_size_limit, 100 * 1024 * 1024)
    
    @patch('psutil.virtual_memory')
    @patch('gc.collect')
    def test_optimize_memory_usage_high_usage(self, mock_gc_collect, mock_memory):
        """测试内存优化 - 高内存使用率"""
        # Mock高内存使用率
        mock_memory.side_effect = [
            Mock(percent=85.0),  # 优化前
            Mock(percent=75.0)   # 优化后
        ]
        mock_gc_collect.return_value = 100  # 回收了100个对象
        
        # 执行优化
        result = self.optimizer.optimize_memory_usage()
        
        # 验证优化结果
        self.assertIn('memory_usage_before', result)
        self.assertIn('objects_collected', result)
        self.assertIn('caches_cleared', result)
        self.assertIn('memory_usage_after', result)
        self.assertIn('memory_freed', result)
        
        self.assertEqual(result['memory_usage_before'], 85.0)
        self.assertEqual(result['objects_collected'], 100)
        self.assertTrue(result['caches_cleared'])
        self.assertEqual(result['memory_usage_after'], 75.0)
        self.assertEqual(result['memory_freed'], 10.0)
    
    @patch('psutil.virtual_memory')
    def test_optimize_memory_usage_low_usage(self, mock_memory):
        """测试内存优化 - 低内存使用率"""
        # Mock低内存使用率
        mock_memory.return_value = Mock(percent=50.0)
        
        # 执行优化
        result = self.optimizer.optimize_memory_usage()
        
        # 验证优化结果（不应执行清理）
        self.assertIn('memory_usage_before', result)
        self.assertNotIn('objects_collected', result)
        self.assertEqual(result['memory_usage_before'], 50.0)

class TestPerformanceOptimizer(unittest.TestCase):
    """测试性能优化器主类"""
    
    def setUp(self):
        """设置测试环境"""
        # Mock模块管理器
        self.mock_module_manager = Mock()
        self.optimizer = PerformanceOptimizer(self.mock_module_manager)
    
    def test_optimizer_initialization(self):
        """测试优化器初始化"""
        self.assertIsNotNone(self.optimizer.metrics)
        self.assertIsNotNone(self.optimizer.network_optimizer)
        self.assertIsNotNone(self.optimizer.memory_optimizer)
        self.assertFalse(self.optimizer.is_optimizing)
        self.assertEqual(self.optimizer.optimization_interval, 30.0)
    
    def test_start_stop_optimization(self):
        """测试启动和停止优化"""
        # 启动优化
        self.optimizer.start_optimization()
        self.assertTrue(self.optimizer.is_optimizing)
        self.assertIsNotNone(self.optimizer.optimization_thread)
        
        # 等待一小段时间确保线程启动
        time.sleep(0.1)
        self.assertTrue(self.optimizer.optimization_thread.is_alive())
        
        # 停止优化
        self.optimizer.stop_optimization()
        self.assertFalse(self.optimizer.is_optimizing)
    
    @patch('performance_optimizer.PerformanceOptimizer._perform_optimizations')
    def test_optimization_loop(self, mock_perform_optimizations):
        """测试优化循环"""
        # 设置短的优化间隔用于测试
        self.optimizer.optimization_interval = 0.1
        
        # 启动优化
        self.optimizer.start_optimization()
        
        # 等待几次优化执行
        time.sleep(0.3)
        
        # 停止优化
        self.optimizer.stop_optimization()
        
        # 验证优化方法被调用
        self.assertTrue(mock_perform_optimizations.called)
        self.assertGreaterEqual(mock_perform_optimizations.call_count, 2)
    
    def test_perform_optimizations(self):
        """测试执行优化"""
        # Mock模块实例
        mock_udp_module = Mock()
        mock_screen_module = Mock()
        mock_whiteboard_module = Mock()
        
        self.mock_module_manager.get_module_instance.side_effect = lambda name: {
            "udp_discovery": mock_udp_module,
            "group_screen_monitor": mock_screen_module,
            "whiteboard_collaboration": mock_whiteboard_module
        }.get(name)
        
        # Mock优化器方法
        self.optimizer.network_optimizer.optimize_udp_discovery = Mock(return_value={'test': 'udp'})
        self.optimizer.network_optimizer.optimize_screen_broadcast = Mock(return_value={'test': 'screen'})
        self.optimizer.network_optimizer.optimize_whiteboard_sync = Mock(return_value={'test': 'whiteboard'})
        self.optimizer.memory_optimizer.optimize_memory_usage = Mock(return_value={'test': 'memory'})
        
        # 执行优化
        self.optimizer._perform_optimizations()
        
        # 验证优化方法被调用
        self.optimizer.network_optimizer.optimize_udp_discovery.assert_called_with(mock_udp_module)
        self.optimizer.network_optimizer.optimize_screen_broadcast.assert_called_with(mock_screen_module)
        self.optimizer.network_optimizer.optimize_whiteboard_sync.assert_called_with(mock_whiteboard_module)
        self.optimizer.memory_optimizer.optimize_memory_usage.assert_called_once()
    
    @patch('performance_optimizer.PerformanceMetrics.update_system_metrics')
    @patch('performance_optimizer.PerformanceMetrics.get_average_cpu_usage')
    @patch('performance_optimizer.PerformanceMetrics.get_average_memory_usage')
    @patch('performance_optimizer.PerformanceMetrics.get_network_throughput')
    def test_get_performance_report(self, mock_throughput, mock_memory, mock_cpu, mock_update):
        """测试获取性能报告"""
        # Mock性能指标
        mock_cpu.return_value = 45.0
        mock_memory.return_value = 60.0
        mock_throughput.return_value = {'upload': 100.0, 'download': 200.0}
        
        # 获取性能报告
        report = self.optimizer.get_performance_report()
        
        # 验证报告内容
        self.assertIn('system_metrics', report)
        self.assertIn('module_performance', report)
        self.assertIn('optimization_status', report)
        
        system_metrics = report['system_metrics']
        self.assertEqual(system_metrics['cpu_usage'], 45.0)
        self.assertEqual(system_metrics['memory_usage'], 60.0)
        self.assertEqual(system_metrics['network_throughput']['upload'], 100.0)
        self.assertEqual(system_metrics['network_throughput']['download'], 200.0)
        
        optimization_status = report['optimization_status']
        self.assertIn('is_optimizing', optimization_status)
        self.assertIn('last_update', optimization_status)
        self.assertIn('optimization_interval', optimization_status)
    
    @patch('performance_optimizer.PerformanceOptimizer._perform_optimizations')
    def test_force_optimization(self, mock_perform_optimizations):
        """测试强制优化"""
        # 执行强制优化
        result = self.optimizer.force_optimization()
        
        # 验证优化被执行
        mock_perform_optimizations.assert_called_once()
        
        # 验证返回结果
        self.assertEqual(result['status'], 'success')
        self.assertIn('message', result)

class TestPerformanceIntegration(unittest.TestCase):
    """性能集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.auth_manager = AuthManager()
        self.comm_manager = CommunicationManager()
        self.module_manager = ModuleManager(self.auth_manager, self.comm_manager)
        self.optimizer = PerformanceOptimizer(self.module_manager)
        
        # 模拟教师登录
        self.auth_manager.login("teacher1", "password", "teacher")
    
    def tearDown(self):
        """清理测试环境"""
        self.optimizer.stop_optimization()
        self.module_manager.stop_all_modules()
        self.auth_manager.logout()
    
    @patch('modules.udp_discovery.UDPDiscoveryWindow')
    def test_udp_discovery_optimization_integration(self, mock_window):
        """测试UDP发现优化集成"""
        # 启动UDP发现模块
        result = self.module_manager.start_module("udp_discovery")
        self.assertTrue(result)
        
        # 获取模块实例
        udp_module = self.module_manager.get_module_instance("udp_discovery")
        self.assertIsNotNone(udp_module)
        
        # 执行优化
        optimization_result = self.optimizer.network_optimizer.optimize_udp_discovery(udp_module)
        
        # 验证优化结果
        self.assertIsInstance(optimization_result, dict)
    
    def test_performance_monitoring_integration(self):
        """测试性能监控集成"""
        # 启动性能优化器
        self.optimizer.start_optimization()
        
        # 等待一段时间让优化器运行
        time.sleep(0.5)
        
        # 获取性能报告
        report = self.optimizer.get_performance_report()
        
        # 验证报告内容
        self.assertIn('system_metrics', report)
        self.assertIn('optimization_status', report)
        self.assertTrue(report['optimization_status']['is_optimizing'])
        
        # 停止优化器
        self.optimizer.stop_optimization()
        
        # 验证优化器已停止
        self.assertFalse(self.optimizer.is_optimizing)

if __name__ == '__main__':
    # 运行性能测试
    unittest.main(verbosity=2)
