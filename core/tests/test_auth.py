"""
认证系统测试脚本
"""
import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from auth_manager import AuthManager
from module_manager import <PERSON>duleManager

def test_auth_manager():
    """测试认证管理器"""
    print("=== 测试认证管理器 ===")
    
    auth_manager = AuthManager()
    
    # 测试初始状态
    print(f"初始认证状态: {auth_manager.is_authenticated()}")
    print(f"初始用户角色: {auth_manager.get_user_role()}")
    
    # 测试JWT令牌创建和验证
    test_user = {
        'id': '1',
        'username': 'test_teacher',
        'name': '测试教师',
        'role': 'teacher'
    }
    
    token = auth_manager.create_token(test_user)
    print(f"创建的JWT令牌: {token[:50]}...")
    
    is_valid = auth_manager.verify_token(token)
    print(f"令牌验证结果: {is_valid}")
    
    # 测试保存和加载令牌
    auth_manager.save_token(token, test_user)
    print("令牌已保存到本地")
    
    # 创建新的认证管理器实例测试加载
    new_auth_manager = AuthManager()
    loaded = new_auth_manager.load_cached_token()
    print(f"令牌加载结果: {loaded}")
    
    if loaded:
        print(f"加载的用户信息: {new_auth_manager.get_current_user()}")

def test_module_manager():
    """测试模块管理器"""
    print("\n=== 测试模块管理器 ===")
    
    module_manager = ModuleManager()
    
    # 测试未认证状态
    print("未认证状态下的可用模块:")
    available_modules = module_manager.get_available_modules()
    for module in available_modules:
        print(f"  - {module.display_name} ({module.name})")
    
    # 测试认证状态
    module_manager.set_auth_status(True, 'teacher')
    print("\n教师认证状态下的可用模块:")
    available_modules = module_manager.get_available_modules()
    for module in available_modules:
        print(f"  - {module.display_name} ({module.name})")
    
    # 测试模块访问权限
    print(f"\n白板模块访问权限: {module_manager.can_access_module('whiteboard')}")
    print(f"弹幕模块访问权限: {module_manager.can_access_module('danmaku')}")

def main():
    """主函数"""
    # 创建QApplication实例（某些Qt功能需要）
    app = QApplication(sys.argv)
    
    try:
        test_auth_manager()
        test_module_manager()
        print("\n=== 所有测试完成 ===")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()