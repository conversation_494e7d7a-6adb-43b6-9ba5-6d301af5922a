"""
主副屏模式模块测试
"""
import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.dual_screen import (
    DualScreenModule, ScreenInfo, DualScreenConfig, CoursewareWindow, WhiteboardWindow
)
from PyQt5.QtCore import QRect

class TestScreenInfo(unittest.TestCase):
    """测试屏幕信息类"""
    
    def test_screen_info_creation(self):
        """测试屏幕信息创建"""
        geometry = QRect(0, 0, 1920, 1080)
        screen_info = ScreenInfo(0, geometry, True)
        
        self.assertEqual(screen_info.screen_id, 0)
        self.assertEqual(screen_info.width, 1920)
        self.assertEqual(screen_info.height, 1080)
        self.assertEqual(screen_info.x, 0)
        self.assertEqual(screen_info.y, 0)
        self.assertTrue(screen_info.is_primary)
    
    def test_screen_info_to_dict(self):
        """测试屏幕信息转换为字典"""
        geometry = QRect(1920, 0, 1920, 1080)
        screen_info = ScreenInfo(1, geometry, False)
        screen_dict = screen_info.to_dict()
        
        self.assertIn("screen_id", screen_dict)
        self.assertIn("width", screen_dict)
        self.assertIn("height", screen_dict)
        self.assertIn("x", screen_dict)
        self.assertIn("y", screen_dict)
        self.assertIn("is_primary", screen_dict)
        
        self.assertEqual(screen_dict["screen_id"], 1)
        self.assertEqual(screen_dict["width"], 1920)
        self.assertEqual(screen_dict["x"], 1920)
        self.assertFalse(screen_dict["is_primary"])

class TestDualScreenConfig(unittest.TestCase):
    """测试双屏配置类"""
    
    def test_config_creation(self):
        """测试配置创建"""
        config = DualScreenConfig()
        
        self.assertEqual(config.primary_screen_id, 0)
        self.assertEqual(config.secondary_screen_id, 1)
        self.assertEqual(config.primary_content, "courseware")
        self.assertEqual(config.secondary_content, "whiteboard")
        self.assertTrue(config.sync_navigation)
        self.assertFalse(config.auto_switch)
        self.assertEqual(config.layout_mode, "extended")
    
    def test_config_to_dict(self):
        """测试配置转换为字典"""
        config = DualScreenConfig()
        config.primary_content = "whiteboard"
        config.secondary_content = "courseware"
        config.sync_navigation = False
        
        config_dict = config.to_dict()
        
        self.assertIn("primary_screen_id", config_dict)
        self.assertIn("secondary_screen_id", config_dict)
        self.assertIn("primary_content", config_dict)
        self.assertIn("secondary_content", config_dict)
        self.assertIn("sync_navigation", config_dict)
        
        self.assertEqual(config_dict["primary_content"], "whiteboard")
        self.assertEqual(config_dict["secondary_content"], "courseware")
        self.assertFalse(config_dict["sync_navigation"])
    
    def test_config_from_dict(self):
        """测试从字典创建配置"""
        data = {
            "primary_screen_id": 1,
            "secondary_screen_id": 0,
            "primary_content": "whiteboard",
            "secondary_content": "courseware",
            "sync_navigation": False,
            "auto_switch": True,
            "layout_mode": "mirrored"
        }
        
        config = DualScreenConfig.from_dict(data)
        
        self.assertEqual(config.primary_screen_id, 1)
        self.assertEqual(config.secondary_screen_id, 0)
        self.assertEqual(config.primary_content, "whiteboard")
        self.assertEqual(config.secondary_content, "courseware")
        self.assertFalse(config.sync_navigation)
        self.assertTrue(config.auto_switch)
        self.assertEqual(config.layout_mode, "mirrored")

class TestCoursewareWindow(unittest.TestCase):
    """测试课件窗口类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建QApplication实例（如果不存在）
        from PyQt5.QtWidgets import QApplication
        if not QApplication.instance():
            self.app = QApplication([])
        else:
            self.app = QApplication.instance()
    
    def test_courseware_window_creation(self):
        """测试课件窗口创建"""
        geometry = QRect(0, 0, 1920, 1080)
        window = CoursewareWindow(geometry)
        
        self.assertEqual(window.current_page, 1)
        self.assertEqual(window.total_pages, 10)
        self.assertEqual(window.geometry(), geometry)
    
    def test_page_navigation(self):
        """测试页面导航"""
        geometry = QRect(0, 0, 1920, 1080)
        window = CoursewareWindow(geometry)
        
        # 测试下一页
        window.next_page()
        self.assertEqual(window.current_page, 2)
        
        # 测试上一页
        window.prev_page()
        self.assertEqual(window.current_page, 1)
        
        # 测试跳转到指定页面
        window.goto_page(5)
        self.assertEqual(window.current_page, 5)
        
        # 测试边界条件
        window.goto_page(0)  # 无效页面
        self.assertEqual(window.current_page, 5)  # 应该保持不变
        
        window.goto_page(15)  # 超出范围
        self.assertEqual(window.current_page, 5)  # 应该保持不变
    
    def test_button_states(self):
        """测试按钮状态"""
        geometry = QRect(0, 0, 1920, 1080)
        window = CoursewareWindow(geometry)
        
        # 第一页时，上一页按钮应该禁用
        self.assertFalse(window.prev_btn.isEnabled())
        self.assertTrue(window.next_btn.isEnabled())
        
        # 跳转到最后一页
        window.goto_page(window.total_pages)
        self.assertTrue(window.prev_btn.isEnabled())
        self.assertFalse(window.next_btn.isEnabled())

class TestWhiteboardWindow(unittest.TestCase):
    """测试白板窗口类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建QApplication实例（如果不存在）
        from PyQt5.QtWidgets import QApplication
        if not QApplication.instance():
            self.app = QApplication([])
        else:
            self.app = QApplication.instance()
    
    def test_whiteboard_window_creation(self):
        """测试白板窗口创建"""
        geometry = QRect(1920, 0, 1920, 1080)
        window = WhiteboardWindow(geometry)
        
        self.assertEqual(window.geometry(), geometry)
        self.assertIsNotNone(window.whiteboard_label)
        self.assertIsNotNone(window.pen_btn)
        self.assertIsNotNone(window.eraser_btn)
        self.assertIsNotNone(window.clear_btn)
    
    def test_clear_whiteboard(self):
        """测试清空白板"""
        geometry = QRect(1920, 0, 1920, 1080)
        window = WhiteboardWindow(geometry)
        
        # 修改白板内容
        window.whiteboard_label.setText("测试内容")
        
        # 清空白板
        window.clear_whiteboard()
        
        # 验证内容已恢复默认
        self.assertIn("白板区域", window.whiteboard_label.text())

class TestDualScreenModule(unittest.TestCase):
    """测试主副屏模式模块"""
    
    def setUp(self):
        """设置测试环境"""
        # Mock认证管理器
        self.mock_auth_manager = Mock()
        self.mock_auth_manager.is_authenticated.return_value = True
        self.mock_auth_manager.get_current_user.return_value = {
            "id": "teacher1",
            "name": "测试教师",
            "role": "teacher"
        }
        
        # 创建主副屏模块
        self.dual_screen = DualScreenModule()
        self.dual_screen.auth_manager = self.mock_auth_manager
    
    @patch('modules.dual_screen.DualScreenControlWindow')
    @patch('modules.dual_screen.QApplication')
    def test_module_initialization(self, mock_app, mock_window):
        """测试模块初始化"""
        # Mock QApplication和desktop
        mock_app_instance = Mock()
        mock_desktop = Mock()
        mock_desktop.screenCount.return_value = 2
        mock_desktop.screenGeometry.side_effect = [
            QRect(0, 0, 1920, 1080),
            QRect(1920, 0, 1920, 1080)
        ]
        mock_desktop.primaryScreen.return_value = 0
        mock_app_instance.desktop.return_value = mock_desktop
        mock_app.instance.return_value = mock_app_instance
        
        # 初始化模块
        result = self.dual_screen._initialize_module()
        
        # 验证初始化成功
        self.assertTrue(result)
        self.assertIsNotNone(self.dual_screen.control_window)
        self.assertEqual(len(self.dual_screen.available_screens), 2)
    
    def test_module_initialization_without_auth(self):
        """测试未认证时的模块初始化"""
        # 设置未认证状态
        self.mock_auth_manager.is_authenticated.return_value = False
        
        # 初始化模块
        result = self.dual_screen._initialize_module()
        
        # 验证初始化失败
        self.assertFalse(result)
    
    @patch('modules.dual_screen.QApplication')
    def test_detect_screens(self, mock_app):
        """测试屏幕检测"""
        # Mock QApplication和desktop
        mock_app_instance = Mock()
        mock_desktop = Mock()
        mock_desktop.screenCount.return_value = 2
        mock_desktop.screenGeometry.side_effect = [
            QRect(0, 0, 1920, 1080),
            QRect(1920, 0, 1920, 1080)
        ]
        mock_desktop.primaryScreen.return_value = 0
        mock_app_instance.desktop.return_value = mock_desktop
        mock_app.instance.return_value = mock_app_instance
        
        # 检测屏幕
        self.dual_screen.detect_screens()
        
        # 验证检测结果
        self.assertEqual(len(self.dual_screen.available_screens), 2)
        self.assertTrue(self.dual_screen.available_screens[0].is_primary)
        self.assertFalse(self.dual_screen.available_screens[1].is_primary)
    
    def test_enable_dual_screen_mode_insufficient_screens(self):
        """测试屏幕数量不足时启用双屏模式"""
        # 只有一个屏幕
        geometry = QRect(0, 0, 1920, 1080)
        screen_info = ScreenInfo(0, geometry, True)
        self.dual_screen.available_screens = [screen_info]
        
        # 尝试启用双屏模式
        result = self.dual_screen.enable_dual_screen_mode()
        
        # 验证启用失败
        self.assertFalse(result)
        self.assertFalse(self.dual_screen.is_dual_screen_mode)
    
    @patch.object(DualScreenModule, 'create_courseware_window')
    @patch.object(DualScreenModule, 'create_whiteboard_window')
    def test_enable_dual_screen_mode_success(self, mock_create_whiteboard, mock_create_courseware):
        """测试成功启用双屏模式"""
        # 添加两个屏幕
        screen1 = ScreenInfo(0, QRect(0, 0, 1920, 1080), True)
        screen2 = ScreenInfo(1, QRect(1920, 0, 1920, 1080), False)
        self.dual_screen.available_screens = [screen1, screen2]
        
        # 启用双屏模式
        result = self.dual_screen.enable_dual_screen_mode()
        
        # 验证启用成功
        self.assertTrue(result)
        self.assertTrue(self.dual_screen.is_dual_screen_mode)
        
        # 验证窗口创建方法被调用
        mock_create_courseware.assert_called_once()
        mock_create_whiteboard.assert_called_once()
    
    def test_disable_dual_screen_mode(self):
        """测试禁用双屏模式"""
        # 先启用双屏模式
        self.dual_screen.is_dual_screen_mode = True
        
        # Mock close_all_windows方法
        self.dual_screen.close_all_windows = Mock()
        
        # 禁用双屏模式
        self.dual_screen.disable_dual_screen_mode()
        
        # 验证禁用成功
        self.assertFalse(self.dual_screen.is_dual_screen_mode)
        self.dual_screen.close_all_windows.assert_called_once()
    
    def test_switch_screen_content(self):
        """测试切换屏幕内容"""
        # 添加屏幕
        screen1 = ScreenInfo(0, QRect(0, 0, 1920, 1080), True)
        self.dual_screen.available_screens = [screen1]
        
        # Mock窗口创建方法
        self.dual_screen.create_courseware_window = Mock()
        self.dual_screen.create_whiteboard_window = Mock()
        
        # 切换到课件
        self.dual_screen.switch_screen_content(0, "courseware")
        self.dual_screen.create_courseware_window.assert_called_once()
        
        # 切换到白板
        self.dual_screen.switch_screen_content(0, "whiteboard")
        self.dual_screen.create_whiteboard_window.assert_called_once()
    
    def test_navigate_to_page(self):
        """测试页面导航"""
        # 创建Mock课件窗口
        mock_window1 = Mock()
        mock_window2 = Mock()
        self.dual_screen.courseware_windows = {0: mock_window1, 1: mock_window2}
        
        # 导航到第5页
        self.dual_screen.navigate_to_page(5)
        
        # 验证所有课件窗口都被更新
        mock_window1.goto_page.assert_called_with(5)
        mock_window2.goto_page.assert_called_with(5)
    
    def test_get_screen_status(self):
        """测试获取屏幕状态"""
        # 设置测试数据
        screen1 = ScreenInfo(0, QRect(0, 0, 1920, 1080), True)
        screen2 = ScreenInfo(1, QRect(1920, 0, 1920, 1080), False)
        self.dual_screen.available_screens = [screen1, screen2]
        self.dual_screen.is_dual_screen_mode = True
        
        # 添加窗口
        self.dual_screen.courseware_windows = {0: Mock()}
        self.dual_screen.whiteboard_windows = {1: Mock()}
        
        # 获取状态
        status = self.dual_screen.get_screen_status()
        
        # 验证状态信息
        self.assertEqual(status["available_screens"], 2)
        self.assertTrue(status["is_dual_screen_mode"])
        self.assertEqual(status["active_windows"]["courseware"], 1)
        self.assertEqual(status["active_windows"]["whiteboard"], 1)
    
    def test_message_handling(self):
        """测试消息处理"""
        # Mock相关方法
        self.dual_screen.enable_dual_screen_mode = Mock(return_value=True)
        self.dual_screen.disable_dual_screen_mode = Mock()
        self.dual_screen.switch_screen_content = Mock()
        self.dual_screen.navigate_to_page = Mock()
        
        # 测试启用双屏模式消息
        self.dual_screen._handle_message("test_sender", "enable_dual_screen", {})
        self.dual_screen.enable_dual_screen_mode.assert_called_once()
        
        # 测试禁用双屏模式消息
        self.dual_screen._handle_message("test_sender", "disable_dual_screen", {})
        self.dual_screen.disable_dual_screen_mode.assert_called_once()
        
        # 测试切换内容消息
        self.dual_screen._handle_message("test_sender", "switch_content", {
            "screen_id": 0, "content_type": "courseware"
        })
        self.dual_screen.switch_screen_content.assert_called_with(0, "courseware")
        
        # 测试页面导航消息
        self.dual_screen._handle_message("test_sender", "navigate_page", {"page": 5})
        self.dual_screen.navigate_to_page.assert_called_with(5)
        
        # 测试获取状态消息
        self.dual_screen._handle_message("test_sender", "get_screen_status", {})
        
        # 测试显示界面消息
        self.dual_screen._handle_message("test_sender", "show_dual_screen", {})

if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
