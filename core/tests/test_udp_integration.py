"""
UDP设备发现模块集成测试
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from module_manager import ModuleManager
import time

def test_udp_discovery_integration():
    """测试UDP设备发现模块集成"""
    print("开始UDP设备发现模块集成测试...")
    
    # 创建应用
    app = QApplication([])
    
    # 创建模块管理器
    module_manager = ModuleManager()
    
    # 检查UDP设备发现模块是否在可用模块列表中
    available_modules = module_manager.get_available_modules()
    udp_module_info = None
    
    for module_info in available_modules:
        if module_info.name == "udp_discovery":
            udp_module_info = module_info
            break
    
    if not udp_module_info:
        print("❌ UDP设备发现模块未在可用模块列表中找到")
        return False
    
    print(f"✅ 找到UDP设备发现模块: {udp_module_info.display_name}")
    
    # 尝试加载模块
    print("正在加载UDP设备发现模块...")
    udp_module = module_manager.load_module("udp_discovery")
    
    if not udp_module:
        print("❌ UDP设备发现模块加载失败")
        return False
    
    print("✅ UDP设备发现模块加载成功")
    
    # 检查模块是否正确初始化
    if not udp_module.is_ready():
        print("❌ UDP设备发现模块未正确初始化")
        return False
    
    print("✅ UDP设备发现模块初始化成功")
    
    # 检查模块信息
    module_info = udp_module.get_module_info()
    print(f"模块信息: {module_info}")
    
    # 检查本地设备信息
    if udp_module.local_device:
        print(f"本地设备信息: {udp_module.local_device.to_dict()}")
    else:
        print("❌ 本地设备信息未创建")
        return False
    
    # 检查界面是否创建
    if udp_module.window:
        print("✅ UDP设备发现界面创建成功")
        # 显示界面进行手动测试
        udp_module.show_window()
    else:
        print("❌ UDP设备发现界面未创建")
        return False
    
    # 测试模块间通信
    print("测试模块间通信...")
    
    # 模拟其他模块请求设备列表
    def test_message_handler(sender, message_type, data):
        if message_type == "devices_response":
            print(f"✅ 收到设备列表响应: {len(data.get('devices', []))} 个设备")
    
    # 创建测试模块来接收响应
    class TestModule:
        def __init__(self):
            self.module_name = "test_module"
            self.received_messages = []
        
        def handle_message(self, sender, message_type, data):
            self.received_messages.append((sender, message_type, data))
            test_message_handler(sender, message_type, data)
    
    test_module = TestModule()
    module_manager.comm_manager.register_module(test_module)
    
    # 发送获取设备列表的消息
    udp_module._handle_message("test_module", "get_devices", {})
    
    # 等待一下让消息处理完成
    app.processEvents()
    
    # 检查是否收到响应
    if test_module.received_messages:
        print("✅ 模块间通信测试成功")
    else:
        print("❌ 模块间通信测试失败")
    
    # 清理
    print("清理模块...")
    module_manager.unload_module("udp_discovery")
    
    print("✅ UDP设备发现模块集成测试完成")
    return True

if __name__ == "__main__":
    success = test_udp_discovery_integration()
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 测试失败！")
    
    sys.exit(0 if success else 1)