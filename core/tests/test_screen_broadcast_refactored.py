"""
屏幕广播模块单元测试
"""
import unittest
import sys
import os
import tempfile
import json
import time
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.screen_broadcast_refactored import ScreenBroadcastModule, ScreenBroadcastWindow

class TestScreenBroadcastModule(unittest.TestCase):
    """屏幕广播模块测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.module = ScreenBroadcastModule()
        
        # 模拟通信管理器
        self.module.comm_manager = Mock()
        self.module.message_bus = Mock()
        
        # 初始化模块
        self.module.initialize()
    
    def tearDown(self):
        """每个测试后的清理"""
        if self.module:
            self.module.cleanup()
    
    def test_module_initialization(self):
        """测试模块初始化"""
        self.assertTrue(self.module.is_initialized)
        self.assertEqual(self.module.module_name, "screen_broadcast")
        self.assertEqual(self.module.display_name, "屏幕广播")
        self.assertIsNotNone(self.module.broadcast_window)
    
    def test_dependency_check(self):
        """测试依赖检查"""
        # 模拟FFmpeg可用
        with patch.object(self.module, '_check_ffmpeg', return_value=True):
            self.assertTrue(self.module._check_ffmpeg())
        
        # 模拟FFmpeg不可用
        with patch.object(self.module, '_check_ffmpeg', return_value=False):
            self.assertFalse(self.module._check_ffmpeg())
        
        # 模拟MediaMTX可用
        with patch.object(self.module, '_check_mediamtx', return_value=True):
            self.assertTrue(self.module._check_mediamtx())
        
        # 模拟MediaMTX不可用
        with patch.object(self.module, '_check_mediamtx', return_value=False):
            self.assertFalse(self.module._check_mediamtx())
    
    def test_settings_management(self):
        """测试设置管理"""
        # 测试应用设置
        test_settings = {
            "quality": "high",
            "framerate": 60,
            "audio": True,
            "target_ip": "*************",
            "target_port": 8080
        }
        
        self.module._apply_settings(test_settings)
        
        self.assertEqual(self.module.broadcast_quality, "high")
        self.assertEqual(self.module.frame_rate, 60)
        self.assertTrue(self.module.enable_audio)
        self.assertEqual(self.module.target_ip, "*************")
        self.assertEqual(self.module.target_port, 8080)
        
        # 测试获取当前设置
        current_settings = self.module._get_current_settings()
        self.assertEqual(current_settings["quality"], "high")
        self.assertEqual(current_settings["framerate"], 60)
        self.assertTrue(current_settings["audio"])
    
    def test_ffmpeg_command_building(self):
        """测试FFmpeg命令构建"""
        # 设置测试参数
        self.module.broadcast_quality = "medium"
        self.module.frame_rate = 30
        self.module.enable_audio = True
        self.module.target_ip = "*************"
        self.module.target_port = 1935
        self.module.stream_key = "test_stream"
        
        command = self.module._build_ffmpeg_command()
        
        self.assertIsInstance(command, list)
        self.assertIn("ffmpeg", command)
        self.assertIn("-framerate", command)
        self.assertIn("30", command)
        
        # 检查RTMP URL
        rtmp_url = f"rtmp://{self.module.target_ip}:{self.module.target_port}/live/{self.module.stream_key}"
        self.assertIn(rtmp_url, command)
    
    def test_mediamtx_config_generation(self):
        """测试MediaMTX配置生成"""
        config = self.module._generate_mediamtx_config()
        
        self.assertIsInstance(config, str)
        self.assertIn(f"rtspAddress: :{self.module.mediamtx_port}", config)
        self.assertIn(f"{self.module.stream_key}:", config)
    
    def test_broadcast_lifecycle_mock(self):
        """测试广播生命周期（模拟）"""
        # 模拟依赖检查
        with patch.object(self.module, '_check_ffmpeg', return_value=True), \
             patch.object(self.module, '_check_mediamtx', return_value=True), \
             patch.object(self.module, '_start_ffmpeg_broadcast', return_value=True), \
             patch.object(self.module, '_stop_ffmpeg_broadcast'):
            
            # 测试开始广播
            result = self.module.start_broadcast("ffmpeg_mediamtx")
            self.assertTrue(result)
            self.assertTrue(self.module.is_broadcasting)
            self.assertEqual(self.module.current_mode, "ffmpeg_mediamtx")
            self.assertIsNotNone(self.module.start_time)
            
            # 测试停止广播
            result = self.module.stop_broadcast()
            self.assertTrue(result)
            self.assertFalse(self.module.is_broadcasting)
    
    def test_broadcast_status(self):
        """测试广播状态"""
        # 初始状态
        status = self.module.get_broadcast_status()
        self.assertFalse(status["is_broadcasting"])
        self.assertEqual(status["mode"], "ffmpeg_mediamtx")
        self.assertEqual(status["elapsed_time"], 0)
        
        # 模拟广播状态
        self.module.is_broadcasting = True
        self.module.start_time = time.time() - 60  # 1分钟前开始
        self.module.viewers_count = 5
        
        status = self.module.get_broadcast_status()
        self.assertTrue(status["is_broadcasting"])
        self.assertGreaterEqual(status["elapsed_time"], 59)
        self.assertLessEqual(status["elapsed_time"], 61)
        self.assertEqual(status["viewers_count"], 5)
    
    def test_broadcast_url_generation(self):
        """测试广播URL生成"""
        # 模拟本地IP
        with patch.object(self.module, '_get_local_ip', return_value='*************'):
            # 测试FFmpeg模式
            self.module.current_mode = "ffmpeg_mediamtx"
            self.module.is_broadcasting = True
            
            url = self.module.get_broadcast_url()
            self.assertIsInstance(url, dict)
            self.assertIn("rtsp", url)
            self.assertIn("hls", url)
            self.assertTrue(url["rtsp"].startswith("rtsp://*************"))
            self.assertTrue(url["hls"].startswith("http://*************"))
    
    def test_broadcast_history(self):
        """测试广播历史"""
        # 清空现有历史
        initial_count = len(self.module.broadcast_history)
        self.module.broadcast_history.clear()

        # 测试初始状态
        history = self.module.get_broadcast_history()
        self.assertIsInstance(history, list)
        self.assertEqual(len(history), 0)

        # 添加测试记录
        self.module.current_mode = "ffmpeg_mediamtx"
        self.module.start_time = time.time()
        self.module._add_to_history("started")

        history = self.module.get_broadcast_history()
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]["status"], "started")
        self.assertEqual(history[0]["mode"], "ffmpeg_mediamtx")
    
    def test_history_persistence(self):
        """测试历史记录持久化"""
        # 使用临时文件进行测试
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name

        try:
            # 备份原始历史文件路径
            original_save_method = self.module._save_broadcast_history
            original_load_method = self.module._load_broadcast_history

            # 重写保存和加载方法使用临时文件
            def temp_save():
                try:
                    with open(temp_file, 'w', encoding='utf-8') as f:
                        json.dump(self.module.broadcast_history, f, ensure_ascii=False, indent=2)
                except Exception as e:
                    self.module.logger.error(f"保存广播历史失败: {str(e)}")

            def temp_load():
                try:
                    if os.path.exists(temp_file):
                        with open(temp_file, 'r', encoding='utf-8') as f:
                            self.module.broadcast_history = json.load(f)
                except Exception as e:
                    self.module.logger.error(f"加载广播历史失败: {str(e)}")
                    self.module.broadcast_history = []

            self.module._save_broadcast_history = temp_save
            self.module._load_broadcast_history = temp_load

            # 清空当前历史
            self.module.broadcast_history.clear()

            # 添加测试记录
            test_record = {
                "mode": "ffmpeg_mediamtx",
                "status": "completed",
                "start_time": "2024-01-01T12:00:00",
                "duration": 300,
                "settings": {"quality": "medium"},
                "timestamp": "2024-01-01T12:05:00"
            }

            self.module.broadcast_history.append(test_record)

            # 保存历史
            self.module._save_broadcast_history()

            # 清空内存中的历史
            self.module.broadcast_history.clear()

            # 重新加载
            self.module._load_broadcast_history()

            # 验证数据
            history = self.module.get_broadcast_history()
            self.assertEqual(len(history), 1)
            self.assertEqual(history[0]["mode"], "ffmpeg_mediamtx")
            self.assertEqual(history[0]["duration"], 300)

            # 恢复原始方法
            self.module._save_broadcast_history = original_save_method
            self.module._load_broadcast_history = original_load_method

        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    def test_message_handling(self):
        """测试消息处理"""
        # 模拟开始广播消息
        with patch.object(self.module, 'start_broadcast') as mock_start:
            self.module._handle_message("test_sender", "start_broadcast", {
                "mode": "ffmpeg_mediamtx",
                "settings": {"quality": "high"}
            })
            mock_start.assert_called_once_with("ffmpeg_mediamtx", {"quality": "high"})
        
        # 模拟停止广播消息
        with patch.object(self.module, 'stop_broadcast') as mock_stop:
            self.module._handle_message("test_sender", "stop_broadcast", {})
            mock_stop.assert_called_once()
    
    def test_local_ip_detection(self):
        """测试本地IP检测"""
        ip = self.module._get_local_ip()
        self.assertIsInstance(ip, str)
        self.assertTrue(len(ip) > 0)
        # 简单验证IP格式
        parts = ip.split('.')
        if ip != "127.0.0.1":
            self.assertEqual(len(parts), 4)

class TestScreenBroadcastWindow(unittest.TestCase):
    """屏幕广播窗口测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.module = ScreenBroadcastModule()
        self.module.comm_manager = Mock()
        self.module.message_bus = Mock()
        
        self.module.initialize()
        self.window = self.module.broadcast_window
    
    def tearDown(self):
        """每个测试后的清理"""
        if self.module:
            self.module.cleanup()
    
    def test_window_initialization(self):
        """测试窗口初始化"""
        self.assertIsNotNone(self.window)
        self.assertIsNotNone(self.window.ffmpeg_radio)
        self.assertIsNotNone(self.window.dlna_radio)
        self.assertIsNotNone(self.window.start_btn)
        self.assertIsNotNone(self.window.stop_btn)
        self.assertIsNotNone(self.window.status_label)
    
    def test_mode_switching(self):
        """测试模式切换"""
        # 测试切换到DLNA模式
        self.window.dlna_radio.setChecked(True)
        self.window.mode_changed()
        self.assertEqual(self.module.current_mode, "dlna")
        
        # 测试切换到FFmpeg模式
        self.window.ffmpeg_radio.setChecked(True)
        self.window.mode_changed()
        self.assertEqual(self.module.current_mode, "ffmpeg_mediamtx")
    
    def test_settings_update(self):
        """测试设置更新"""
        # 设置测试值
        self.window.target_ip_edit.setText("*************")
        self.window.target_port_edit.setText("8080")
        self.window.quality_combo.setCurrentText("high")
        self.window.framerate_combo.setCurrentText("60")
        self.window.audio_cb.setChecked(True)
        
        # 更新设置
        self.window.update_settings()
        
        # 验证设置已更新
        self.assertEqual(self.module.target_ip, "*************")
        self.assertEqual(self.module.target_port, 8080)
        self.assertEqual(self.module.broadcast_quality, "high")
        self.assertEqual(self.module.frame_rate, 60)
        self.assertTrue(self.module.enable_audio)
    
    def test_button_states(self):
        """测试按钮状态"""
        # 初始状态
        self.assertTrue(self.window.start_btn.isEnabled())
        self.assertFalse(self.window.stop_btn.isEnabled())
        self.assertFalse(self.window.copy_url_btn.isEnabled())
        
        # 模拟开始广播
        with patch.object(self.module, 'start_broadcast', return_value=True):
            self.window.start_broadcast()
            
            self.assertFalse(self.window.start_btn.isEnabled())
            self.assertTrue(self.window.stop_btn.isEnabled())
            self.assertTrue(self.window.copy_url_btn.isEnabled())
    
    def test_status_display_update(self):
        """测试状态显示更新"""
        # 模拟广播状态
        self.module.is_broadcasting = True
        self.module.start_time = time.time() - 65  # 1分5秒前开始
        self.module.viewers_count = 3
        
        # 模拟广播URL
        with patch.object(self.module, 'get_broadcast_url', return_value={
            "rtsp": "rtsp://*************:8554/desktop",
            "hls": "http://*************:8888/desktop/index.m3u8"
        }):
            self.window.update_status_display()
            
            # 验证时间显示
            time_text = self.window.time_label.text()
            self.assertEqual(time_text, "00:01:05")
            
            # 验证观看人数
            self.assertEqual(self.window.viewers_label.text(), "3")
    
    def test_history_operations(self):
        """测试历史操作"""
        # 添加测试历史
        test_history = [
            {
                "mode": "ffmpeg_mediamtx",
                "status": "completed",
                "duration": 300,
                "timestamp": "2024-01-01T12:00:00"
            },
            {
                "mode": "dlna",
                "status": "completed", 
                "duration": 600,
                "timestamp": "2024-01-01T13:00:00"
            }
        ]
        
        self.module.broadcast_history = test_history
        
        # 测试刷新历史
        self.window.refresh_history()
        self.assertEqual(self.window.history_list.count(), 2)
    
    def test_url_copy(self):
        """测试URL复制"""
        # 模拟广播URL
        with patch.object(self.module, 'get_broadcast_url', return_value={
            "rtsp": "rtsp://*************:8554/desktop",
            "hls": "http://*************:8888/desktop/index.m3u8"
        }):
            with patch('PyQt5.QtWidgets.QApplication.clipboard') as mock_clipboard:
                mock_clipboard_obj = Mock()
                mock_clipboard.return_value = mock_clipboard_obj
                
                self.window.copy_broadcast_url()
                mock_clipboard_obj.setText.assert_called_once_with("rtsp://*************:8554/desktop")

if __name__ == '__main__':
    unittest.main()
