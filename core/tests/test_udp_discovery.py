"""
UDP设备发现模块单元测试
"""
import unittest
import sys
import os
import socket
import json
import time
import threading
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest

from modules.udp_discovery import (
    DeviceInfo, UDPDiscoveryProtocol, UDPDiscoveryModule, UDPDiscoveryWindow
)

class TestDeviceInfo(unittest.TestCase):
    """设备信息类测试"""
    
    def setUp(self):
        self.device_info = DeviceInfo(
            device_id="test-device-1",
            device_type="teacher",
            device_name="测试设备",
            ip_address="*************",
            port=8888,
            capabilities=["whiteboard", "screen_share"]
        )
    
    def test_device_info_creation(self):
        """测试设备信息创建"""
        self.assertEqual(self.device_info.device_id, "test-device-1")
        self.assertEqual(self.device_info.device_type, "teacher")
        self.assertEqual(self.device_info.device_name, "测试设备")
        self.assertEqual(self.device_info.ip_address, "*************")
        self.assertEqual(self.device_info.port, 8888)
        self.assertEqual(self.device_info.capabilities, ["whiteboard", "screen_share"])
        self.assertEqual(self.device_info.status, "online")
    
    def test_to_dict(self):
        """测试转换为字典"""
        data = self.device_info.to_dict()
        
        self.assertEqual(data["device_id"], "test-device-1")
        self.assertEqual(data["device_type"], "teacher")
        self.assertEqual(data["device_name"], "测试设备")
        self.assertEqual(data["ip_address"], "*************")
        self.assertEqual(data["port"], 8888)
        self.assertEqual(data["capabilities"], ["whiteboard", "screen_share"])
        self.assertEqual(data["status"], "online")
        self.assertIn("last_seen", data)
    
    def test_from_dict(self):
        """测试从字典创建"""
        data = {
            "device_id": "test-device-2",
            "device_type": "group",
            "device_name": "小组设备",
            "ip_address": "*************",
            "port": 8889,
            "capabilities": ["file_transfer"],
            "status": "offline"
        }
        
        device = DeviceInfo.from_dict(data)
        
        self.assertEqual(device.device_id, "test-device-2")
        self.assertEqual(device.device_type, "group")
        self.assertEqual(device.device_name, "小组设备")
        self.assertEqual(device.ip_address, "*************")
        self.assertEqual(device.port, 8889)
        self.assertEqual(device.capabilities, ["file_transfer"])
        self.assertEqual(device.status, "offline")
    
    def test_update_last_seen(self):
        """测试更新最后见到时间"""
        old_time = self.device_info.last_seen
        time.sleep(0.01)  # 等待一小段时间
        
        self.device_info.update_last_seen()
        
        self.assertGreater(self.device_info.last_seen, old_time)
        self.assertEqual(self.device_info.status, "online")
    
    def test_is_expired(self):
        """测试设备过期检查"""
        # 新设备不应该过期
        self.assertFalse(self.device_info.is_expired(30))
        
        # 设置过期时间
        self.device_info.last_seen = datetime.now() - timedelta(seconds=35)
        self.assertTrue(self.device_info.is_expired(30))

class TestUDPDiscoveryProtocol(unittest.TestCase):
    """UDP发现协议测试"""
    
    def setUp(self):
        self.device_info = DeviceInfo(
            device_id="test-device",
            device_type="teacher",
            device_name="测试设备",
            ip_address="*************",
            port=8888,
            capabilities=["whiteboard"]
        )
    
    def test_create_discovery_message(self):
        """测试创建发现消息"""
        message_bytes = UDPDiscoveryProtocol.create_discovery_message(self.device_info)
        
        self.assertIsInstance(message_bytes, bytes)
        
        # 解析消息
        message = json.loads(message_bytes.decode('utf-8'))
        
        self.assertEqual(message["type"], UDPDiscoveryProtocol.MSG_DISCOVERY)
        self.assertEqual(message["device_id"], "test-device")
        self.assertEqual(message["device_type"], "teacher")
        self.assertEqual(message["device_name"], "测试设备")
        self.assertEqual(message["ip_address"], "*************")
        self.assertEqual(message["port"], 8888)
        self.assertEqual(message["capabilities"], ["whiteboard"])
        self.assertIn("timestamp", message)
    
    def test_create_heartbeat_message(self):
        """测试创建心跳消息"""
        message_bytes = UDPDiscoveryProtocol.create_heartbeat_message(self.device_info)
        
        self.assertIsInstance(message_bytes, bytes)
        
        # 解析消息
        message = json.loads(message_bytes.decode('utf-8'))
        
        self.assertEqual(message["type"], UDPDiscoveryProtocol.MSG_HEARTBEAT)
        self.assertEqual(message["device_id"], "test-device")
        self.assertIn("timestamp", message)
    
    def test_create_goodbye_message(self):
        """测试创建离线消息"""
        message_bytes = UDPDiscoveryProtocol.create_goodbye_message("test-device")
        
        self.assertIsInstance(message_bytes, bytes)
        
        # 解析消息
        message = json.loads(message_bytes.decode('utf-8'))
        
        self.assertEqual(message["type"], UDPDiscoveryProtocol.MSG_GOODBYE)
        self.assertEqual(message["device_id"], "test-device")
        self.assertIn("timestamp", message)
    
    def test_parse_message_valid(self):
        """测试解析有效消息"""
        original_message = {
            "type": UDPDiscoveryProtocol.MSG_DISCOVERY,
            "device_id": "test-device",
            "timestamp": datetime.now().isoformat()
        }
        
        message_bytes = json.dumps(original_message).encode('utf-8')
        parsed_message = UDPDiscoveryProtocol.parse_message(message_bytes)
        
        self.assertEqual(parsed_message, original_message)
    
    def test_parse_message_invalid(self):
        """测试解析无效消息"""
        # 无效JSON
        invalid_bytes = b"invalid json"
        result = UDPDiscoveryProtocol.parse_message(invalid_bytes)
        self.assertIsNone(result)
        
        # 无效编码
        invalid_bytes = b"\xff\xfe\x00\x00"
        result = UDPDiscoveryProtocol.parse_message(invalid_bytes)
        self.assertIsNone(result)

class TestUDPDiscoveryModule(unittest.TestCase):
    """UDP设备发现模块测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.module = UDPDiscoveryModule()
        
        # 模拟配置
        self.module.set_config({
            "device_type": "teacher",
            "device_name": "测试教师设备",
            "capabilities": ["whiteboard", "screen_share"]
        })
    
    def tearDown(self):
        """清理测试"""
        if self.module.is_running:
            self.module.stop_discovery()
        self.module.cleanup()
    
    def test_module_initialization(self):
        """测试模块初始化"""
        success = self.module.initialize()
        
        self.assertTrue(success)
        self.assertTrue(self.module.is_ready())
        self.assertIsNotNone(self.module.local_device)
        self.assertIsNotNone(self.module.window)
        self.assertEqual(self.module.local_device.device_type, "teacher")
        self.assertEqual(self.module.local_device.device_name, "测试教师设备")
    
    @patch('socket.socket')
    def test_start_discovery_success(self, mock_socket_class):
        """测试成功启动设备发现"""
        # 模拟套接字
        mock_socket = Mock()
        mock_socket_class.return_value = mock_socket
        
        # 初始化模块
        self.module.initialize()
        
        # 启动发现
        success = self.module.start_discovery()
        
        self.assertTrue(success)
        self.assertTrue(self.module.is_running)
        self.assertIsNotNone(self.module.socket)
        self.assertIsNotNone(self.module.listen_thread)
        
        # 验证套接字配置
        mock_socket.setsockopt.assert_any_call(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        mock_socket.setsockopt.assert_any_call(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        mock_socket.bind.assert_called_once_with(('', 8888))
    
    @patch('socket.socket')
    def test_start_discovery_failure(self, mock_socket_class):
        """测试启动设备发现失败"""
        # 模拟套接字异常
        mock_socket_class.side_effect = Exception("Socket error")
        
        # 初始化模块
        self.module.initialize()
        
        # 启动发现
        success = self.module.start_discovery()
        
        self.assertFalse(success)
        self.assertFalse(self.module.is_running)
    
    def test_stop_discovery(self):
        """测试停止设备发现"""
        # 初始化并启动
        self.module.initialize()
        
        with patch('socket.socket'):
            self.module.start_discovery()
            self.assertTrue(self.module.is_running)
            
            # 停止发现
            self.module.stop_discovery()
            
            self.assertFalse(self.module.is_running)
            self.assertIsNone(self.module.socket)
    
    def test_device_management(self):
        """测试设备管理功能"""
        # 初始化模块
        self.module.initialize()
        
        # 添加测试设备
        device1 = DeviceInfo("device1", "group", "小组1", "*************", 8888, ["whiteboard"])
        device2 = DeviceInfo("device2", "student", "学生1", "192.168.1.102", 8888, ["screen_share"])
        
        self.module.discovered_devices["device1"] = device1
        self.module.discovered_devices["device2"] = device2
        
        # 测试获取所有设备
        devices = self.module.get_discovered_devices()
        self.assertEqual(len(devices), 2)
        self.assertIn("device1", devices)
        self.assertIn("device2", devices)
        
        # 测试根据ID获取设备
        device = self.module.get_device_by_id("device1")
        self.assertEqual(device.device_name, "小组1")
        
        # 测试根据类型获取设备
        group_devices = self.module.get_devices_by_type("group")
        self.assertEqual(len(group_devices), 1)
        self.assertEqual(group_devices[0].device_name, "小组1")
        
        student_devices = self.module.get_devices_by_type("student")
        self.assertEqual(len(student_devices), 1)
        self.assertEqual(student_devices[0].device_name, "学生1")
    
    def test_message_handling(self):
        """测试消息处理"""
        # 初始化模块
        self.module.initialize()
        
        # 模拟发送消息的模块
        mock_sender = Mock()
        mock_sender.module_name = "test_sender"
        
        # 测试获取设备列表消息
        with patch.object(self.module, 'send_message') as mock_send:
            self.module._handle_message("test_sender", "get_devices", {})
            
            # 验证回复消息
            mock_send.assert_called_once()
            args = mock_send.call_args[0]
            self.assertEqual(args[0], "test_sender")
            self.assertEqual(args[1], "devices_response")
            self.assertIn("devices", args[2])
            self.assertIn("local_device", args[2])
    
    def test_cleanup_offline_devices(self):
        """测试清理离线设备"""
        # 初始化模块
        self.module.initialize()
        
        # 添加测试设备
        device = DeviceInfo("device1", "group", "小组1", "*************", 8888, ["whiteboard"])
        device.last_seen = datetime.now() - timedelta(seconds=35)  # 设置为过期
        self.module.discovered_devices["device1"] = device
        
        # 模拟设备离线信号
        with patch.object(self.module, 'device_offline') as mock_signal:
            self.module._cleanup_offline_devices()
            
            # 验证设备状态更新
            self.assertEqual(device.status, "offline")
            mock_signal.emit.assert_called_once_with("device1")

class TestUDPDiscoveryWindow(unittest.TestCase):
    """UDP设备发现界面测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.module = Mock()
        self.module.device_discovered = Mock()
        self.module.device_updated = Mock()
        self.module.device_offline = Mock()
        
        self.window = UDPDiscoveryWindow(self.module)
    
    def tearDown(self):
        """清理测试"""
        self.window.close()
    
    def test_window_creation(self):
        """测试窗口创建"""
        self.assertIsNotNone(self.window)
        self.assertEqual(self.window.windowTitle(), "UDP设备发现")
        
        # 检查控件存在
        self.assertIsNotNone(self.window.start_btn)
        self.assertIsNotNone(self.window.stop_btn)
        self.assertIsNotNone(self.window.refresh_btn)
        self.assertIsNotNone(self.window.device_list)
        self.assertIsNotNone(self.window.log_text)
        
        # 检查初始状态
        self.assertTrue(self.window.start_btn.isEnabled())
        self.assertFalse(self.window.stop_btn.isEnabled())
    
    def test_start_discovery_button(self):
        """测试开始发现按钮"""
        # 模拟成功启动
        self.module.start_discovery.return_value = True
        
        # 点击开始按钮
        self.window.start_btn.click()
        
        # 验证模块方法被调用
        self.module.start_discovery.assert_called_once()
        
        # 验证按钮状态
        self.assertFalse(self.window.start_btn.isEnabled())
        self.assertTrue(self.window.stop_btn.isEnabled())
    
    def test_stop_discovery_button(self):
        """测试停止发现按钮"""
        # 先启动
        self.module.start_discovery.return_value = True
        self.window.start_btn.click()
        
        # 点击停止按钮
        self.window.stop_btn.click()
        
        # 验证模块方法被调用
        self.module.stop_discovery.assert_called_once()
        
        # 验证按钮状态
        self.assertTrue(self.window.start_btn.isEnabled())
        self.assertFalse(self.window.stop_btn.isEnabled())
    
    def test_refresh_device_list(self):
        """测试刷新设备列表"""
        # 模拟设备数据
        devices = {
            "device1": DeviceInfo("device1", "teacher", "教师设备", "*************", 8888, []),
            "device2": DeviceInfo("device2", "group", "小组设备", "*************", 8888, [])
        }
        devices["device2"].status = "offline"
        
        self.module.get_discovered_devices.return_value = devices
        
        # 刷新列表
        self.window.refresh_btn.click()
        
        # 验证列表项
        self.assertEqual(self.window.device_list.count(), 2)
        
        # 检查列表项内容
        item1_text = self.window.device_list.item(0).text()
        item2_text = self.window.device_list.item(1).text()
        
        self.assertIn("教师设备", item1_text)
        self.assertIn("*************", item1_text)
        
        self.assertIn("小组设备", item2_text)
        self.assertIn("[离线]", item2_text)
    
    def test_device_discovered_signal(self):
        """测试设备发现信号处理"""
        device = DeviceInfo("new_device", "student", "新学生设备", "192.168.1.103", 8888, [])
        
        # 模拟get_discovered_devices返回空字典
        self.module.get_discovered_devices.return_value = {}
        
        # 模拟信号
        self.window.on_device_discovered(device)
        
        # 检查日志是否添加
        log_content = self.window.log_text.toPlainText()
        self.assertIn("发现新设备: 新学生设备", log_content)
    
    def test_add_log(self):
        """测试添加日志"""
        test_message = "测试日志消息"
        
        self.window.add_log(test_message)
        
        log_content = self.window.log_text.toPlainText()
        self.assertIn(test_message, log_content)
        
        # 检查时间戳格式
        lines = log_content.strip().split('\n')
        self.assertTrue(lines[-1].startswith('['))
        self.assertIn(']', lines[-1])

class TestUDPDiscoveryIntegration(unittest.TestCase):
    """UDP设备发现集成测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.module1 = UDPDiscoveryModule()
        self.module2 = UDPDiscoveryModule()
        
        # 配置不同的设备
        self.module1.set_config({
            "device_type": "teacher",
            "device_name": "教师设备",
            "capabilities": ["whiteboard", "screen_share"]
        })
        
        self.module2.set_config({
            "device_type": "group",
            "device_name": "小组设备",
            "capabilities": ["file_transfer"]
        })
        
        # 使用不同端口避免冲突
        self.module1.discovery_port = 8887
        self.module2.discovery_port = 8887
    
    def tearDown(self):
        """清理测试"""
        self.module1.stop_discovery()
        self.module2.stop_discovery()
        self.module1.cleanup()
        self.module2.cleanup()
    
    @unittest.skip("需要真实网络环境的集成测试")
    def test_device_discovery_integration(self):
        """测试设备发现集成功能"""
        # 初始化模块
        self.module1.initialize()
        self.module2.initialize()
        
        # 设置信号监听
        discovered_devices_1 = []
        discovered_devices_2 = []
        
        def on_device1_discovered(device):
            discovered_devices_1.append(device)
        
        def on_device2_discovered(device):
            discovered_devices_2.append(device)
        
        self.module1.device_discovered.connect(on_device1_discovered)
        self.module2.device_discovered.connect(on_device2_discovered)
        
        # 启动发现
        success1 = self.module1.start_discovery()
        success2 = self.module2.start_discovery()
        
        self.assertTrue(success1)
        self.assertTrue(success2)
        
        # 等待设备发现
        time.sleep(2)
        
        # 验证互相发现
        self.assertGreater(len(discovered_devices_1), 0)
        self.assertGreater(len(discovered_devices_2), 0)
        
        # 验证设备信息
        device1_found = discovered_devices_2[0]
        self.assertEqual(device1_found.device_type, "teacher")
        self.assertEqual(device1_found.device_name, "教师设备")
        
        device2_found = discovered_devices_1[0]
        self.assertEqual(device2_found.device_type, "group")
        self.assertEqual(device2_found.device_name, "小组设备")

def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestDeviceInfo,
        TestUDPDiscoveryProtocol,
        TestUDPDiscoveryModule,
        TestUDPDiscoveryWindow,
        TestUDPDiscoveryIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)