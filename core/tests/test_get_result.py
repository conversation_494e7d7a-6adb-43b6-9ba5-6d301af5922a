#!/usr/bin/env python3
"""
测试 get_result 方法
"""
import sys
import warnings
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *

# 过滤警告
warnings.filterwarnings("ignore", category=DeprecationWarning, module="PyQt5")

def test_get_result():
    """测试 get_result 方法"""
    app = QApplication(sys.argv)
    
    try:
        # 导入修复版登录对话框
        from login_dialog_fixed import LoginDialogFixed
        
        # 创建实例
        dialog = LoginDialogFixed()
        
        # 检查方法是否存在
        print(f"get_result 方法存在: {hasattr(dialog, 'get_result')}")
        
        if hasattr(dialog, 'get_result'):
            # 测试调用
            result = dialog.get_result()
            print(f"get_result() 返回: {result}")
            print(f"返回类型: {type(result)}")
            
            # 设置一些数据并再次测试
            dialog.result_data = {"test": "data"}
            result2 = dialog.get_result()
            print(f"设置数据后 get_result() 返回: {result2}")
            
            return True
        else:
            print("get_result 方法不存在")
            return False
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_in_main():
    """测试在主应用程序中的导入"""
    try:
        # 模拟主应用程序的导入方式
        from login_dialog_fixed import LoginDialogFixed as LoginDialog
        
        dialog = LoginDialog()
        print(f"主应用程序导入方式 - get_result 存在: {hasattr(dialog, 'get_result')}")
        
        if hasattr(dialog, 'get_result'):
            result = dialog.get_result()
            print(f"主应用程序导入方式 - get_result() 返回: {result}")
            return True
        else:
            print("主应用程序导入方式 - get_result 方法不存在")
            return False
            
    except Exception as e:
        print(f"主应用程序导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("get_result 方法测试")
    print("=" * 50)
    
    print("\n1. 直接导入测试:")
    test1 = test_get_result()
    
    print("\n2. 主应用程序导入方式测试:")
    test2 = test_import_in_main()
    
    print(f"\n测试结果:")
    print(f"直接导入: {'✓' if test1 else '✗'}")
    print(f"主应用导入: {'✓' if test2 else '✗'}")
    
    if test1 and test2:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 测试失败！")
