"""
智慧课堂教学工具core集成测试
测试各模块间的协作和整体系统功能
"""
import sys
import os
import unittest
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from module_manager import ModuleManager
from communication_manager import CommunicationManager
from auth_manager import AuthManager
from modules.udp_discovery import UDPDiscoveryModule, DeviceInfo

class TestSystemIntegration(unittest.TestCase):
    """系统集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建核心组件
        self.auth_manager = AuthManager()
        self.comm_manager = CommunicationManager()
        self.module_manager = ModuleManager(self.auth_manager, self.comm_manager)
        
        # 模拟教师登录
        self.auth_manager.login("teacher1", "password", "teacher")
    
    def tearDown(self):
        """清理测试环境"""
        # 停止所有模块
        self.module_manager.stop_all_modules()
        
        # 清理认证
        self.auth_manager.logout()
    
    def test_module_manager_initialization(self):
        """测试模块管理器初始化"""
        # 验证模块管理器初始化成功
        self.assertIsNotNone(self.module_manager.auth_manager)
        self.assertIsNotNone(self.module_manager.comm_manager)
        
        # 验证模块信息加载
        self.assertTrue(len(self.module_manager.available_modules) > 0)
        
        # 验证教师专用模块存在
        teacher_modules = [m for m in self.module_manager.available_modules.values() 
                          if not m.is_common and m.requires_auth]
        self.assertTrue(len(teacher_modules) > 0)
        
        # 验证关键模块存在
        module_names = [m.name for m in self.module_manager.available_modules.values()]
        expected_modules = [
            "device_dashboard", "random_picker", "whiteboard_collaboration",
            "device_control", "dual_screen", "virtual_keyboard", "group_screen_monitor"
        ]
        
        for module_name in expected_modules:
            self.assertIn(module_name, module_names, f"模块 {module_name} 未找到")
    
    @patch('modules.device_dashboard.DeviceDashboardWindow')
    def test_device_dashboard_module_lifecycle(self, mock_window):
        """测试设备看板模块生命周期"""
        # 启动设备看板模块
        result = self.module_manager.start_module("device_dashboard")
        self.assertTrue(result, "设备看板模块启动失败")
        
        # 验证模块已启动
        self.assertIn("device_dashboard", self.module_manager.running_modules)
        
        # 获取模块实例
        module = self.module_manager.get_module_instance("device_dashboard")
        self.assertIsNotNone(module, "无法获取设备看板模块实例")
        
        # 验证模块状态
        self.assertTrue(module.is_running, "设备看板模块未运行")
        
        # 停止模块
        result = self.module_manager.stop_module("device_dashboard")
        self.assertTrue(result, "设备看板模块停止失败")
        
        # 验证模块已停止
        self.assertNotIn("device_dashboard", self.module_manager.running_modules)
    
    @patch('modules.random_picker.RandomPickerWindow')
    def test_random_picker_module_lifecycle(self, mock_window):
        """测试随机点名模块生命周期"""
        # 启动随机点名模块
        result = self.module_manager.start_module("random_picker")
        self.assertTrue(result, "随机点名模块启动失败")
        
        # 验证模块已启动
        self.assertIn("random_picker", self.module_manager.running_modules)
        
        # 获取模块实例
        module = self.module_manager.get_module_instance("random_picker")
        self.assertIsNotNone(module, "无法获取随机点名模块实例")
        
        # 测试模块功能
        self.assertEqual(len(module.courses), 0)  # 初始无课程
        self.assertEqual(len(module.students), 0)  # 初始无学生
        
        # 停止模块
        result = self.module_manager.stop_module("random_picker")
        self.assertTrue(result, "随机点名模块停止失败")
    
    def test_module_communication(self):
        """测试模块间通信"""
        # 启动UDP发现模块和设备看板模块
        with patch('modules.udp_discovery.UDPDiscoveryWindow'), \
             patch('modules.device_dashboard.DeviceDashboardWindow'):
            
            # 启动模块
            self.module_manager.start_module("udp_discovery")
            self.module_manager.start_module("device_dashboard")
            
            # 获取模块实例
            udp_module = self.module_manager.get_module_instance("udp_discovery")
            dashboard_module = self.module_manager.get_module_instance("device_dashboard")
            
            self.assertIsNotNone(udp_module)
            self.assertIsNotNone(dashboard_module)
            
            # 模拟设备发现
            device_info = DeviceInfo("test_device", "group", "测试设备", "192.168.1.100", 8888, ["whiteboard"])
            
            # 触发设备发现事件
            udp_module.discovered_devices["test_device"] = device_info
            udp_module.device_discovered.emit(device_info)
            
            # 验证设备看板模块接收到设备信息
            # 注意：由于是单元测试环境，信号连接可能需要手动处理
            dashboard_module.on_device_discovered(device_info)
            self.assertIn("test_device", dashboard_module.devices)
    
    def test_authentication_integration(self):
        """测试认证集成"""
        # 测试未认证时启动教师专用模块
        self.auth_manager.logout()
        
        with patch('modules.device_dashboard.DeviceDashboardWindow'):
            result = self.module_manager.start_module("device_dashboard")
            self.assertFalse(result, "未认证时不应能启动教师专用模块")
        
        # 重新登录
        self.auth_manager.login("teacher1", "password", "teacher")
        
        with patch('modules.device_dashboard.DeviceDashboardWindow'):
            result = self.module_manager.start_module("device_dashboard")
            self.assertTrue(result, "认证后应能启动教师专用模块")
    
    def test_multiple_modules_startup(self):
        """测试多模块启动"""
        modules_to_test = [
            "device_dashboard",
            "random_picker", 
            "virtual_keyboard"
        ]
        
        # Mock所有窗口类
        with patch('modules.device_dashboard.DeviceDashboardWindow'), \
             patch('modules.random_picker.RandomPickerWindow'), \
             patch('modules.virtual_keyboard.VirtualKeyboardWindow'), \
             patch('subprocess.run') as mock_run:
            
            # Mock dbus-send检查
            mock_run.return_value = Mock(returncode=0)
            
            # 启动多个模块
            for module_name in modules_to_test:
                result = self.module_manager.start_module(module_name)
                self.assertTrue(result, f"模块 {module_name} 启动失败")
            
            # 验证所有模块都在运行
            for module_name in modules_to_test:
                self.assertIn(module_name, self.module_manager.running_modules)
            
            # 获取模块状态
            status = self.module_manager.get_all_module_status()
            self.assertEqual(len(status), len(modules_to_test))
            
            # 停止所有模块
            self.module_manager.stop_all_modules()
            
            # 验证所有模块都已停止
            self.assertEqual(len(self.module_manager.running_modules), 0)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试启动不存在的模块
        result = self.module_manager.start_module("nonexistent_module")
        self.assertFalse(result, "不应能启动不存在的模块")
        
        # 测试重复启动模块
        with patch('modules.device_dashboard.DeviceDashboardWindow'):
            result1 = self.module_manager.start_module("device_dashboard")
            self.assertTrue(result1, "首次启动应成功")
            
            result2 = self.module_manager.start_module("device_dashboard")
            self.assertFalse(result2, "重复启动应失败")
        
        # 测试停止未启动的模块
        result = self.module_manager.stop_module("random_picker")
        self.assertFalse(result, "不应能停止未启动的模块")
    
    def test_configuration_management(self):
        """测试配置管理"""
        with patch('modules.device_dashboard.DeviceDashboardWindow'):
            # 启动模块
            self.module_manager.start_module("device_dashboard")
            module = self.module_manager.get_module_instance("device_dashboard")
            
            # 测试配置读取
            self.assertIsInstance(module.config, dict)
            self.assertTrue(len(module.config) > 0)
            
            # 测试配置修改
            original_value = module.get_config("device_timeout", 30)
            module.set_config("device_timeout", 60)
            new_value = module.get_config("device_timeout", 30)
            self.assertEqual(new_value, 60)
            
            # 恢复原值
            module.set_config("device_timeout", original_value)
    
    def test_logging_integration(self):
        """测试日志集成"""
        with patch('modules.device_dashboard.DeviceDashboardWindow'):
            # 启动模块
            self.module_manager.start_module("device_dashboard")
            module = self.module_manager.get_module_instance("device_dashboard")
            
            # 验证日志记录器存在
            self.assertIsNotNone(module.logger)
            
            # 测试日志记录
            module.logger.info("测试日志消息")
            # 注意：实际的日志验证需要配置日志处理器

class TestModuleInteraction(unittest.TestCase):
    """模块交互测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.auth_manager = AuthManager()
        self.comm_manager = CommunicationManager()
        self.module_manager = ModuleManager(self.auth_manager, self.comm_manager)
        
        # 模拟教师登录
        self.auth_manager.login("teacher1", "password", "teacher")
    
    def tearDown(self):
        """清理测试环境"""
        self.module_manager.stop_all_modules()
        self.auth_manager.logout()
    
    @patch('modules.udp_discovery.UDPDiscoveryWindow')
    @patch('modules.device_control.DeviceControlWindow')
    @patch('subprocess.run')
    def test_device_discovery_and_control_integration(self, mock_run, mock_control_window, mock_udp_window):
        """测试设备发现和设备控制集成"""
        # Mock subprocess调用
        mock_run.return_value = Mock(returncode=0)
        
        # 启动UDP发现和设备控制模块
        self.module_manager.start_module("udp_discovery")
        self.module_manager.start_module("device_control")
        
        udp_module = self.module_manager.get_module_instance("udp_discovery")
        control_module = self.module_manager.get_module_instance("device_control")
        
        # 模拟设备发现
        device_info = DeviceInfo("group1", "group", "小组设备1", "192.168.1.100", 8888, ["control"])
        udp_module.discovered_devices["group1"] = device_info
        
        # 手动触发设备发现事件
        control_module.on_device_discovered(device_info)
        
        # 验证设备控制模块接收到设备
        self.assertIn("group1", control_module.available_devices)
        
        # 测试设备控制功能
        # 注意：实际的设备控制需要真实的网络环境
        self.assertEqual(len(control_module.get_available_groups()), 1)
    
    @patch('modules.random_picker.RandomPickerWindow')
    @patch('modules.dual_screen.DualScreenControlWindow')
    @patch('modules.dual_screen.QApplication')
    def test_random_picker_and_dual_screen_integration(self, mock_app, mock_dual_window, mock_picker_window):
        """测试随机点名和双屏模式集成"""
        # Mock QApplication
        mock_app_instance = Mock()
        mock_desktop = Mock()
        mock_desktop.screenCount.return_value = 2
        mock_desktop.screenGeometry.side_effect = [
            Mock(width=lambda: 1920, height=lambda: 1080, x=lambda: 0, y=lambda: 0),
            Mock(width=lambda: 1920, height=lambda: 1080, x=lambda: 1920, y=lambda: 0)
        ]
        mock_desktop.primaryScreen.return_value = 0
        mock_app_instance.desktop.return_value = mock_desktop
        mock_app.instance.return_value = mock_app_instance
        
        # 启动模块
        self.module_manager.start_module("random_picker")
        self.module_manager.start_module("dual_screen")
        
        picker_module = self.module_manager.get_module_instance("random_picker")
        dual_screen_module = self.module_manager.get_module_instance("dual_screen")
        
        # 验证模块启动成功
        self.assertIsNotNone(picker_module)
        self.assertIsNotNone(dual_screen_module)
        
        # 验证双屏检测
        self.assertEqual(len(dual_screen_module.available_screens), 2)
    
    def test_communication_manager_message_routing(self):
        """测试通信管理器消息路由"""
        with patch('modules.device_dashboard.DeviceDashboardWindow'), \
             patch('modules.random_picker.RandomPickerWindow'):
            
            # 启动模块
            self.module_manager.start_module("device_dashboard")
            self.module_manager.start_module("random_picker")
            
            dashboard_module = self.module_manager.get_module_instance("device_dashboard")
            picker_module = self.module_manager.get_module_instance("random_picker")
            
            # 测试模块间消息发送
            dashboard_module.send_message("random_picker", "test_message", {"data": "test"})
            
            # 验证通信管理器处理消息
            # 注意：实际的消息验证需要监听消息事件

class TestPerformance(unittest.TestCase):
    """性能测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.auth_manager = AuthManager()
        self.comm_manager = CommunicationManager()
        self.module_manager = ModuleManager(self.auth_manager, self.comm_manager)
        
        # 模拟教师登录
        self.auth_manager.login("teacher1", "password", "teacher")
    
    def tearDown(self):
        """清理测试环境"""
        self.module_manager.stop_all_modules()
        self.auth_manager.logout()
    
    def test_module_startup_performance(self):
        """测试模块启动性能"""
        modules_to_test = ["device_dashboard", "random_picker", "virtual_keyboard"]
        
        with patch('modules.device_dashboard.DeviceDashboardWindow'), \
             patch('modules.random_picker.RandomPickerWindow'), \
             patch('modules.virtual_keyboard.VirtualKeyboardWindow'), \
             patch('subprocess.run') as mock_run:
            
            mock_run.return_value = Mock(returncode=0)
            
            # 测量启动时间
            start_time = time.time()
            
            for module_name in modules_to_test:
                self.module_manager.start_module(module_name)
            
            end_time = time.time()
            startup_time = end_time - start_time
            
            # 验证启动时间合理（应在5秒内）
            self.assertLess(startup_time, 5.0, f"模块启动时间过长: {startup_time:.2f}秒")
            
            # 验证所有模块都启动成功
            self.assertEqual(len(self.module_manager.running_modules), len(modules_to_test))
    
    def test_memory_usage(self):
        """测试内存使用"""
        import psutil
        import os
        
        # 获取当前进程
        process = psutil.Process(os.getpid())
        
        # 记录初始内存使用
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        with patch('modules.device_dashboard.DeviceDashboardWindow'), \
             patch('modules.random_picker.RandomPickerWindow'):
            
            # 启动多个模块
            self.module_manager.start_module("device_dashboard")
            self.module_manager.start_module("random_picker")
            
            # 记录启动后内存使用
            after_startup_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # 停止模块
            self.module_manager.stop_all_modules()
            
            # 记录停止后内存使用
            after_cleanup_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # 验证内存使用合理
            memory_increase = after_startup_memory - initial_memory
            self.assertLess(memory_increase, 100, f"内存使用增长过多: {memory_increase:.2f}MB")
            
            # 验证内存释放
            memory_after_cleanup = after_cleanup_memory - initial_memory
            self.assertLess(memory_after_cleanup, memory_increase * 1.5, "内存未正确释放")

if __name__ == '__main__':
    # 运行集成测试
    unittest.main(verbosity=2)
