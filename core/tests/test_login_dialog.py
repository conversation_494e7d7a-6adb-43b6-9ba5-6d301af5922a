#!/usr/bin/env python3
"""
登录对话框测试脚本
"""
import sys
import warnings
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *

# 过滤PyQt5的弃用警告
warnings.filterwarnings("ignore", category=DeprecationWarning, module="PyQt5")

# 设置高DPI支持
QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

def test_login_dialog():
    """测试登录对话框"""
    app = QApplication(sys.argv)
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 10)
    app.setFont(font)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 导入并创建登录对话框
    from login_dialog import LoginDialog
    
    dialog = LoginDialog()
    
    # 显示对话框
    result = dialog.exec_()
    
    if result == QDialog.Accepted:
        print("登录成功")
        if hasattr(dialog, 'result_data') and dialog.result_data:
            print(f"用户名: {dialog.result_data.get('username', 'N/A')}")
            print(f"角色: {dialog.result_data.get('role', 'N/A')}")
    else:
        print("登录取消")
    
    return result

if __name__ == "__main__":
    try:
        result = test_login_dialog()
        sys.exit(0 if result == QDialog.Accepted else 1)
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
