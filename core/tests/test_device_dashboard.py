"""
设备看板模块测试
"""
import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.device_dashboard import DeviceDashboardModule, DeviceStats, DeviceAlert
from modules.udp_discovery import DeviceInfo

class TestDeviceStats(unittest.TestCase):
    """测试设备统计类"""
    
    def setUp(self):
        self.stats = DeviceStats()
    
    def test_initial_stats(self):
        """测试初始统计数据"""
        self.assertEqual(self.stats.total_devices, 0)
        self.assertEqual(self.stats.online_devices, 0)
        self.assertEqual(self.stats.offline_devices, 0)
        self.assertEqual(self.stats.teacher_devices, 0)
        self.assertEqual(self.stats.group_devices, 0)
        self.assertEqual(self.stats.student_devices, 0)
    
    def test_update_from_devices(self):
        """测试从设备列表更新统计"""
        # 创建测试设备
        devices = {
            "device1": DeviceInfo("device1", "teacher", "教师设备1", "192.168.1.10", 8888, ["whiteboard"]),
            "device2": DeviceInfo("device2", "group", "小组设备1", "192.168.1.11", 8888, ["screen_share"]),
            "device3": DeviceInfo("device3", "student", "学生设备1", "192.168.1.12", 8888, ["file_transfer"]),
        }
        
        # 设置一个设备为离线
        devices["device3"].status = "offline"
        
        # 更新统计
        self.stats.update_from_devices(devices)
        
        # 验证统计结果
        self.assertEqual(self.stats.total_devices, 3)
        self.assertEqual(self.stats.online_devices, 2)
        self.assertEqual(self.stats.offline_devices, 1)
        self.assertEqual(self.stats.teacher_devices, 1)
        self.assertEqual(self.stats.group_devices, 1)
        self.assertEqual(self.stats.student_devices, 1)

class TestDeviceAlert(unittest.TestCase):
    """测试设备告警类"""
    
    def test_alert_creation(self):
        """测试告警创建"""
        alert = DeviceAlert("device1", "offline", "设备离线", "warning")
        
        self.assertEqual(alert.device_id, "device1")
        self.assertEqual(alert.alert_type, "offline")
        self.assertEqual(alert.message, "设备离线")
        self.assertEqual(alert.severity, "warning")
        self.assertFalse(alert.acknowledged)
        self.assertIsInstance(alert.timestamp, datetime)
    
    def test_alert_to_dict(self):
        """测试告警转换为字典"""
        alert = DeviceAlert("device1", "offline", "设备离线", "warning")
        alert_dict = alert.to_dict()
        
        self.assertIn("device_id", alert_dict)
        self.assertIn("alert_type", alert_dict)
        self.assertIn("message", alert_dict)
        self.assertIn("severity", alert_dict)
        self.assertIn("timestamp", alert_dict)
        self.assertIn("acknowledged", alert_dict)

class TestDeviceDashboardModule(unittest.TestCase):
    """测试设备看板模块"""
    
    def setUp(self):
        """设置测试环境"""
        # Mock认证管理器
        self.mock_auth_manager = Mock()
        self.mock_auth_manager.is_authenticated.return_value = True
        self.mock_auth_manager.get_current_user.return_value = {
            "id": "teacher1",
            "name": "测试教师",
            "role": "teacher"
        }
        
        # 创建设备看板模块
        self.dashboard = DeviceDashboardModule()
        self.dashboard.auth_manager = self.mock_auth_manager
    
    @patch('modules.device_dashboard.DeviceDashboardWindow')
    def test_module_initialization(self, mock_window):
        """测试模块初始化"""
        # Mock UDP发现模块
        mock_udp_discovery = Mock()
        mock_udp_discovery.device_discovered = Mock()
        mock_udp_discovery.device_updated = Mock()
        mock_udp_discovery.device_offline = Mock()
        mock_udp_discovery.get_discovered_devices.return_value = {}
        
        # Mock获取UDP发现模块的方法
        self.dashboard._get_udp_discovery_module = Mock(return_value=mock_udp_discovery)
        
        # 初始化模块
        result = self.dashboard._initialize_module()
        
        # 验证初始化成功
        self.assertTrue(result)
        self.assertIsNotNone(self.dashboard.udp_discovery)
        self.assertIsNotNone(self.dashboard.dashboard_window)
        
        # 验证信号连接
        mock_udp_discovery.device_discovered.connect.assert_called()
        mock_udp_discovery.device_updated.connect.assert_called()
        mock_udp_discovery.device_offline.connect.assert_called()
    
    def test_module_initialization_without_auth(self):
        """测试未认证时的模块初始化"""
        # 设置未认证状态
        self.mock_auth_manager.is_authenticated.return_value = False
        
        # 初始化模块
        result = self.dashboard._initialize_module()
        
        # 验证初始化失败
        self.assertFalse(result)
    
    def test_device_stats_update(self):
        """测试设备统计更新"""
        # 创建测试设备
        devices = {
            "device1": DeviceInfo("device1", "teacher", "教师设备1", "192.168.1.10", 8888, ["whiteboard"]),
            "device2": DeviceInfo("device2", "group", "小组设备1", "192.168.1.11", 8888, ["screen_share"]),
        }
        
        self.dashboard.devices = devices
        
        # 更新统计
        self.dashboard.update_device_stats()
        
        # 验证统计结果
        self.assertEqual(self.dashboard.device_stats.total_devices, 2)
        self.assertEqual(self.dashboard.device_stats.online_devices, 2)
        self.assertEqual(self.dashboard.device_stats.teacher_devices, 1)
        self.assertEqual(self.dashboard.device_stats.group_devices, 1)
    
    def test_add_alert(self):
        """测试添加告警"""
        # 创建告警
        alert = DeviceAlert("device1", "offline", "设备离线", "warning")
        
        # 添加告警
        self.dashboard.add_alert(alert)
        
        # 验证告警已添加
        self.assertEqual(len(self.dashboard.alerts), 1)
        self.assertEqual(self.dashboard.alerts[0], alert)
    
    def test_add_duplicate_alert(self):
        """测试添加重复告警"""
        # 创建相同的告警
        alert1 = DeviceAlert("device1", "offline", "设备离线", "warning")
        alert2 = DeviceAlert("device1", "offline", "设备离线", "warning")
        
        # 添加告警
        self.dashboard.add_alert(alert1)
        self.dashboard.add_alert(alert2)
        
        # 验证只有一个告警
        self.assertEqual(len(self.dashboard.alerts), 1)
    
    def test_acknowledge_alert(self):
        """测试确认告警"""
        # 创建告警
        alert = DeviceAlert("device1", "offline", "设备离线", "warning")
        self.dashboard.add_alert(alert)
        
        # 确认告警
        self.dashboard.acknowledge_alert(0)
        
        # 验证告警已确认
        self.assertTrue(self.dashboard.alerts[0].acknowledged)
    
    def test_device_discovered_event(self):
        """测试设备发现事件"""
        # 创建设备信息
        device_info = DeviceInfo("device1", "group", "小组设备1", "192.168.1.11", 8888, ["screen_share"])
        
        # 触发设备发现事件
        self.dashboard.on_device_discovered(device_info)
        
        # 验证设备已添加
        self.assertIn("device1", self.dashboard.devices)
        self.assertEqual(self.dashboard.devices["device1"], device_info)
        
        # 验证统计已更新
        self.assertEqual(self.dashboard.device_stats.total_devices, 1)
        self.assertEqual(self.dashboard.device_stats.group_devices, 1)
    
    def test_device_offline_event(self):
        """测试设备离线事件"""
        # 先添加一个在线设备
        device_info = DeviceInfo("device1", "group", "小组设备1", "192.168.1.11", 8888, ["screen_share"])
        self.dashboard.devices["device1"] = device_info
        
        # 触发设备离线事件
        self.dashboard.on_device_offline("device1")
        
        # 验证设备状态已更新
        self.assertEqual(self.dashboard.devices["device1"].status, "offline")
        
        # 验证统计已更新
        self.assertEqual(self.dashboard.device_stats.offline_devices, 1)
    
    def test_check_device_alerts(self):
        """测试设备告警检查"""
        # 创建一个长时间离线的设备
        device_info = DeviceInfo("device1", "group", "小组设备1", "192.168.1.11", 8888, ["screen_share"])
        device_info.status = "offline"
        device_info.last_seen = datetime.now() - timedelta(seconds=60)  # 60秒前
        self.dashboard.devices["device1"] = device_info
        
        # 设置超时时间为30秒
        self.dashboard.config["device_timeout"] = 30
        
        # 检查告警
        self.dashboard.check_device_alerts()
        
        # 验证生成了离线告警
        self.assertTrue(len(self.dashboard.alerts) > 0)
        offline_alerts = [alert for alert in self.dashboard.alerts if alert.alert_type == "offline"]
        self.assertTrue(len(offline_alerts) > 0)
    
    def test_message_handling(self):
        """测试消息处理"""
        # 测试获取设备统计消息
        self.dashboard._handle_message("test_sender", "get_device_stats", {})
        
        # 测试获取设备列表消息
        self.dashboard._handle_message("test_sender", "get_device_list", {})
        
        # 测试获取告警消息
        self.dashboard._handle_message("test_sender", "get_alerts", {})
        
        # 测试确认告警消息
        alert = DeviceAlert("device1", "offline", "设备离线", "warning")
        self.dashboard.add_alert(alert)
        self.dashboard._handle_message("test_sender", "acknowledge_alert", {"alert_id": 0})
        
        # 验证告警已确认
        self.assertTrue(self.dashboard.alerts[0].acknowledged)

if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
