#!/usr/bin/env python3
"""
完整的登录流程测试
"""
import sys
import warnings
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *

# 过滤PyQt5的弃用警告
warnings.filterwarnings("ignore", category=DeprecationWarning, module="PyQt5")

# 设置高DPI支持
QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

def test_login_dialog_methods():
    """测试登录对话框的方法"""
    print("测试登录对话框方法...")
    
    try:
        app = QApplication(sys.argv)
        app.setFont(QFont("Microsoft YaHei UI", 10))
        
        from login_dialog_fixed import LoginDialogFixed
        
        # 创建对话框实例
        dialog = LoginDialogFixed()
        
        # 检查必要的方法
        methods_to_check = ['get_result', 'on_login_clicked', 'on_guest_clicked', 'show_status']
        
        for method_name in methods_to_check:
            if hasattr(dialog, method_name):
                print(f"✓ 方法 {method_name} 存在")
            else:
                print(f"✗ 方法 {method_name} 缺失")
                return False
        
        # 测试初始状态
        initial_result = dialog.get_result()
        print(f"✓ 初始结果: {initial_result}")
        
        # 模拟登录成功
        dialog.result_data = {
            "mode": "login",
            "username": "admin",
            "password": "admin",
            "role": "teacher",
            "remember": False
        }
        
        result = dialog.get_result()
        print(f"✓ 登录结果: {result}")
        
        # 检查结果格式
        required_keys = ['mode', 'username', 'role']
        for key in required_keys:
            if key in result:
                print(f"✓ 结果包含 {key}: {result[key]}")
            else:
                print(f"✗ 结果缺少 {key}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_app_integration():
    """测试主应用程序集成"""
    print("\n测试主应用程序集成...")
    
    try:
        # 检查导入
        from main_app import CoreApplication
        print("✓ 主应用程序导入成功")
        
        # 检查登录对话框导入
        from login_dialog_fixed import LoginDialogFixed as LoginDialog
        print("✓ 登录对话框导入成功")
        
        # 创建应用程序实例（不启动）
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        core_app = CoreApplication(sys.argv)
        print("✓ 主应用程序实例创建成功")
        
        # 测试登录对话框创建
        dialog = LoginDialog()
        print("✓ 登录对话框创建成功")
        
        # 检查get_result方法
        if hasattr(dialog, 'get_result'):
            print("✓ get_result 方法存在")
            result = dialog.get_result()
            print(f"✓ get_result 返回: {result}")
        else:
            print("✗ get_result 方法缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_ui_display():
    """测试登录界面显示"""
    print("\n测试登录界面显示...")
    
    try:
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        from login_dialog_fixed import LoginDialogFixed
        
        dialog = LoginDialogFixed()
        
        # 检查UI元素
        ui_elements = [
            ('username_edit', '用户名输入框'),
            ('password_edit', '密码输入框'),
            ('remember_checkbox', '记住密码复选框'),
            ('login_btn', '登录按钮'),
            ('cancel_btn', '取消按钮'),
            ('guest_btn', '游客模式按钮'),
            ('status_label', '状态标签')
        ]
        
        for attr_name, description in ui_elements:
            if hasattr(dialog, attr_name):
                element = getattr(dialog, attr_name)
                if element:
                    print(f"✓ {description} 存在")
                else:
                    print(f"✗ {description} 为空")
            else:
                print(f"✗ {description} 缺失")
                return False
        
        # 检查默认值
        if dialog.username_edit.text() == "admin":
            print("✓ 默认用户名设置正确")
        else:
            print(f"? 默认用户名: '{dialog.username_edit.text()}'")
        
        if dialog.password_edit.text() == "admin":
            print("✓ 默认密码设置正确")
        else:
            print(f"? 默认密码: '{dialog.password_edit.text()}'")
        
        return True
        
    except Exception as e:
        print(f"✗ UI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("登录系统完整测试")
    print("=" * 60)
    
    tests = [
        ("登录对话框方法测试", test_login_dialog_methods),
        ("主应用程序集成测试", test_main_app_integration),
        ("登录界面显示测试", test_login_ui_display)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！登录系统工作正常。")
        print("\n现在可以正常启动应用程序:")
        print("  python main_app.py")
        return 0
    else:
        print("❌ 部分测试失败，请检查错误信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
