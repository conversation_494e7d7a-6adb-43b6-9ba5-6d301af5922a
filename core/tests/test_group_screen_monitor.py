"""
小组屏幕看板模块测试
"""
import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.group_screen_monitor import (
    GroupScreenMonitorModule, StreamInfo, LayoutManager, VideoStreamWidget
)
from modules.udp_discovery import DeviceInfo

class TestStreamInfo(unittest.TestCase):
    """测试视频流信息类"""
    
    def test_stream_info_creation(self):
        """测试流信息创建"""
        stream_info = StreamInfo("stream1", "device1", "小组设备1", "rtsp://localhost:8554/device1")
        
        self.assertEqual(stream_info.stream_id, "stream1")
        self.assertEqual(stream_info.device_id, "device1")
        self.assertEqual(stream_info.device_name, "小组设备1")
        self.assertEqual(stream_info.stream_url, "rtsp://localhost:8554/device1")
        self.assertEqual(stream_info.resolution, "1920x1080")
        self.assertFalse(stream_info.is_active)
        self.assertEqual(stream_info.viewer_count, 0)
        self.assertIsInstance(stream_info.last_update, datetime)
    
    def test_stream_info_to_dict(self):
        """测试流信息转换为字典"""
        stream_info = StreamInfo("stream1", "device1", "小组设备1", "rtsp://localhost:8554/device1")
        stream_info.is_active = True
        stream_info.viewer_count = 2
        
        stream_dict = stream_info.to_dict()
        
        self.assertIn("stream_id", stream_dict)
        self.assertIn("device_id", stream_dict)
        self.assertIn("device_name", stream_dict)
        self.assertIn("stream_url", stream_dict)
        self.assertIn("resolution", stream_dict)
        self.assertIn("is_active", stream_dict)
        self.assertIn("last_update", stream_dict)
        self.assertIn("viewer_count", stream_dict)
        
        self.assertEqual(stream_dict["stream_id"], "stream1")
        self.assertTrue(stream_dict["is_active"])
        self.assertEqual(stream_dict["viewer_count"], 2)

class TestLayoutManager(unittest.TestCase):
    """测试布局管理器类"""
    
    def test_get_layout_info(self):
        """测试获取布局信息"""
        # 测试2x2网格布局
        layout_info = LayoutManager.get_layout_info("grid_2x2")
        self.assertEqual(layout_info["rows"], 2)
        self.assertEqual(layout_info["cols"], 2)
        self.assertEqual(layout_info["max_streams"], 4)
        
        # 测试3x3网格布局
        layout_info = LayoutManager.get_layout_info("grid_3x3")
        self.assertEqual(layout_info["rows"], 3)
        self.assertEqual(layout_info["cols"], 3)
        self.assertEqual(layout_info["max_streams"], 9)
        
        # 测试单屏布局
        layout_info = LayoutManager.get_layout_info("single")
        self.assertEqual(layout_info["rows"], 1)
        self.assertEqual(layout_info["cols"], 1)
        self.assertEqual(layout_info["max_streams"], 1)
        
        # 测试不存在的布局（应返回默认布局）
        layout_info = LayoutManager.get_layout_info("nonexistent")
        self.assertEqual(layout_info["rows"], 2)
        self.assertEqual(layout_info["cols"], 2)
        self.assertEqual(layout_info["max_streams"], 4)
    
    def test_get_available_layouts(self):
        """测试获取可用布局列表"""
        layouts = LayoutManager.get_available_layouts()
        
        self.assertIn("grid_2x2", layouts)
        self.assertIn("grid_3x3", layouts)
        self.assertIn("grid_4x4", layouts)
        self.assertIn("single", layouts)
        self.assertIn("pip", layouts)
        
        self.assertEqual(len(layouts), 5)

class TestVideoStreamWidget(unittest.TestCase):
    """测试视频流显示组件"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建QApplication实例（如果不存在）
        from PyQt5.QtWidgets import QApplication
        if not QApplication.instance():
            self.app = QApplication([])
        else:
            self.app = QApplication.instance()
    
    def test_video_stream_widget_creation(self):
        """测试视频流组件创建"""
        stream_info = StreamInfo("stream1", "device1", "小组设备1", "rtsp://localhost:8554/device1")
        widget = VideoStreamWidget(stream_info)
        
        self.assertEqual(widget.stream_info, stream_info)
        self.assertIsNotNone(widget.media_player)
        self.assertIsNotNone(widget.video_widget)
        self.assertIsNotNone(widget.info_label)
        self.assertIsNotNone(widget.play_btn)
        self.assertIsNotNone(widget.fullscreen_btn)
        self.assertIsNotNone(widget.status_label)
    
    def test_video_stream_widget_controls(self):
        """测试视频流组件控制"""
        stream_info = StreamInfo("stream1", "device1", "小组设备1", "rtsp://localhost:8554/device1")
        widget = VideoStreamWidget(stream_info)
        
        # 测试初始状态
        self.assertEqual(widget.play_btn.text(), "播放")
        self.assertEqual(widget.fullscreen_btn.text(), "全屏")
        self.assertEqual(widget.status_label.text(), "未连接")
        
        # 测试全屏切换
        widget.toggle_fullscreen()
        self.assertEqual(widget.fullscreen_btn.text(), "退出全屏")

class TestGroupScreenMonitorModule(unittest.TestCase):
    """测试小组屏幕看板模块"""
    
    def setUp(self):
        """设置测试环境"""
        # Mock认证管理器
        self.mock_auth_manager = Mock()
        self.mock_auth_manager.is_authenticated.return_value = True
        self.mock_auth_manager.get_current_user.return_value = {
            "id": "teacher1",
            "name": "测试教师",
            "role": "teacher"
        }
        
        # 创建小组屏幕看板模块
        self.monitor = GroupScreenMonitorModule()
        self.monitor.auth_manager = self.mock_auth_manager
    
    @patch('modules.group_screen_monitor.GroupScreenMonitorWindow')
    @patch('requests.get')
    def test_module_initialization(self, mock_requests, mock_window):
        """测试模块初始化"""
        # Mock MediaMTX检查
        mock_requests.return_value = Mock(status_code=200)
        
        # 初始化模块
        result = self.monitor._initialize_module()
        
        # 验证初始化成功
        self.assertTrue(result)
        self.assertIsNotNone(self.monitor.monitor_window)
    
    def test_module_initialization_without_auth(self):
        """测试未认证时的模块初始化"""
        # 设置未认证状态
        self.mock_auth_manager.is_authenticated.return_value = False
        
        # 初始化模块
        result = self.monitor._initialize_module()
        
        # 验证初始化失败
        self.assertFalse(result)
    
    @patch('requests.get')
    def test_check_mediamtx_available(self, mock_requests):
        """测试MediaMTX可用性检查"""
        # 测试MediaMTX可用
        mock_requests.return_value = Mock(status_code=200)
        result = self.monitor._check_mediamtx_available()
        self.assertTrue(result)
        
        # 测试MediaMTX不可用
        mock_requests.side_effect = Exception("Connection failed")
        result = self.monitor._check_mediamtx_available()
        self.assertFalse(result)
    
    @patch('modules.group_screen_monitor.VideoStreamWidget')
    def test_add_stream(self, mock_widget_class):
        """测试添加视频流"""
        # 添加测试设备
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["screen_share"])
        self.monitor.available_devices["group1"] = device
        
        # Mock视频流组件
        mock_widget = Mock()
        mock_widget_class.return_value = mock_widget
        
        # 添加流
        stream_id = self.monitor.add_stream("group1", "rtsp://localhost:8554/group1")
        
        # 验证流添加成功
        self.assertIsNotNone(stream_id)
        self.assertIn(stream_id, self.monitor.active_streams)
        self.assertIn(stream_id, self.monitor.stream_widgets)
        
        stream_info = self.monitor.active_streams[stream_id]
        self.assertEqual(stream_info.device_id, "group1")
        self.assertEqual(stream_info.device_name, "小组设备1")
        self.assertTrue(stream_info.is_active)
        
        # 验证视频组件被创建和启动
        mock_widget_class.assert_called_once()
        mock_widget.start_stream.assert_called_once()
    
    def test_add_stream_device_not_found(self):
        """测试设备不存在时添加流"""
        # 添加不存在的设备流
        stream_id = self.monitor.add_stream("nonexistent", "rtsp://localhost:8554/nonexistent")
        
        # 验证添加失败
        self.assertIsNone(stream_id)
        self.assertEqual(len(self.monitor.active_streams), 0)
    
    def test_add_stream_max_limit(self):
        """测试达到最大流数时添加流"""
        # 设置最大流数为1
        self.monitor.config["max_concurrent_streams"] = 1
        
        # 添加测试设备
        device1 = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["screen_share"])
        device2 = DeviceInfo("group2", "group", "小组设备2", "*************", 8888, ["screen_share"])
        self.monitor.available_devices["group1"] = device1
        self.monitor.available_devices["group2"] = device2
        
        # Mock视频流组件
        with patch('modules.group_screen_monitor.VideoStreamWidget'):
            # 添加第一个流
            stream_id1 = self.monitor.add_stream("group1", "rtsp://localhost:8554/group1")
            self.assertIsNotNone(stream_id1)
            
            # 尝试添加第二个流
            stream_id2 = self.monitor.add_stream("group2", "rtsp://localhost:8554/group2")
            self.assertIsNone(stream_id2)  # 应该失败
            
            # 验证只有一个流
            self.assertEqual(len(self.monitor.active_streams), 1)
    
    def test_remove_stream(self):
        """测试移除视频流"""
        # 先添加一个流
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["screen_share"])
        self.monitor.available_devices["group1"] = device
        
        with patch('modules.group_screen_monitor.VideoStreamWidget') as mock_widget_class:
            mock_widget = Mock()
            mock_widget_class.return_value = mock_widget
            
            stream_id = self.monitor.add_stream("group1", "rtsp://localhost:8554/group1")
            
            # 验证流已添加
            self.assertIsNotNone(stream_id)
            self.assertIn(stream_id, self.monitor.active_streams)
            
            # 移除流
            self.monitor.remove_stream(stream_id)
            
            # 验证流已移除
            self.assertNotIn(stream_id, self.monitor.active_streams)
            self.assertNotIn(stream_id, self.monitor.stream_widgets)
            
            # 验证视频组件被停止
            mock_widget.stop_stream.assert_called_once()
    
    def test_remove_stream_nonexistent(self):
        """测试移除不存在的流"""
        # 移除不存在的流
        self.monitor.remove_stream("nonexistent")
        
        # 应该没有异常抛出
        self.assertEqual(len(self.monitor.active_streams), 0)
    
    def test_change_layout(self):
        """测试改变布局"""
        # Mock监控窗口
        self.monitor.monitor_window = Mock()
        
        # 改变布局
        self.monitor.change_layout("grid_3x3")
        
        # 验证布局已改变
        self.assertEqual(self.monitor.current_layout, "grid_3x3")
        
        # 验证窗口更新方法被调用
        self.monitor.monitor_window.update_layout.assert_called_once()
    
    def test_change_layout_invalid(self):
        """测试改变到无效布局"""
        original_layout = self.monitor.current_layout
        
        # 尝试改变到无效布局
        self.monitor.change_layout("invalid_layout")
        
        # 验证布局没有改变
        self.assertEqual(self.monitor.current_layout, original_layout)
    
    def test_select_stream(self):
        """测试选择流"""
        # 添加测试流
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["screen_share"])
        self.monitor.available_devices["group1"] = device
        
        with patch('modules.group_screen_monitor.VideoStreamWidget'):
            stream_id = self.monitor.add_stream("group1", "rtsp://localhost:8554/group1")
            
            # 选择流
            self.monitor.select_stream(stream_id)
            
            # 验证流已选择
            self.assertEqual(self.monitor.selected_stream, stream_id)
    
    def test_auto_adjust_layout(self):
        """测试自动调整布局"""
        # Mock change_layout方法
        self.monitor.change_layout = Mock()
        
        # 测试不同流数的自动布局
        
        # 0个流
        self.monitor._auto_adjust_layout()
        self.monitor.change_layout.assert_not_called()
        
        # 1个流
        self.monitor.active_streams = {"stream1": Mock()}
        self.monitor._auto_adjust_layout()
        self.monitor.change_layout.assert_called_with("single")
        
        # 4个流
        self.monitor.active_streams = {f"stream{i}": Mock() for i in range(1, 5)}
        self.monitor._auto_adjust_layout()
        self.monitor.change_layout.assert_called_with("grid_2x2")
        
        # 9个流
        self.monitor.active_streams = {f"stream{i}": Mock() for i in range(1, 10)}
        self.monitor._auto_adjust_layout()
        self.monitor.change_layout.assert_called_with("grid_3x3")
        
        # 16个流
        self.monitor.active_streams = {f"stream{i}": Mock() for i in range(1, 17)}
        self.monitor._auto_adjust_layout()
        self.monitor.change_layout.assert_called_with("grid_4x4")
    
    def test_get_available_devices(self):
        """测试获取可用设备"""
        # 添加测试设备
        device1 = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["screen_share"])
        device2 = DeviceInfo("group2", "group", "小组设备2", "*************", 8888, ["whiteboard"])  # 不支持屏幕广播
        device3 = DeviceInfo("group3", "group", "小组设备3", "192.168.1.102", 8888, ["screen_share"])
        device3.status = "offline"  # 离线设备
        
        self.monitor.available_devices = {
            "group1": device1,
            "group2": device2,
            "group3": device3
        }
        
        # 获取可用设备
        available = self.monitor.get_available_devices()
        
        # 验证结果（只有支持屏幕广播且在线的设备）
        self.assertEqual(len(available), 1)
        self.assertEqual(available[0].device_id, "group1")
    
    def test_get_monitor_status(self):
        """测试获取监控状态"""
        # 添加测试数据
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["screen_share"])
        self.monitor.available_devices["group1"] = device
        
        with patch('modules.group_screen_monitor.VideoStreamWidget'):
            stream_id = self.monitor.add_stream("group1", "rtsp://localhost:8554/group1")
            self.monitor.select_stream(stream_id)
        
        # 获取状态
        status = self.monitor.get_monitor_status()
        
        # 验证状态信息
        self.assertEqual(status["available_devices"], 1)
        self.assertEqual(status["active_streams"], 1)
        self.assertEqual(status["current_layout"], "grid_2x2")  # 默认布局
        self.assertEqual(status["selected_stream"], stream_id)
        self.assertEqual(len(status["streams"]), 1)
    
    def test_message_handling(self):
        """测试消息处理"""
        # 添加测试设备
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["screen_share"])
        self.monitor.available_devices["group1"] = device
        
        # Mock相关方法
        self.monitor.add_stream = Mock(return_value="stream1")
        self.monitor.remove_stream = Mock()
        self.monitor.change_layout = Mock()
        self.monitor.select_stream = Mock()
        
        # 测试添加流消息
        self.monitor._handle_message("test_sender", "add_stream", {
            "device_id": "group1", "stream_url": "rtsp://localhost:8554/group1"
        })
        self.monitor.add_stream.assert_called_with("group1", "rtsp://localhost:8554/group1")
        
        # 测试移除流消息
        self.monitor._handle_message("test_sender", "remove_stream", {"stream_id": "stream1"})
        self.monitor.remove_stream.assert_called_with("stream1")
        
        # 测试改变布局消息
        self.monitor._handle_message("test_sender", "change_layout", {"layout_name": "grid_3x3"})
        self.monitor.change_layout.assert_called_with("grid_3x3")
        
        # 测试选择流消息
        self.monitor._handle_message("test_sender", "select_stream", {"stream_id": "stream1"})
        self.monitor.select_stream.assert_called_with("stream1")
        
        # 测试获取状态消息
        self.monitor._handle_message("test_sender", "get_monitor_status", {})
        
        # 测试显示界面消息
        self.monitor._handle_message("test_sender", "show_monitor", {})

if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
