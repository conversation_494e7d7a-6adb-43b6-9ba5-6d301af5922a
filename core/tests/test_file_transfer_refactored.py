"""
文件传输模块单元测试
"""
import unittest
import sys
import os
import tempfile
import json
import socket
import threading
import time
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QPoint

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.file_transfer_refactored import FileTransferModule, FileTransferWindow

class TestFileTransferModule(unittest.TestCase):
    """文件传输模块测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.module = FileTransferModule()
        
        # 模拟通信管理器
        self.module.comm_manager = Mock()
        self.module.message_bus = Mock()
        
        # 使用临时目录作为共享文件夹
        self.temp_dir = tempfile.mkdtemp()
        self.module.shared_folder = self.temp_dir
        
        # 初始化模块
        self.module.initialize()
    
    def tearDown(self):
        """每个测试后的清理"""
        if self.module:
            self.module.cleanup()
        
        # 清理临时目录
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_module_initialization(self):
        """测试模块初始化"""
        self.assertTrue(self.module.is_initialized)
        self.assertEqual(self.module.module_name, "file_transfer")
        self.assertEqual(self.module.display_name, "文件传输")
        self.assertIsNotNone(self.module.transfer_window)
        self.assertTrue(os.path.exists(self.module.shared_folder))
    
    def test_get_local_ip(self):
        """测试获取本地IP"""
        ip = self.module.get_local_ip()
        self.assertIsInstance(ip, str)
        self.assertTrue(len(ip) > 0)
        # 验证IP格式（简单验证）
        parts = ip.split('.')
        if ip != "127.0.0.1":
            self.assertEqual(len(parts), 4)
    
    def test_port_availability(self):
        """测试端口可用性检查"""
        # 测试一个通常不会被占用的高端口
        test_port = 65000
        is_available = self.module.is_port_available(test_port)
        self.assertIsInstance(is_available, bool)
        
        # 测试查找可用端口
        available_port = self.module.find_available_port(test_port)
        self.assertIsInstance(available_port, int)
        self.assertGreaterEqual(available_port, test_port)
    
    def test_server_start_stop(self):
        """测试服务器启动和停止"""
        # 测试启动服务器
        result = self.module.start_server(port=0)  # 使用系统分配的端口
        if result:
            self.assertIsNotNone(self.module.server)
            self.assertIsNotNone(self.module.server_thread)
            
            # 测试停止服务器
            self.module.stop_server()
            self.assertIsNone(self.module.server)
    
    def test_qr_code_generation(self):
        """测试二维码生成"""
        # 先启动服务器
        if self.module.start_server(port=0):
            url, qr_path = self.module.generate_qr_code()
            
            self.assertIsNotNone(url)
            self.assertIsNotNone(qr_path)
            self.assertTrue(url.startswith("http://"))
            
            if qr_path:
                self.assertTrue(os.path.exists(qr_path))
            
            self.module.stop_server()
    
    def test_transfer_history(self):
        """测试传输历史管理"""
        # 测试初始状态
        history = self.module.get_transfer_history()
        self.assertIsInstance(history, list)
        
        # 添加测试记录
        test_record = {
            "file_name": "test.txt",
            "file_size": 1024,
            "direction": "send",
            "status": "completed",
            "timestamp": "2024-01-01T12:00:00",
            "target": "*************"
        }
        
        self.module.transfer_history.append(test_record)
        
        # 测试保存和加载
        self.module._save_transfer_history()
        
        # 清空内存中的历史
        self.module.transfer_history.clear()
        
        # 重新加载
        self.module._load_transfer_history()
        
        # 验证数据是否正确加载
        loaded_history = self.module.get_transfer_history()
        self.assertEqual(len(loaded_history), 1)
        self.assertEqual(loaded_history[0]["file_name"], "test.txt")
    
    def test_file_send_preparation(self):
        """测试文件发送准备"""
        # 创建测试文件
        test_file = os.path.join(self.temp_dir, "test_send.txt")
        with open(test_file, 'w') as f:
            f.write("Test content for file transfer")
        
        # 测试发送文件（仅测试准备阶段）
        with patch.object(self.module, '_send_file_http') as mock_send:
            transfer_id = self.module.send_file(
                file_path=test_file,
                target_ip="*************",
                target_port=8001
            )
            
            self.assertIsNotNone(transfer_id)
            self.assertIn(transfer_id, self.module.active_transfers)
            
            transfer_info = self.module.get_transfer_status(transfer_id)
            self.assertEqual(transfer_info["file_name"], "test_send.txt")
            self.assertEqual(transfer_info["target_ip"], "*************")
            self.assertEqual(transfer_info["target_port"], 8001)
    
    def test_transfer_cancellation(self):
        """测试传输取消"""
        # 创建测试传输
        transfer_id = "test_transfer_123"
        self.module.active_transfers[transfer_id] = {
            "id": transfer_id,
            "status": "in_progress",
            "file_name": "test.txt"
        }
        
        # 取消传输
        self.module.cancel_transfer(transfer_id)
        
        # 验证传输已被移除
        self.assertNotIn(transfer_id, self.module.active_transfers)
    
    def test_message_handling(self):
        """测试消息处理"""
        # 测试传输请求消息
        request_data = {
            "file_name": "test.txt",
            "file_size": 1024,
            "sender_ip": "*************"
        }
        
        # 由于handle_transfer_request方法目前是空的，我们只测试调用不会出错
        try:
            self.module.handle_transfer_request(request_data)
        except Exception as e:
            self.fail(f"handle_transfer_request raised an exception: {e}")
    
    def test_config_management(self):
        """测试配置管理"""
        # 测试默认配置
        self.assertIn("default_port", self.module.config)
        self.assertIn("max_file_size", self.module.config)
        self.assertIn("enable_compression", self.module.config)
        
        # 测试配置更新
        original_port = self.module.config["default_port"]
        new_port = original_port + 1
        
        self.module.config["default_port"] = new_port
        self.assertEqual(self.module.config["default_port"], new_port)

class TestFileTransferWindow(unittest.TestCase):
    """文件传输窗口测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.module = FileTransferModule()
        self.module.comm_manager = Mock()
        self.module.message_bus = Mock()
        
        # 使用临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.module.shared_folder = self.temp_dir
        
        self.module.initialize()
        self.window = self.module.transfer_window
    
    def tearDown(self):
        """每个测试后的清理"""
        if self.module:
            self.module.cleanup()
        
        # 清理临时目录
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_window_initialization(self):
        """测试窗口初始化"""
        self.assertIsNotNone(self.window)
        self.assertIsNotNone(self.window.tab_widget)
        self.assertEqual(self.window.tab_widget.count(), 4)  # 4个选项卡
    
    def test_file_list_operations(self):
        """测试文件列表操作"""
        # 创建测试文件
        test_file = os.path.join(self.temp_dir, "test_file.txt")
        with open(test_file, 'w') as f:
            f.write("Test content")
        
        # 测试添加文件到列表
        initial_count = self.window.file_list.count()
        self.window.file_list.addItem(test_file)
        self.assertEqual(self.window.file_list.count(), initial_count + 1)
        
        # 测试清空文件列表
        self.window.clear_file_list()
        self.assertEqual(self.window.file_list.count(), 0)
    
    def test_server_controls(self):
        """测试服务器控制"""
        # 测试初始状态
        self.assertTrue(self.window.start_server_btn.isEnabled())
        self.assertFalse(self.window.stop_server_btn.isEnabled())
        
        # 模拟启动服务器
        with patch.object(self.module, 'start_server', return_value=True):
            with patch.object(self.module, 'get_local_ip', return_value='*************'):
                self.window.start_server()
                
                self.assertFalse(self.window.start_server_btn.isEnabled())
                self.assertTrue(self.window.stop_server_btn.isEnabled())
        
        # 模拟停止服务器
        self.window.stop_server()
        self.assertTrue(self.window.start_server_btn.isEnabled())
        self.assertFalse(self.window.stop_server_btn.isEnabled())
    
    def test_settings_save(self):
        """测试设置保存"""
        # 设置测试值
        self.window.port_edit.setText("9001")
        self.window.timeout_edit.setText("600")
        self.window.max_size_edit.setText("2048")
        self.window.compression_cb.setChecked(False)
        self.window.encryption_cb.setChecked(True)
        
        # 保存设置
        self.window.save_settings()
        
        # 验证设置已保存
        self.assertEqual(self.module.config["default_port"], 9001)
        self.assertEqual(self.module.config["transfer_timeout"], 600)
        self.assertEqual(self.module.config["max_file_size"], 2048 * 1024 * 1024)
        self.assertFalse(self.module.config["enable_compression"])
        self.assertTrue(self.module.config["enable_encryption"])
    
    def test_history_operations(self):
        """测试历史记录操作"""
        # 添加测试历史记录
        test_history = [
            {
                "file_name": "test1.txt",
                "file_size": 1024,
                "direction": "send",
                "status": "completed",
                "timestamp": "2024-01-01T12:00:00",
                "target": "*************"
            },
            {
                "file_name": "test2.txt",
                "file_size": 2048,
                "direction": "receive",
                "status": "completed",
                "timestamp": "2024-01-01T13:00:00",
                "target": "192.168.1.101"
            }
        ]
        
        self.module.transfer_history = test_history
        
        # 测试刷新历史
        self.window.refresh_history()
        self.assertEqual(self.window.history_table.rowCount(), 2)
        
        # 验证表格内容
        self.assertEqual(self.window.history_table.item(0, 0).text(), "test1.txt")
        self.assertEqual(self.window.history_table.item(1, 0).text(), "test2.txt")
    
    def test_send_validation(self):
        """测试发送验证"""
        # 测试没有文件时的验证
        with patch('PyQt5.QtWidgets.QMessageBox.warning') as mock_warning:
            self.window.start_send()
            mock_warning.assert_called_once()
        
        # 添加文件但没有目标IP
        self.window.file_list.addItem("test_file.txt")
        self.window.target_ip_edit.setText("")
        
        with patch('PyQt5.QtWidgets.QMessageBox.warning') as mock_warning:
            self.window.start_send()
            mock_warning.assert_called_once()
        
        # 设置无效端口
        self.window.target_ip_edit.setText("*************")
        self.window.target_port_edit.setText("invalid_port")
        
        with patch('PyQt5.QtWidgets.QMessageBox.warning') as mock_warning:
            self.window.start_send()
            mock_warning.assert_called_once()

if __name__ == '__main__':
    unittest.main()
