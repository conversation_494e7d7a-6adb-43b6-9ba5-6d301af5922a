"""
部署管理器测试
"""
import sys
import os
import unittest
import tempfile
import shutil
import json
import yaml
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from deployment_manager import (
    ConfigManager, UpdateManager, DeploymentManager
)

class TestConfigManager(unittest.TestCase):
    """测试配置管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager(self.temp_dir)
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_manager_initialization(self):
        """测试配置管理器初始化"""
        self.assertTrue(self.config_manager.config_dir.exists())
        self.assertTrue(self.config_manager.module_config_dir.exists())
        self.assertIsInstance(self.config_manager.default_app_config, dict)
        self.assertIsInstance(self.config_manager.default_user_config, dict)
    
    def test_load_app_config_default(self):
        """测试加载默认应用配置"""
        config = self.config_manager.load_app_config()
        
        # 验证配置结构
        self.assertIn('app', config)
        self.assertIn('system', config)
        self.assertIn('network', config)
        self.assertIn('ui', config)
        self.assertIn('modules', config)
        
        # 验证默认值
        self.assertEqual(config['app']['name'], '智慧课堂教学工具')
        self.assertEqual(config['network']['udp_port'], 8888)
        self.assertEqual(config['ui']['theme'], 'default')
    
    def test_save_and_load_app_config(self):
        """测试保存和加载应用配置"""
        # 修改配置
        config = self.config_manager.load_app_config()
        config['app']['version'] = '2.0.0'
        config['network']['udp_port'] = 9999
        
        # 保存配置
        self.config_manager.save_app_config(config)
        
        # 重新加载配置
        loaded_config = self.config_manager.load_app_config()
        
        # 验证修改已保存
        self.assertEqual(loaded_config['app']['version'], '2.0.0')
        self.assertEqual(loaded_config['network']['udp_port'], 9999)
    
    def test_load_user_config_default(self):
        """测试加载默认用户配置"""
        config = self.config_manager.load_user_config()
        
        # 验证配置结构
        self.assertIn('user', config)
        self.assertIn('recent', config)
        
        # 验证默认值
        self.assertEqual(config['user']['role'], 'teacher')
        self.assertIsInstance(config['recent']['courses'], list)
    
    def test_save_and_load_user_config(self):
        """测试保存和加载用户配置"""
        # 修改配置
        config = self.config_manager.load_user_config()
        config['user']['name'] = '测试教师'
        config['user']['preferences']['auto_login'] = True
        
        # 保存配置
        self.config_manager.save_user_config(config)
        
        # 重新加载配置
        loaded_config = self.config_manager.load_user_config()
        
        # 验证修改已保存
        self.assertEqual(loaded_config['user']['name'], '测试教师')
        self.assertTrue(loaded_config['user']['preferences']['auto_login'])
    
    def test_module_config_operations(self):
        """测试模块配置操作"""
        module_name = "test_module"
        module_config = {
            "enabled": True,
            "settings": {
                "timeout": 30,
                "retry_count": 3
            }
        }
        
        # 保存模块配置
        self.config_manager.save_module_config(module_name, module_config)
        
        # 加载模块配置
        loaded_config = self.config_manager.load_module_config(module_name)
        
        # 验证配置正确
        self.assertEqual(loaded_config['enabled'], True)
        self.assertEqual(loaded_config['settings']['timeout'], 30)
        self.assertEqual(loaded_config['settings']['retry_count'], 3)
    
    def test_config_merge(self):
        """测试配置合并"""
        default_config = {
            "section1": {
                "key1": "default1",
                "key2": "default2"
            },
            "section2": {
                "key3": "default3"
            }
        }
        
        user_config = {
            "section1": {
                "key1": "user1"  # 覆盖默认值
            },
            "section3": {  # 新增部分
                "key4": "user4"
            }
        }
        
        merged = self.config_manager._merge_config(default_config, user_config)
        
        # 验证合并结果
        self.assertEqual(merged['section1']['key1'], 'user1')  # 用户值覆盖默认值
        self.assertEqual(merged['section1']['key2'], 'default2')  # 保留默认值
        self.assertEqual(merged['section2']['key3'], 'default3')  # 保留默认部分
        self.assertEqual(merged['section3']['key4'], 'user4')  # 新增用户部分
    
    def test_reset_to_defaults(self):
        """测试重置为默认配置"""
        # 修改配置
        config = self.config_manager.load_app_config()
        config['app']['version'] = '2.0.0'
        self.config_manager.save_app_config(config)
        
        # 创建模块配置
        self.config_manager.save_module_config("test_module", {"test": True})
        
        # 重置配置
        self.config_manager.reset_to_defaults()
        
        # 验证配置已重置
        reset_config = self.config_manager.load_app_config()
        self.assertEqual(reset_config['app']['version'], '1.0.0')  # 恢复默认值
        
        # 验证模块配置已清理
        module_config = self.config_manager.load_module_config("test_module")
        self.assertEqual(module_config, {})
    
    def test_export_import_config(self):
        """测试配置导出和导入"""
        # 修改配置
        app_config = self.config_manager.load_app_config()
        app_config['app']['version'] = '2.0.0'
        self.config_manager.save_app_config(app_config)
        
        user_config = self.config_manager.load_user_config()
        user_config['user']['name'] = '测试用户'
        self.config_manager.save_user_config(user_config)
        
        self.config_manager.save_module_config("test_module", {"test": True})
        
        # 导出配置
        export_dir = tempfile.mkdtemp()
        try:
            result = self.config_manager.export_config(export_dir)
            self.assertTrue(result)
            
            # 验证导出文件存在
            self.assertTrue(Path(export_dir, "app_config.yaml").exists())
            self.assertTrue(Path(export_dir, "user_config.yaml").exists())
            self.assertTrue(Path(export_dir, "modules", "test_module.yaml").exists())
            
            # 重置配置
            self.config_manager.reset_to_defaults()
            
            # 导入配置
            result = self.config_manager.import_config(export_dir)
            self.assertTrue(result)
            
            # 验证配置已恢复
            imported_app_config = self.config_manager.load_app_config()
            self.assertEqual(imported_app_config['app']['version'], '2.0.0')
            
            imported_user_config = self.config_manager.load_user_config()
            self.assertEqual(imported_user_config['user']['name'], '测试用户')
            
            imported_module_config = self.config_manager.load_module_config("test_module")
            self.assertTrue(imported_module_config['test'])
            
        finally:
            shutil.rmtree(export_dir, ignore_errors=True)

class TestUpdateManager(unittest.TestCase):
    """测试更新管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager(self.temp_dir)
        self.update_manager = UpdateManager(self.config_manager)
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_update_manager_initialization(self):
        """测试更新管理器初始化"""
        self.assertIsNotNone(self.update_manager.config_manager)
        self.assertEqual(self.update_manager.current_version, "1.0.0")
        self.assertIsInstance(self.update_manager.update_server_url, str)
    
    @patch('requests.get')
    def test_check_for_updates_no_update(self, mock_get):
        """测试检查更新 - 无更新"""
        # Mock HTTP响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"has_update": False}
        mock_get.return_value = mock_response
        
        # 检查更新
        update_info = self.update_manager.check_for_updates()
        
        # 验证结果
        self.assertIsNone(update_info)
        mock_get.assert_called_once()
    
    @patch('requests.get')
    def test_check_for_updates_has_update(self, mock_get):
        """测试检查更新 - 有更新"""
        # Mock HTTP响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "has_update": True,
            "latest_version": "2.0.0",
            "download_url": "https://example.com/update.zip",
            "checksum": "abc123"
        }
        mock_get.return_value = mock_response
        
        # 检查更新
        update_info = self.update_manager.check_for_updates()
        
        # 验证结果
        self.assertIsNotNone(update_info)
        self.assertTrue(update_info['has_update'])
        self.assertEqual(update_info['latest_version'], '2.0.0')
    
    @patch('requests.get')
    def test_check_for_updates_http_error(self, mock_get):
        """测试检查更新 - HTTP错误"""
        # Mock HTTP错误响应
        mock_response = Mock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response
        
        # 检查更新
        update_info = self.update_manager.check_for_updates()
        
        # 验证结果
        self.assertIsNone(update_info)
    
    def test_should_check_update(self):
        """测试是否应该检查更新"""
        # 首次检查应该返回True
        should_check = self.update_manager._should_check_update()
        self.assertTrue(should_check)
        
        # 记录检查时间
        self.update_manager._record_update_check()
        
        # 立即再次检查应该返回False
        should_check = self.update_manager._should_check_update()
        self.assertFalse(should_check)
    
    def test_verify_file_integrity(self):
        """测试文件完整性验证"""
        # 创建测试文件
        test_file = Path(self.temp_dir) / "test.txt"
        test_content = b"Hello, World!"
        
        with open(test_file, 'wb') as f:
            f.write(test_content)
        
        # 计算正确的校验和
        import hashlib
        expected_checksum = hashlib.sha256(test_content).hexdigest()
        
        # 验证文件完整性
        result = self.update_manager._verify_file_integrity(test_file, expected_checksum)
        self.assertTrue(result)
        
        # 验证错误的校验和
        result = self.update_manager._verify_file_integrity(test_file, "wrong_checksum")
        self.assertFalse(result)
        
        # 验证空校验和（应该返回True）
        result = self.update_manager._verify_file_integrity(test_file, "")
        self.assertTrue(result)

class TestDeploymentManager(unittest.TestCase):
    """测试部署管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)
        
        # 创建测试文件结构
        self.create_test_files()
        
        self.deployment_manager = DeploymentManager()
    
    def tearDown(self):
        """清理测试环境"""
        os.chdir("/")  # 切换到根目录避免删除当前目录的问题
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_files(self):
        """创建测试文件"""
        # 创建主要文件
        main_files = [
            "main.py",
            "module_manager.py",
            "auth_manager.py",
            "communication_manager.py"
        ]
        
        for file_name in main_files:
            with open(file_name, 'w') as f:
                f.write(f"# {file_name}\nprint('Hello from {file_name}')")
        
        # 创建模块目录
        modules_dir = Path("modules")
        modules_dir.mkdir(exist_ok=True)
        
        with open(modules_dir / "test_module.py", 'w') as f:
            f.write("# Test module\nclass TestModule: pass")
    
    def test_deployment_manager_initialization(self):
        """测试部署管理器初始化"""
        self.assertIsNotNone(self.deployment_manager.config_manager)
        self.assertIsNotNone(self.deployment_manager.update_manager)
    
    def test_create_deployment_package(self):
        """测试创建部署包"""
        package_path = Path(self.temp_dir) / "deployment.zip"
        
        # 创建部署包
        result = self.deployment_manager.create_deployment_package(str(package_path))
        self.assertTrue(result)
        self.assertTrue(package_path.exists())
        
        # 验证包内容
        import zipfile
        with zipfile.ZipFile(package_path, 'r') as zipf:
            file_list = zipf.namelist()
            
            # 验证主要文件存在
            self.assertIn("main.py", file_list)
            self.assertIn("module_manager.py", file_list)
            self.assertIn("modules/test_module.py", file_list)
            self.assertIn("deployment_info.json", file_list)
    
    def test_install_from_package(self):
        """测试从包安装"""
        # 先创建部署包
        package_path = Path(self.temp_dir) / "deployment.zip"
        self.deployment_manager.create_deployment_package(str(package_path))
        
        # 创建安装目录
        install_dir = Path(self.temp_dir) / "install"
        
        # 从包安装
        result = self.deployment_manager.install_from_package(str(package_path), str(install_dir))
        self.assertTrue(result)
        
        # 验证安装结果
        self.assertTrue((install_dir / "main.py").exists())
        self.assertTrue((install_dir / "modules" / "test_module.py").exists())
        self.assertTrue((install_dir / "config").exists())
    
    def test_get_deployment_info(self):
        """测试获取部署信息"""
        info = self.deployment_manager.get_deployment_info()
        
        # 验证信息结构
        self.assertIn('app_info', info)
        self.assertIn('system_info', info)
        self.assertIn('config_status', info)
        
        # 验证系统信息
        self.assertIn('platform', info['system_info'])
        self.assertIn('python_version', info['system_info'])
        self.assertIn('install_path', info['system_info'])
        
        # 验证配置状态
        self.assertIn('app_config_exists', info['config_status'])
        self.assertIn('user_config_exists', info['config_status'])
        self.assertIn('module_configs', info['config_status'])

class TestDeploymentIntegration(unittest.TestCase):
    """部署集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.temp_dir)
        
        # 创建完整的测试环境
        self.create_complete_test_environment()
    
    def tearDown(self):
        """清理测试环境"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_complete_test_environment(self):
        """创建完整的测试环境"""
        # 创建所有必要的文件
        files_to_create = [
            "main.py",
            "module_manager.py",
            "auth_manager.py",
            "communication_manager.py",
            "performance_optimizer.py",
            "ui_optimizer.py",
            "deployment_manager.py"
        ]
        
        for file_name in files_to_create:
            with open(file_name, 'w') as f:
                f.write(f"# {file_name}\n")
        
        # 创建模块目录和文件
        modules_dir = Path("modules")
        modules_dir.mkdir(exist_ok=True)
        
        module_files = [
            "udp_discovery.py",
            "device_dashboard.py",
            "random_picker.py",
            "whiteboard_collaboration.py"
        ]
        
        for module_file in module_files:
            with open(modules_dir / module_file, 'w') as f:
                f.write(f"# {module_file}\n")
    
    def test_complete_deployment_workflow(self):
        """测试完整的部署工作流"""
        deployment_manager = DeploymentManager()
        
        # 1. 创建部署包
        package_path = "deployment_package.zip"
        result = deployment_manager.create_deployment_package(package_path)
        self.assertTrue(result)
        self.assertTrue(Path(package_path).exists())
        
        # 2. 安装到新目录
        install_dir = "installation"
        result = deployment_manager.install_from_package(package_path, install_dir)
        self.assertTrue(result)
        
        # 3. 验证安装
        install_path = Path(install_dir)
        self.assertTrue((install_path / "main.py").exists())
        self.assertTrue((install_path / "modules").exists())
        self.assertTrue((install_path / "config").exists())
        
        # 4. 获取部署信息
        info = deployment_manager.get_deployment_info()
        self.assertIsInstance(info, dict)
        self.assertIn('app_info', info)
    
    def test_config_management_workflow(self):
        """测试配置管理工作流"""
        config_manager = ConfigManager()
        
        # 1. 加载默认配置
        app_config = config_manager.load_app_config()
        user_config = config_manager.load_user_config()
        
        # 2. 修改配置
        app_config['app']['version'] = '2.0.0'
        user_config['user']['name'] = '测试用户'
        
        # 3. 保存配置
        config_manager.save_app_config(app_config)
        config_manager.save_user_config(user_config)
        
        # 4. 导出配置
        export_dir = "config_export"
        result = config_manager.export_config(export_dir)
        self.assertTrue(result)
        
        # 5. 重置配置
        config_manager.reset_to_defaults()
        
        # 6. 导入配置
        result = config_manager.import_config(export_dir)
        self.assertTrue(result)
        
        # 7. 验证配置恢复
        restored_app_config = config_manager.load_app_config()
        self.assertEqual(restored_app_config['app']['version'], '2.0.0')

if __name__ == '__main__':
    # 运行部署测试
    unittest.main(verbosity=2)
