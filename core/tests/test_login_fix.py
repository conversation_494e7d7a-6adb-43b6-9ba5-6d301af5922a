#!/usr/bin/env python3
"""
测试修复后的登录界面
"""
import sys
import warnings
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *

# 过滤PyQt5的弃用警告
warnings.filterwarnings("ignore", category=DeprecationWarning, module="PyQt5")

# 设置高DPI支持
QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

def test_main_app_login():
    """测试主应用程序的登录流程"""
    print("测试主应用程序登录流程...")
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setFont(QFont("Microsoft YaHei", 10))
        app.setStyle('Fusion')
        
        # 导入主应用程序类
        from main_app import CoreApplication
        
        # 创建主应用程序实例
        core_app = CoreApplication(sys.argv)
        
        # 显示登录对话框
        from login_dialog_fixed import LoginDialogFixed
        login_dialog = LoginDialogFixed()
        
        print("显示登录对话框...")
        result = login_dialog.exec_()
        
        if result == QDialog.Accepted:
            print("✓ 登录成功")
            if login_dialog.result_data:
                print(f"  用户: {login_dialog.result_data.get('username')}")
                print(f"  角色: {login_dialog.result_data.get('role')}")
            return True
        else:
            print("✗ 登录取消")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_font_rendering():
    """测试字体渲染"""
    print("\n测试字体渲染...")
    
    try:
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = QWidget()
        window.setWindowTitle("字体测试")
        window.setFixedSize(300, 200)
        
        layout = QVBoxLayout(window)
        
        # 测试各种中文文本
        test_texts = [
            "智慧课堂教学工具",
            "用户名：",
            "密码：",
            "登录",
            "取消",
            "以游客模式继续",
            "记住密码"
        ]
        
        for text in test_texts:
            label = QLabel(text)
            label.setFont(QFont("Microsoft YaHei", 12))
            layout.addWidget(label)
        
        window.show()
        
        # 显示2秒后关闭
        QTimer.singleShot(2000, window.close)
        
        print("✓ 字体渲染测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 字体测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("登录界面修复验证测试")
    print("=" * 50)
    
    # 测试字体渲染
    font_ok = test_font_rendering()
    
    # 测试登录流程
    login_ok = test_main_app_login()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"字体渲染: {'✓ 通过' if font_ok else '✗ 失败'}")
    print(f"登录流程: {'✓ 通过' if login_ok else '✗ 失败'}")
    
    if font_ok and login_ok:
        print("\n🎉 所有测试通过！登录界面已修复。")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
