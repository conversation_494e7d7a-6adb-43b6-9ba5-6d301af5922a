"""
认证系统单元测试
"""
import unittest
import os
import sys
import tempfile
import json
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from auth_manager import AuthManager
from module_manager import ModuleManager, ModuleInfo

class TestAuthManager(unittest.TestCase):
    """认证管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.auth_manager = AuthManager()
        # 使用临时文件避免影响实际配置
        self.temp_dir = tempfile.mkdtemp()
        self.auth_manager.token_file = os.path.join(self.temp_dir, "test_token.json")
    
    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_initial_state(self):
        """测试初始状态"""
        self.assertFalse(self.auth_manager.is_authenticated())
        self.assertEqual(self.auth_manager.get_user_role(), 'guest')
        self.assertIsNone(self.auth_manager.get_current_user())
    
    def test_token_creation_and_verification(self):
        """测试令牌创建和验证"""
        user_info = {
            'id': '123',
            'username': 'test_user',
            'name': '测试用户',
            'role': 'teacher'
        }
        
        # 创建令牌
        token = self.auth_manager.create_token(user_info)
        self.assertIsInstance(token, str)
        self.assertTrue(len(token) > 0)
        
        # 验证令牌
        self.assertTrue(self.auth_manager.verify_token(token))
    
    def test_token_save_and_load(self):
        """测试令牌保存和加载"""
        user_info = {
            'id': '456',
            'username': 'teacher2',
            'name': '教师2',
            'role': 'teacher'
        }
        
        token = self.auth_manager.create_token(user_info)
        
        # 保存令牌
        self.auth_manager.save_token(token, user_info)
        self.assertTrue(os.path.exists(self.auth_manager.token_file))
        
        # 创建新实例并加载令牌
        new_auth_manager = AuthManager()
        new_auth_manager.token_file = self.auth_manager.token_file
        
        self.assertTrue(new_auth_manager.load_cached_token())
        self.assertEqual(new_auth_manager.get_current_user()['username'], 'teacher2')
    
    def test_expired_token(self):
        """测试过期令牌"""
        # 创建一个已过期的令牌
        import jwt
        now = datetime.now()
        payload = {
            'user_id': '789',
            'username': 'expired_user',
            'role': 'teacher',
            'exp': int((now - timedelta(hours=1)).timestamp()),  # 1小时前过期
            'iat': int((now - timedelta(hours=2)).timestamp())   # 2小时前创建
        }
        expired_token = jwt.encode(payload, self.auth_manager.jwt_secret, algorithm='HS256')
        
        # 验证过期令牌应该失败
        self.assertFalse(self.auth_manager.verify_token(expired_token))

class TestModuleManager(unittest.TestCase):
    """模块管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.module_manager = ModuleManager()
    
    def test_initial_state(self):
        """测试初始状态"""
        self.assertFalse(self.module_manager.is_authenticated)
        self.assertEqual(self.module_manager.user_role, 'guest')
    
    def test_guest_mode_modules(self):
        """测试游客模式可用模块"""
        # 游客模式下只能访问通用功能
        available_modules = self.module_manager.get_available_modules()
        
        # 检查通用功能模块存在
        common_module_names = [m.name for m in available_modules if m.is_common]
        self.assertIn('whiteboard', common_module_names)
        self.assertIn('file_sharing', common_module_names)
        
        # 检查教师专用功能不存在
        teacher_module_names = [m.name for m in available_modules if m.requires_auth]
        self.assertEqual(len(teacher_module_names), 0)
    
    def test_teacher_mode_modules(self):
        """测试教师模式可用模块"""
        # 设置为教师认证状态
        self.module_manager.set_auth_status(True, 'teacher')
        
        available_modules = self.module_manager.get_available_modules()
        module_names = [m.name for m in available_modules]
        
        # 检查通用功能模块存在
        self.assertIn('whiteboard', module_names)
        self.assertIn('file_sharing', module_names)
        
        # 检查教师专用功能存在
        self.assertIn('danmaku', module_names)
        self.assertIn('control_pc', module_names)
    
    def test_module_access_permission(self):
        """测试模块访问权限"""
        # 游客模式
        self.assertTrue(self.module_manager.can_access_module('whiteboard'))
        self.assertFalse(self.module_manager.can_access_module('danmaku'))
        
        # 教师模式
        self.module_manager.set_auth_status(True, 'teacher')
        self.assertTrue(self.module_manager.can_access_module('whiteboard'))
        self.assertTrue(self.module_manager.can_access_module('danmaku'))
    
    def test_custom_module_addition(self):
        """测试自定义模块添加"""
        custom_module = ModuleInfo(
            name="test_module",
            display_name="测试模块",
            module_path="test.module",
            class_name="TestModule",
            is_common=False,
            requires_auth=True,
            tooltip="这是一个测试模块"
        )
        
        self.module_manager.add_custom_module(custom_module)
        
        # 检查模块是否添加成功
        module_info = self.module_manager.get_module_info("test_module")
        self.assertIsNotNone(module_info)
        self.assertEqual(module_info.display_name, "测试模块")

if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)