"""
设备控制模块测试
"""
import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.device_control import (
    DeviceControlModule, DeviceControlRecord, DeviceControlWorker
)
from modules.udp_discovery import DeviceInfo

class TestDeviceControlRecord(unittest.TestCase):
    """测试设备控制记录类"""
    
    def test_record_creation(self):
        """测试记录创建"""
        record = DeviceControlRecord("device1", "小组设备1", "wake", "success")
        
        self.assertEqual(record.device_id, "device1")
        self.assertEqual(record.device_name, "小组设备1")
        self.assertEqual(record.action, "wake")
        self.assertEqual(record.result, "success")
        self.assertIsInstance(record.timestamp, datetime)
    
    def test_record_to_dict(self):
        """测试记录转换为字典"""
        record = DeviceControlRecord("device1", "小组设备1", "wake", "success")
        record_dict = record.to_dict()
        
        self.assertIn("device_id", record_dict)
        self.assertIn("device_name", record_dict)
        self.assertIn("action", record_dict)
        self.assertIn("result", record_dict)
        self.assertIn("timestamp", record_dict)

class TestDeviceControlWorker(unittest.TestCase):
    """测试设备控制工作线程"""
    
    def setUp(self):
        """设置测试环境"""
        self.worker = DeviceControlWorker()
    
    def test_worker_creation(self):
        """测试工作线程创建"""
        self.assertFalse(self.worker.is_running)
    
    @patch('subprocess.run')
    def test_ping_device(self, mock_run):
        """测试设备ping检查"""
        # 测试ping成功
        mock_run.return_value = Mock(returncode=0)
        result = self.worker._ping_device("*************")
        self.assertTrue(result)
        
        # 测试ping失败
        mock_run.return_value = Mock(returncode=1)
        result = self.worker._ping_device("*************")
        self.assertFalse(result)
    
    @patch('modules.device_control.send_magic_packet')
    @patch.object(DeviceControlWorker, '_ping_device')
    def test_wake_device(self, mock_ping, mock_magic_packet):
        """测试唤醒设备"""
        # Mock ping返回True（设备已唤醒）
        mock_ping.return_value = True
        
        # 创建信号接收器
        operation_completed = Mock()
        self.worker.operation_completed.connect(operation_completed)
        
        # 执行唤醒操作
        self.worker.wake_device("device1", "00:11:22:33:44:55", "*************")
        
        # 验证魔术包已发送
        mock_magic_packet.assert_called_with("00:11:22:33:44:55")
    
    @patch('subprocess.run')
    def test_shutdown_device_windows(self, mock_run):
        """测试Windows系统关机"""
        mock_run.return_value = Mock(returncode=0)
        
        # 创建信号接收器
        operation_completed = Mock()
        self.worker.operation_completed.connect(operation_completed)
        
        # 执行关机操作
        with patch('sys.platform', 'win32'):
            self.worker.shutdown_device("device1", "*************")
        
        # 验证shutdown命令已调用
        mock_run.assert_called()
        args = mock_run.call_args[0][0]
        self.assertIn('shutdown', args)
        self.assertIn('/s', args)
    
    @patch('subprocess.run')
    def test_restart_device_linux(self, mock_run):
        """测试Linux系统重启"""
        mock_run.return_value = Mock(returncode=0)
        
        # 创建信号接收器
        operation_completed = Mock()
        self.worker.operation_completed.connect(operation_completed)
        
        # 执行重启操作
        with patch('sys.platform', 'linux'):
            self.worker.restart_device("device1", "*************", "admin")
        
        # 验证ssh命令已调用
        mock_run.assert_called()
        args = mock_run.call_args[0][0]
        self.assertIn('ssh', args)
        self.assertIn('reboot', args)

class TestDeviceControlModule(unittest.TestCase):
    """测试设备控制模块"""
    
    def setUp(self):
        """设置测试环境"""
        # Mock认证管理器
        self.mock_auth_manager = Mock()
        self.mock_auth_manager.is_authenticated.return_value = True
        self.mock_auth_manager.get_current_user.return_value = {
            "id": "teacher1",
            "name": "测试教师",
            "role": "teacher"
        }
        
        # 创建设备控制模块
        self.control = DeviceControlModule()
        self.control.auth_manager = self.mock_auth_manager
    
    @patch('modules.device_control.DeviceControlWindow')
    def test_module_initialization(self, mock_window):
        """测试模块初始化"""
        # 初始化模块
        result = self.control._initialize_module()
        
        # 验证初始化成功
        self.assertTrue(result)
        self.assertIsNotNone(self.control.control_window)
        self.assertIsNotNone(self.control.control_worker)
    
    def test_module_initialization_without_auth(self):
        """测试未认证时的模块初始化"""
        # 设置未认证状态
        self.mock_auth_manager.is_authenticated.return_value = False
        
        # 初始化模块
        result = self.control._initialize_module()
        
        # 验证初始化失败
        self.assertFalse(result)
    
    def test_security_check(self):
        """测试安全检查"""
        # 添加测试设备
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["control"])
        self.control.available_devices["group1"] = device
        
        # 测试正常情况
        result = self.control._security_check("wake", "group1")
        self.assertTrue(result)
        
        # 测试非教师用户
        self.mock_auth_manager.get_current_user.return_value = {"role": "student"}
        result = self.control._security_check("wake", "group1")
        self.assertFalse(result)
        
        # 恢复教师角色
        self.mock_auth_manager.get_current_user.return_value = {"role": "teacher"}
        
        # 测试非小组设备
        teacher_device = DeviceInfo("teacher1", "teacher", "教师设备", "192.168.1.101", 8888, ["control"])
        self.control.available_devices["teacher1"] = teacher_device
        result = self.control._security_check("wake", "teacher1")
        self.assertFalse(result)
    
    def test_security_check_frequency_limit(self):
        """测试操作频率限制"""
        # 添加测试设备
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["control"])
        self.control.available_devices["group1"] = device
        
        # 添加多个最近的操作记录
        for i in range(3):
            record = DeviceControlRecord("group1", "小组设备1", "wake", "success")
            record.timestamp = datetime.now() - timedelta(seconds=30)
            self.control.control_records.append(record)
        
        # 测试频率限制
        result = self.control._security_check("wake", "group1")
        self.assertFalse(result)
    
    @patch('modules.device_control.QMessageBox')
    def test_confirm_operation(self, mock_msgbox):
        """测试操作确认"""
        # 测试用户确认
        mock_msgbox.question.return_value = mock_msgbox.Yes
        result = self.control._confirm_operation("唤醒", "小组设备1")
        self.assertTrue(result)
        
        # 测试用户拒绝
        mock_msgbox.question.return_value = mock_msgbox.No
        result = self.control._confirm_operation("唤醒", "小组设备1")
        self.assertFalse(result)
    
    def test_get_device_mac(self):
        """测试获取设备MAC地址"""
        # 从设备认证信息获取
        self.control.device_credentials["group1"] = {
            "mac_address": "00:11:22:33:44:55",
            "username": "admin",
            "password": "password"
        }
        
        mac = self.control._get_device_mac("group1")
        self.assertEqual(mac, "00:11:22:33:44:55")
        
        # 设备不存在
        mac = self.control._get_device_mac("nonexistent")
        self.assertIsNone(mac)
    
    @patch.object(DeviceControlModule, '_security_check')
    @patch.object(DeviceControlModule, '_confirm_operation')
    @patch.object(DeviceControlModule, '_get_device_mac')
    def test_wake_device(self, mock_get_mac, mock_confirm, mock_security):
        """测试唤醒设备"""
        # 设置Mock返回值
        mock_security.return_value = True
        mock_confirm.return_value = True
        mock_get_mac.return_value = "00:11:22:33:44:55"
        
        # 添加测试设备
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["control"])
        self.control.available_devices["group1"] = device
        
        # Mock工作线程
        self.control.control_worker = Mock()
        
        # 执行唤醒操作
        result = self.control.wake_device("group1")
        
        # 验证操作成功
        self.assertTrue(result)
        self.control.control_worker.wake_device.assert_called_with(
            "group1", "00:11:22:33:44:55", "*************"
        )
    
    def test_wake_device_no_mac(self):
        """测试没有MAC地址时的唤醒操作"""
        # 添加测试设备
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["control"])
        self.control.available_devices["group1"] = device
        
        # 禁用安全检查和确认
        self.control.config["enable_security_check"] = False
        self.control.config["require_confirmation"] = False
        
        # 执行唤醒操作（没有MAC地址）
        result = self.control.wake_device("group1")
        
        # 验证操作失败
        self.assertFalse(result)
    
    def test_batch_operation(self):
        """测试批量操作"""
        # 添加测试设备
        device1 = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["control"])
        device2 = DeviceInfo("group2", "group", "小组设备2", "192.168.1.101", 8888, ["control"])
        self.control.available_devices["group1"] = device1
        self.control.available_devices["group2"] = device2
        
        # 禁用确认对话框
        self.control.config["require_confirmation"] = False
        
        # Mock单个操作方法
        self.control.wake_device = Mock(return_value=True)
        
        # 执行批量唤醒
        result = self.control.batch_operation(["group1", "group2"], "wake")
        
        # 验证批量操作成功
        self.assertTrue(result)
        self.assertEqual(self.control.wake_device.call_count, 2)
    
    def test_on_operation_completed(self):
        """测试操作完成事件处理"""
        # 添加测试设备
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["control"])
        self.control.available_devices["group1"] = device
        
        # 触发操作完成事件
        self.control.on_operation_completed("group1", "wake", "success")
        
        # 验证记录已添加
        self.assertEqual(len(self.control.control_records), 1)
        record = self.control.control_records[0]
        self.assertEqual(record.device_id, "group1")
        self.assertEqual(record.action, "wake")
        self.assertEqual(record.result, "success")
    
    def test_get_available_groups(self):
        """测试获取可用小组设备"""
        # 添加测试设备
        device1 = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["control"])
        device2 = DeviceInfo("teacher1", "teacher", "教师设备", "192.168.1.101", 8888, ["control"])
        device3 = DeviceInfo("group2", "group", "小组设备2", "192.168.1.102", 8888, ["control"])
        
        self.control.available_devices = {
            "group1": device1,
            "teacher1": device2,
            "group2": device3
        }
        
        # 获取可用小组设备
        groups = self.control.get_available_groups()
        
        # 验证结果（只有小组设备）
        self.assertEqual(len(groups), 2)
        group_ids = [g.device_id for g in groups]
        self.assertIn("group1", group_ids)
        self.assertIn("group2", group_ids)
        self.assertNotIn("teacher1", group_ids)
    
    def test_get_control_status(self):
        """测试获取控制状态"""
        # 添加测试数据
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["control"])
        self.control.available_devices["group1"] = device
        
        record = DeviceControlRecord("group1", "小组设备1", "wake", "success")
        self.control.control_records.append(record)
        
        # 获取控制状态
        status = self.control.get_control_status()
        
        # 验证状态信息
        self.assertEqual(status["available_devices"], 1)
        self.assertEqual(status["group_devices"], 1)
        self.assertEqual(status["total_records"], 1)
        self.assertEqual(status["recent_operations"], 1)
    
    def test_message_handling(self):
        """测试消息处理"""
        # 添加测试设备
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["control"])
        self.control.available_devices["group1"] = device
        
        # Mock操作方法
        self.control.wake_device = Mock(return_value=True)
        self.control.shutdown_device = Mock(return_value=True)
        self.control.restart_device = Mock(return_value=True)
        
        # 测试唤醒设备消息
        self.control._handle_message("test_sender", "wake_device", {"device_id": "group1"})
        self.control.wake_device.assert_called_with("group1")
        
        # 测试关闭设备消息
        self.control._handle_message("test_sender", "shutdown_device", {"device_id": "group1"})
        self.control.shutdown_device.assert_called_with("group1")
        
        # 测试重启设备消息
        self.control._handle_message("test_sender", "restart_device", {"device_id": "group1"})
        self.control.restart_device.assert_called_with("group1")
        
        # 测试获取状态消息
        self.control._handle_message("test_sender", "get_control_status", {})
        
        # 测试显示界面消息
        self.control._handle_message("test_sender", "show_control", {})

if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
