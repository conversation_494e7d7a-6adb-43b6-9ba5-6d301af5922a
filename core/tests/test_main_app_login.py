#!/usr/bin/env python3
"""
测试主应用程序登录流程
"""
import sys
import os
import warnings
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *

# 过滤PyQt5的弃用警告
warnings.filterwarnings("ignore", category=DeprecationWarning, module="PyQt5")

# 设置高DPI支持
QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

# 添加当前目录到Python路径（模拟主应用程序）
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_main_app_login_flow():
    """测试主应用程序登录流程"""
    print("测试主应用程序登录流程...")
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setFont(QFont("Microsoft YaHei UI", 10))
        app.setStyle('Fusion')
        
        # 模拟主应用程序的导入方式
        from login_dialog_fixed import LoginDialogFixed as LoginDialog
        
        print("✓ 登录对话框导入成功")
        
        # 创建登录对话框
        dialog = LoginDialog()
        print("✓ 登录对话框创建成功")
        
        # 检查 get_result 方法
        if hasattr(dialog, 'get_result'):
            print("✓ get_result 方法存在")
            
            # 测试方法调用
            result = dialog.get_result()
            print(f"✓ get_result() 返回: {result}")
            
            # 模拟登录成功的情况
            dialog.result_data = {
                "mode": "login",
                "username": "admin",
                "password": "admin",
                "role": "teacher",
                "remember": False
            }
            
            result = dialog.get_result()
            print(f"✓ 登录数据设置后 get_result() 返回: {result}")
            
            # 检查必要的键
            required_keys = ['mode', 'username', 'role']
            for key in required_keys:
                if key in result:
                    print(f"✓ 结果包含 {key}: {result[key]}")
                else:
                    print(f"✗ 结果缺少 {key}")
                    return False
            
            return True
        else:
            print("✗ get_result 方法不存在")
            # 列出所有可用的方法
            methods = [method for method in dir(dialog) if not method.startswith('_')]
            print(f"可用方法: {methods}")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_show_login_dialog():
    """测试显示登录对话框的函数"""
    print("\n测试显示登录对话框...")
    
    try:
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        # 模拟主应用程序的 show_login_dialog 方法
        from login_dialog_fixed import LoginDialogFixed as LoginDialog
        
        def show_login_dialog():
            """显示登录对话框"""
            dialog = LoginDialog()
            
            # 设置一些测试数据（模拟用户输入）
            dialog.result_data = {
                "mode": "login",
                "username": "admin",
                "password": "admin",
                "role": "teacher",
                "remember": False
            }
            
            # 模拟对话框被接受
            if True:  # 模拟 dialog.exec_() == QDialog.Accepted
                result = dialog.get_result()
                print(f"✓ 获取到登录结果: {result}")
                
                if result['mode'] == 'login':
                    print(f"✓ 教师登录模式: {result['username']}")
                    return True
                elif result['mode'] == 'guest':
                    print("✓ 游客模式")
                    return True
            else:
                print("✗ 用户取消登录")
                return False
        
        return show_login_dialog()
        
    except Exception as e:
        print(f"✗ 显示登录对话框测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("主应用程序登录流程测试")
    print("=" * 60)
    
    tests = [
        ("登录流程测试", test_main_app_login_flow),
        ("显示登录对话框测试", test_show_login_dialog)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！主应用程序登录流程正常。")
        print("\n现在可以尝试启动主应用程序:")
        print("  python main_app.py")
        return 0
    else:
        print("❌ 部分测试失败，需要进一步调试。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
