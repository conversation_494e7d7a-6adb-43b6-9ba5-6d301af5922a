"""
模块化架构单元测试
"""
import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入要测试的模块
from modules.base_module import BaseModule, CommonModule, TeacherModule
from modules.communication_manager import CommunicationManager, MessageBus
from module_manager import ModuleManager, ModuleInfo

class TestBaseModule(unittest.TestCase):
    """基础模块测试"""
    
    def setUp(self):
        """测试前准备"""
        if not QApplication.instance():
            self.app = QApplication([])
        else:
            self.app = QApplication.instance()
    
    def test_common_module_creation(self):
        """测试通用模块创建"""
        class TestCommonModule(CommonModule):
            def _initialize_module(self):
                return True
        
        module = TestCommonModule("test_common", "测试通用模块")
        
        # 检查基本属性
        self.assertEqual(module.module_name, "test_common")
        self.assertEqual(module.display_name, "测试通用模块")
        self.assertEqual(module.module_type, "common")
        self.assertFalse(module.is_initialized)
        self.assertEqual(module.status, "not_initialized")
        
        # 检查模块信息
        info = module.get_module_info()
        self.assertEqual(info["name"], "test_common")
        self.assertEqual(info["type"], "common")
        self.assertFalse(info["requires_auth"])
    
    def test_teacher_module_creation(self):
        """测试教师模块创建"""
        class TestTeacherModule(TeacherModule):
            def _initialize_module(self):
                return True
        
        module = TestTeacherModule("test_teacher", "测试教师模块")
        
        # 检查基本属性
        self.assertEqual(module.module_name, "test_teacher")
        self.assertEqual(module.display_name, "测试教师模块")
        self.assertEqual(module.module_type, "teacher")
        self.assertFalse(module.is_initialized)
        
        # 检查模块信息
        info = module.get_module_info()
        self.assertEqual(info["name"], "test_teacher")
        self.assertEqual(info["type"], "teacher")
        self.assertTrue(info["requires_auth"])
    
    def test_module_initialization(self):
        """测试模块初始化"""
        class TestModule(CommonModule):
            def __init__(self):
                super().__init__("test", "测试模块")
                self.init_called = False
            
            def _initialize_module(self):
                self.init_called = True
                return True
        
        module = TestModule()
        
        # 初始化前状态
        self.assertFalse(module.is_initialized)
        self.assertEqual(module.status, "not_initialized")
        self.assertFalse(module.init_called)
        
        # 执行初始化
        result = module.initialize()
        
        # 初始化后状态
        self.assertTrue(result)
        self.assertTrue(module.is_initialized)
        self.assertEqual(module.status, "ready")
        self.assertTrue(module.init_called)
    
    def test_module_initialization_failure(self):
        """测试模块初始化失败"""
        class FailingModule(CommonModule):
            def _initialize_module(self):
                return False
        
        module = FailingModule("failing", "失败模块")
        
        # 执行初始化
        result = module.initialize()
        
        # 检查失败状态
        self.assertFalse(result)
        self.assertFalse(module.is_initialized)
        self.assertEqual(module.status, "error")
    
    def test_module_cleanup(self):
        """测试模块清理"""
        class TestModule(CommonModule):
            def __init__(self):
                super().__init__("test", "测试模块")
                self.cleanup_called = False
            
            def _initialize_module(self):
                return True
            
            def _cleanup_module(self):
                self.cleanup_called = True
        
        module = TestModule()
        module.initialize()
        
        # 清理前状态
        self.assertTrue(module.is_initialized)
        self.assertFalse(module.cleanup_called)
        
        # 执行清理
        module.cleanup()
        
        # 清理后状态
        self.assertFalse(module.is_initialized)
        self.assertEqual(module.status, "not_initialized")
        self.assertTrue(module.cleanup_called)

class TestCommunicationManager(unittest.TestCase):
    """通信管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        if not QApplication.instance():
            self.app = QApplication([])
        else:
            self.app = QApplication.instance()
        
        self.comm_manager = CommunicationManager()
    
    def test_module_registration(self):
        """测试模块注册"""
        # 创建模拟模块
        mock_module = Mock()
        mock_module.module_name = "test_module"
        mock_module.module_message = Mock()
        
        # 注册模块
        self.comm_manager.register_module(mock_module)
        
        # 检查注册结果
        self.assertIn("test_module", self.comm_manager.modules)
        self.assertEqual(self.comm_manager.modules["test_module"], mock_module)
        
        # 检查已注册模块列表
        registered = self.comm_manager.get_registered_modules()
        self.assertIn("test_module", registered)
    
    def test_module_unregistration(self):
        """测试模块注销"""
        # 创建并注册模块
        mock_module = Mock()
        mock_module.module_name = "test_module"
        mock_module.module_message = Mock()
        
        self.comm_manager.register_module(mock_module)
        self.assertIn("test_module", self.comm_manager.modules)
        
        # 注销模块
        self.comm_manager.unregister_module("test_module")
        
        # 检查注销结果
        self.assertNotIn("test_module", self.comm_manager.modules)
    
    def test_send_message(self):
        """测试发送消息"""
        # 创建目标模块
        target_module = Mock()
        target_module.module_name = "target"
        target_module.handle_message = Mock()
        
        self.comm_manager.register_module(target_module)
        
        # 发送消息
        result = self.comm_manager.send_message(
            "sender", "target", "test_message", {"data": "test"}
        )
        
        # 检查发送结果
        self.assertTrue(result)
        target_module.handle_message.assert_called_once_with(
            "sender", "test_message", {"data": "test"}
        )
        
        # 检查统计信息
        stats = self.comm_manager.get_message_stats()
        self.assertEqual(stats["sent"], 1)
    
    def test_broadcast_message(self):
        """测试广播消息"""
        # 创建多个模块
        modules = []
        for i in range(3):
            module = Mock()
            module.module_name = f"module_{i}"
            module.handle_message = Mock()
            modules.append(module)
            self.comm_manager.register_module(module)
        
        # 广播消息
        result = self.comm_manager.broadcast_message(
            "sender", "broadcast_message", {"data": "broadcast"}
        )
        
        # 检查广播结果
        self.assertTrue(result)
        
        # 检查所有模块都收到消息
        for module in modules:
            module.handle_message.assert_called_once_with(
                "sender", "broadcast_message", {"data": "broadcast"}
            )
        
        # 检查统计信息
        stats = self.comm_manager.get_message_stats()
        self.assertEqual(stats["broadcast"], 1)
    
    def test_message_history(self):
        """测试消息历史"""
        # 创建目标模块
        target_module = Mock()
        target_module.module_name = "target"
        target_module.handle_message = Mock()
        
        self.comm_manager.register_module(target_module)
        
        # 发送消息
        self.comm_manager.send_message(
            "sender", "target", "test_message", {"data": "test"}
        )
        
        # 检查消息历史
        history = self.comm_manager.get_message_history()
        self.assertEqual(len(history), 1)
        
        message = history[0]
        self.assertEqual(message["sender"], "sender")
        self.assertEqual(message["target"], "target")
        self.assertEqual(message["type"], "test_message")
        self.assertEqual(message["data"], {"data": "test"})
        self.assertEqual(message["delivery_type"], "direct")

class TestMessageBus(unittest.TestCase):
    """消息总线测试"""
    
    def setUp(self):
        """测试前准备"""
        if not QApplication.instance():
            self.app = QApplication([])
        else:
            self.app = QApplication.instance()
        
        self.comm_manager = CommunicationManager()
        self.message_bus = MessageBus(self.comm_manager)
    
    def test_topic_subscription(self):
        """测试主题订阅"""
        # 订阅主题
        self.message_bus.subscribe("module1", "test_topic")
        self.message_bus.subscribe("module2", "test_topic")
        
        # 检查订阅者
        subscribers = self.message_bus.get_topic_subscribers("test_topic")
        self.assertIn("module1", subscribers)
        self.assertIn("module2", subscribers)
        
        # 检查主题列表
        topics = self.message_bus.get_all_topics()
        self.assertIn("test_topic", topics)
    
    def test_topic_unsubscription(self):
        """测试取消订阅"""
        # 订阅主题
        self.message_bus.subscribe("module1", "test_topic")
        self.message_bus.subscribe("module2", "test_topic")
        
        # 取消订阅
        self.message_bus.unsubscribe("module1", "test_topic")
        
        # 检查订阅者
        subscribers = self.message_bus.get_topic_subscribers("test_topic")
        self.assertNotIn("module1", subscribers)
        self.assertIn("module2", subscribers)
    
    def test_topic_publish(self):
        """测试主题发布"""
        # 创建订阅者模块
        subscriber1 = Mock()
        subscriber1.module_name = "subscriber1"
        subscriber1.handle_message = Mock()
        
        subscriber2 = Mock()
        subscriber2.module_name = "subscriber2"
        subscriber2.handle_message = Mock()
        
        self.comm_manager.register_module(subscriber1)
        self.comm_manager.register_module(subscriber2)
        
        # 订阅主题
        self.message_bus.subscribe("subscriber1", "test_topic")
        self.message_bus.subscribe("subscriber2", "test_topic")
        
        # 发布消息
        delivered = self.message_bus.publish(
            "publisher", "test_topic", {"message": "hello"}
        )
        
        # 检查发布结果
        self.assertEqual(delivered, 2)
        
        # 检查订阅者收到消息
        subscriber1.handle_message.assert_called_once()
        subscriber2.handle_message.assert_called_once()

class TestModuleManager(unittest.TestCase):
    """模块管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        if not QApplication.instance():
            self.app = QApplication([])
        else:
            self.app = QApplication.instance()
        
        self.module_manager = ModuleManager()
    
    def test_module_manager_initialization(self):
        """测试模块管理器初始化"""
        # 检查基本属性
        self.assertIsNotNone(self.module_manager.modules)
        self.assertIsNotNone(self.module_manager.active_modules)
        self.assertIsNotNone(self.module_manager.comm_manager)
        self.assertIsNotNone(self.module_manager.message_bus)
        
        # 检查默认状态
        self.assertFalse(self.module_manager.is_authenticated)
        self.assertEqual(self.module_manager.user_role, 'guest')
    
    def test_auth_status_setting(self):
        """测试认证状态设置"""
        # 设置认证状态
        self.module_manager.set_auth_status(True, 'teacher')
        
        # 检查状态
        self.assertTrue(self.module_manager.is_authenticated)
        self.assertEqual(self.module_manager.user_role, 'teacher')
    
    def test_available_modules_guest(self):
        """测试游客模式可用模块"""
        # 游客模式
        self.module_manager.set_auth_status(False, 'guest')
        
        # 获取可用模块
        available = self.module_manager.get_available_modules()
        
        # 检查只有通用模块可用
        for module in available:
            self.assertTrue(module.is_common)
    
    def test_available_modules_teacher(self):
        """测试教师模式可用模块"""
        # 教师模式
        self.module_manager.set_auth_status(True, 'teacher')
        
        # 获取可用模块
        available = self.module_manager.get_available_modules()
        
        # 检查包含教师专用模块
        has_teacher_module = any(module.requires_auth for module in available)
        self.assertTrue(has_teacher_module)
    
    def test_module_access_permission(self):
        """测试模块访问权限"""
        # 游客模式
        self.module_manager.set_auth_status(False, 'guest')
        
        # 检查通用模块可访问
        self.assertTrue(self.module_manager.can_access_module("whiteboard"))
        
        # 检查教师模块不可访问
        self.assertFalse(self.module_manager.can_access_module("danmaku"))
        
        # 教师模式
        self.module_manager.set_auth_status(True, 'teacher')
        
        # 检查教师模块可访问
        self.assertTrue(self.module_manager.can_access_module("danmaku"))
    
    @patch('importlib.import_module')
    def test_module_loading_success(self, mock_import):
        """测试模块加载成功"""
        # 创建模拟模块类
        mock_module_class = Mock()
        mock_instance = Mock(spec=BaseModule)
        mock_instance.module_name = "test_module"
        mock_instance.initialize.return_value = True
        mock_instance.module_ready = Mock()
        mock_instance.module_error = Mock()
        mock_module_class.return_value = mock_instance
        
        # 设置导入模拟
        mock_module = Mock()
        mock_module.TestModule = mock_module_class
        mock_import.return_value = mock_module
        
        # 添加测试模块配置
        test_module_info = ModuleInfo(
            name="test_module",
            display_name="测试模块",
            module_path="test.module",
            class_name="TestModule",
            is_common=True
        )
        self.module_manager.modules["test_module"] = test_module_info
        
        # 加载模块
        result = self.module_manager.load_module("test_module")
        
        # 检查加载结果
        self.assertIsNotNone(result)
        self.assertEqual(result, mock_instance)
        self.assertIn("test_module", self.module_manager.active_modules)
        
        # 检查模块初始化被调用
        mock_instance.initialize.assert_called_once()
    
    def test_module_loading_failure(self):
        """测试模块加载失败"""
        # 尝试加载不存在的模块
        result = self.module_manager.load_module("nonexistent_module")
        
        # 检查加载失败
        self.assertIsNone(result)
    
    def test_module_unloading(self):
        """测试模块卸载"""
        # 创建模拟模块
        mock_instance = Mock(spec=BaseModule)
        mock_instance.cleanup = Mock()
        
        # 添加到活跃模块
        test_module_info = ModuleInfo(
            name="test_module",
            display_name="测试模块",
            module_path="test.module",
            class_name="TestModule",
            is_common=True
        )
        test_module_info.instance = mock_instance
        test_module_info.is_loaded = True
        
        self.module_manager.modules["test_module"] = test_module_info
        self.module_manager.active_modules["test_module"] = mock_instance
        
        # 卸载模块
        self.module_manager.unload_module("test_module")
        
        # 检查卸载结果
        mock_instance.cleanup.assert_called_once()
        self.assertNotIn("test_module", self.module_manager.active_modules)
        self.assertIsNone(test_module_info.instance)
        self.assertFalse(test_module_info.is_loaded)

def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_classes = [
        TestBaseModule,
        TestCommunicationManager,
        TestMessageBus,
        TestModuleManager
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)