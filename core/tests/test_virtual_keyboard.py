"""
虚拟键盘模块测试
"""
import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.virtual_keyboard import (
    VirtualKeyboardModule, KeyboardSession, KeyboardMonitor
)
from modules.udp_discovery import DeviceInfo

class TestKeyboardSession(unittest.TestCase):
    """测试键盘会话类"""
    
    def test_session_creation(self):
        """测试会话创建"""
        session = KeyboardSession("session1", "teacher1", "group1")
        
        self.assertEqual(session.session_id, "session1")
        self.assertEqual(session.source_device, "teacher1")
        self.assertEqual(session.target_device, "group1")
        self.assertTrue(session.is_active)
        self.assertIsInstance(session.start_time, datetime)
        self.assertEqual(len(session.input_events), 0)
    
    def test_session_to_dict(self):
        """测试会话转换为字典"""
        session = KeyboardSession("session1", "teacher1", "group1")
        session_dict = session.to_dict()
        
        self.assertIn("session_id", session_dict)
        self.assertIn("source_device", session_dict)
        self.assertIn("target_device", session_dict)
        self.assertIn("start_time", session_dict)
        self.assertIn("is_active", session_dict)
        self.assertIn("input_events_count", session_dict)

class TestKeyboardMonitor(unittest.TestCase):
    """测试键盘监控器类"""
    
    def setUp(self):
        """设置测试环境"""
        self.monitor = KeyboardMonitor("device1", "*************")
    
    def test_monitor_creation(self):
        """测试监控器创建"""
        self.assertEqual(self.monitor.device_id, "device1")
        self.assertEqual(self.monitor.device_ip, "*************")
        self.assertFalse(self.monitor.is_monitoring)
        self.assertIsNone(self.monitor.monitor_thread)
    
    @patch('subprocess.run')
    def test_check_keyboard_visibility_local(self, mock_run):
        """测试本地键盘可见性检查"""
        # 测试本地设备
        local_monitor = KeyboardMonitor("local", "localhost")
        
        # 测试键盘可见
        mock_run.return_value = Mock(returncode=0, stdout="boolean true")
        result = local_monitor._check_keyboard_visibility()
        self.assertTrue(result)
        
        # 测试键盘不可见
        mock_run.return_value = Mock(returncode=0, stdout="boolean false")
        result = local_monitor._check_keyboard_visibility()
        self.assertFalse(result)
        
        # 测试命令失败
        mock_run.return_value = Mock(returncode=1, stdout="")
        result = local_monitor._check_keyboard_visibility()
        self.assertFalse(result)
    
    @patch('subprocess.run')
    def test_check_keyboard_visibility_remote(self, mock_run):
        """测试远程键盘可见性检查"""
        # 测试远程设备
        remote_monitor = KeyboardMonitor("remote", "*************")
        
        # 测试键盘可见
        mock_run.return_value = Mock(returncode=0, stdout="boolean true")
        result = remote_monitor._check_keyboard_visibility()
        self.assertTrue(result)
        
        # 验证SSH命令被调用
        mock_run.assert_called()
        args = mock_run.call_args[0][0]
        self.assertIn('ssh', args)
        self.assertIn('user@*************', args)

class TestVirtualKeyboardModule(unittest.TestCase):
    """测试虚拟键盘模块"""
    
    def setUp(self):
        """设置测试环境"""
        # Mock认证管理器
        self.mock_auth_manager = Mock()
        self.mock_auth_manager.is_authenticated.return_value = True
        self.mock_auth_manager.get_current_user.return_value = {
            "id": "teacher1",
            "name": "测试教师",
            "role": "teacher"
        }
        
        # 创建虚拟键盘模块
        self.keyboard = VirtualKeyboardModule()
        self.keyboard.auth_manager = self.mock_auth_manager
    
    @patch('modules.virtual_keyboard.VirtualKeyboardWindow')
    @patch('subprocess.run')
    def test_module_initialization(self, mock_run, mock_window):
        """测试模块初始化"""
        # Mock dbus-send和onboard检查
        mock_run.return_value = Mock(returncode=0)
        
        # 初始化模块
        result = self.keyboard._initialize_module()
        
        # 验证初始化成功
        self.assertTrue(result)
        self.assertIsNotNone(self.keyboard.keyboard_window)
    
    @patch('subprocess.run')
    def test_module_initialization_without_dbus(self, mock_run):
        """测试没有dbus-send时的模块初始化"""
        # Mock dbus-send不可用
        mock_run.return_value = Mock(returncode=1)
        
        # 初始化模块
        result = self.keyboard._initialize_module()
        
        # 验证初始化失败
        self.assertFalse(result)
    
    def test_module_initialization_without_auth(self):
        """测试未认证时的模块初始化"""
        # 设置未认证状态
        self.mock_auth_manager.is_authenticated.return_value = False
        
        # 初始化模块
        result = self.keyboard._initialize_module()
        
        # 验证初始化失败
        self.assertFalse(result)
    
    @patch('subprocess.run')
    def test_toggle_virtual_keyboard_local(self, mock_run):
        """测试本地虚拟键盘切换"""
        # 添加本地设备
        device = DeviceInfo("local1", "group", "本地设备", "localhost", 8888, ["keyboard"])
        self.keyboard.available_devices["local1"] = device
        
        # Mock命令执行成功
        mock_run.return_value = Mock(returncode=0)
        
        # 切换键盘
        result = self.keyboard.toggle_virtual_keyboard("local1")
        
        # 验证切换成功
        self.assertTrue(result)
        
        # 验证dbus-send命令被调用
        mock_run.assert_called()
        args = mock_run.call_args[0][0]
        self.assertIn('dbus-send', args)
        self.assertIn('org.onboard.Onboard.Keyboard.ToggleVisible', args)
    
    @patch('subprocess.run')
    def test_toggle_virtual_keyboard_remote(self, mock_run):
        """测试远程虚拟键盘切换"""
        # 添加远程设备
        device = DeviceInfo("remote1", "group", "远程设备", "*************", 8888, ["keyboard"])
        self.keyboard.available_devices["remote1"] = device
        
        # Mock命令执行成功
        mock_run.return_value = Mock(returncode=0)
        
        # 切换键盘
        result = self.keyboard.toggle_virtual_keyboard("remote1")
        
        # 验证切换成功
        self.assertTrue(result)
        
        # 验证SSH命令被调用
        mock_run.assert_called()
        args = mock_run.call_args[0][0]
        self.assertIn('ssh', args)
        self.assertIn('user@*************', args)
    
    def test_toggle_virtual_keyboard_device_not_found(self):
        """测试设备不存在时的键盘切换"""
        # 切换不存在的设备
        result = self.keyboard.toggle_virtual_keyboard("nonexistent")
        
        # 验证切换失败
        self.assertFalse(result)
    
    def test_start_keyboard_session(self):
        """测试开始键盘会话"""
        # 添加测试设备
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["keyboard"])
        self.keyboard.available_devices["group1"] = device
        
        # 开始会话
        session_id = self.keyboard.start_keyboard_session("group1")
        
        # 验证会话开始成功
        self.assertIsNotNone(session_id)
        self.assertIn(session_id, self.keyboard.active_sessions)
        
        session = self.keyboard.active_sessions[session_id]
        self.assertEqual(session.target_device, "group1")
        self.assertTrue(session.is_active)
    
    def test_start_keyboard_session_device_not_found(self):
        """测试设备不存在时开始会话"""
        # 开始会话（设备不存在）
        session_id = self.keyboard.start_keyboard_session("nonexistent")
        
        # 验证会话开始失败
        self.assertIsNone(session_id)
        self.assertEqual(len(self.keyboard.active_sessions), 0)
    
    def test_start_keyboard_session_max_sessions(self):
        """测试达到最大会话数时开始会话"""
        # 设置最大会话数为1
        self.keyboard.config["max_sessions"] = 1
        
        # 添加测试设备
        device1 = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["keyboard"])
        device2 = DeviceInfo("group2", "group", "小组设备2", "192.168.1.101", 8888, ["keyboard"])
        self.keyboard.available_devices["group1"] = device1
        self.keyboard.available_devices["group2"] = device2
        
        # 开始第一个会话
        session_id1 = self.keyboard.start_keyboard_session("group1")
        self.assertIsNotNone(session_id1)
        
        # 尝试开始第二个会话
        session_id2 = self.keyboard.start_keyboard_session("group2")
        self.assertIsNone(session_id2)  # 应该失败
        
        # 验证只有一个会话
        self.assertEqual(len(self.keyboard.active_sessions), 1)
    
    def test_end_keyboard_session(self):
        """测试结束键盘会话"""
        # 先开始一个会话
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["keyboard"])
        self.keyboard.available_devices["group1"] = device
        session_id = self.keyboard.start_keyboard_session("group1")
        
        # 验证会话已创建
        self.assertIsNotNone(session_id)
        self.assertIn(session_id, self.keyboard.active_sessions)
        
        # 结束会话
        self.keyboard.end_keyboard_session(session_id)
        
        # 验证会话已结束
        self.assertNotIn(session_id, self.keyboard.active_sessions)
    
    def test_end_keyboard_session_nonexistent(self):
        """测试结束不存在的会话"""
        # 结束不存在的会话
        self.keyboard.end_keyboard_session("nonexistent")
        
        # 应该没有异常抛出
        self.assertEqual(len(self.keyboard.active_sessions), 0)
    
    @patch('subprocess.run')
    def test_send_input_to_device_local(self, mock_run):
        """测试向本地设备发送输入"""
        # 添加本地设备
        device = DeviceInfo("local1", "group", "本地设备", "localhost", 8888, ["keyboard"])
        self.keyboard.available_devices["local1"] = device
        
        # Mock命令执行成功
        mock_run.return_value = Mock(returncode=0)
        
        # 发送输入
        result = self.keyboard.send_input_to_device("local1", "Hello World")
        
        # 验证发送成功
        self.assertTrue(result)
        
        # 验证xdotool命令被调用
        mock_run.assert_called()
        args = mock_run.call_args[0][0]
        self.assertIn('xdotool', args)
        self.assertIn('type', args)
        self.assertIn('Hello World', args)
    
    @patch('subprocess.run')
    def test_send_key_to_device(self, mock_run):
        """测试向设备发送按键"""
        # 添加测试设备
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["keyboard"])
        self.keyboard.available_devices["group1"] = device
        
        # Mock命令执行成功
        mock_run.return_value = Mock(returncode=0)
        
        # 发送按键
        result = self.keyboard.send_key_to_device("group1", "Return")
        
        # 验证发送成功
        self.assertTrue(result)
        
        # 验证SSH + xdotool命令被调用
        mock_run.assert_called()
        args = mock_run.call_args[0][0]
        self.assertIn('ssh', args)
        self.assertIn('xdotool key Return', ' '.join(args))
    
    def test_get_available_devices(self):
        """测试获取可用设备"""
        # 添加测试设备
        device1 = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["keyboard"])
        device2 = DeviceInfo("group2", "group", "小组设备2", "192.168.1.101", 8888, ["keyboard"])
        device2.status = "offline"
        
        self.keyboard.available_devices = {
            "group1": device1,
            "group2": device2
        }
        
        # 获取可用设备
        available = self.keyboard.get_available_devices()
        
        # 验证结果（只有在线设备）
        self.assertEqual(len(available), 1)
        self.assertEqual(available[0].device_id, "group1")
    
    def test_get_keyboard_status(self):
        """测试获取键盘状态"""
        # 添加测试数据
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["keyboard"])
        self.keyboard.available_devices["group1"] = device
        
        session_id = self.keyboard.start_keyboard_session("group1")
        
        # 获取状态
        status = self.keyboard.get_keyboard_status()
        
        # 验证状态信息
        self.assertEqual(status["available_devices"], 1)
        self.assertEqual(status["active_sessions"], 1)
        self.assertEqual(len(status["sessions"]), 1)
        self.assertEqual(status["sessions"][0]["session_id"], session_id)
    
    def test_on_device_discovered(self):
        """测试设备发现事件"""
        # 创建设备信息
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["keyboard"])
        
        # 触发设备发现事件
        self.keyboard.on_device_discovered(device)
        
        # 验证设备已添加
        self.assertIn("group1", self.keyboard.available_devices)
        self.assertEqual(self.keyboard.available_devices["group1"], device)
    
    def test_on_device_offline(self):
        """测试设备离线事件"""
        # 先添加设备和会话
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["keyboard"])
        self.keyboard.available_devices["group1"] = device
        session_id = self.keyboard.start_keyboard_session("group1")
        
        # 验证设备和会话已创建
        self.assertIn("group1", self.keyboard.available_devices)
        self.assertIn(session_id, self.keyboard.active_sessions)
        
        # 触发设备离线事件
        self.keyboard.on_device_offline("group1")
        
        # 验证设备已移除，会话已结束
        self.assertNotIn("group1", self.keyboard.available_devices)
        self.assertNotIn(session_id, self.keyboard.active_sessions)
    
    def test_message_handling(self):
        """测试消息处理"""
        # 添加测试设备
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["keyboard"])
        self.keyboard.available_devices["group1"] = device
        
        # Mock相关方法
        self.keyboard.toggle_virtual_keyboard = Mock(return_value=True)
        self.keyboard.start_keyboard_session = Mock(return_value="session1")
        self.keyboard.end_keyboard_session = Mock()
        self.keyboard.send_input_to_device = Mock(return_value=True)
        
        # 测试切换键盘消息
        self.keyboard._handle_message("test_sender", "toggle_keyboard", {"device_id": "group1"})
        self.keyboard.toggle_virtual_keyboard.assert_called_with("group1")
        
        # 测试开始会话消息
        self.keyboard._handle_message("test_sender", "start_keyboard_session", {"target_device_id": "group1"})
        self.keyboard.start_keyboard_session.assert_called_with("group1")
        
        # 测试结束会话消息
        self.keyboard._handle_message("test_sender", "end_keyboard_session", {"session_id": "session1"})
        self.keyboard.end_keyboard_session.assert_called_with("session1")
        
        # 测试发送输入消息
        self.keyboard._handle_message("test_sender", "send_input", {"device_id": "group1", "input_text": "test"})
        self.keyboard.send_input_to_device.assert_called_with("group1", "test")
        
        # 测试获取状态消息
        self.keyboard._handle_message("test_sender", "get_keyboard_status", {})
        
        # 测试显示界面消息
        self.keyboard._handle_message("test_sender", "show_keyboard", {})

if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
