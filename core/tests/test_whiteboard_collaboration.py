"""
白板协作模块测试
"""
import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.whiteboard_collaboration import (
    WhiteboardCollaborationModule, CollaborationSession, XDotoolSyncWorker
)
from modules.udp_discovery import DeviceInfo

class TestCollaborationSession(unittest.TestCase):
    """测试协作会话类"""
    
    def test_session_creation(self):
        """测试会话创建"""
        session = CollaborationSession("session1", "teacher1", "group1")
        
        self.assertEqual(session.session_id, "session1")
        self.assertEqual(session.teacher_device, "teacher1")
        self.assertEqual(session.group_device, "group1")
        self.assertTrue(session.is_active)
        self.assertIsInstance(session.start_time, datetime)
        self.assertEqual(len(session.sync_events), 0)
    
    def test_session_to_dict(self):
        """测试会话转换为字典"""
        session = CollaborationSession("session1", "teacher1", "group1")
        session_dict = session.to_dict()
        
        self.assertIn("session_id", session_dict)
        self.assertIn("teacher_device", session_dict)
        self.assertIn("group_device", session_dict)
        self.assertIn("start_time", session_dict)
        self.assertIn("is_active", session_dict)
        self.assertIn("sync_events_count", session_dict)

class TestXDotoolSyncWorker(unittest.TestCase):
    """测试xdotool同步工作线程"""
    
    def setUp(self):
        """设置测试环境"""
        self.worker = XDotoolSyncWorker("*************")
    
    def test_worker_creation(self):
        """测试工作线程创建"""
        self.assertEqual(self.worker.target_device_ip, "*************")
        self.assertFalse(self.worker.is_running)
        self.assertIsNone(self.worker.sync_thread)
    
    @patch('subprocess.run')
    def test_send_mouse_event(self, mock_run):
        """测试发送鼠标事件"""
        mock_run.return_value = Mock(returncode=0)
        
        # 测试鼠标移动
        self.worker.send_mouse_event(100, 200, "move")
        mock_run.assert_called_with(
            ['ssh', 'user@*************', 'xdotool mousemove 100 200'],
            timeout=1, capture_output=True
        )
        
        # 测试鼠标点击
        self.worker.send_mouse_event(100, 200, "click")
        mock_run.assert_called_with(
            ['ssh', 'user@*************', 'xdotool mousemove 100 200 click 1'],
            timeout=1, capture_output=True
        )
    
    @patch('subprocess.run')
    def test_send_key_event(self, mock_run):
        """测试发送键盘事件"""
        mock_run.return_value = Mock(returncode=0)
        
        # 测试按键
        self.worker.send_key_event("Return", "press")
        mock_run.assert_called_with(
            ['ssh', 'user@*************', 'xdotool key Return'],
            timeout=1, capture_output=True
        )
        
        # 测试输入文本
        self.worker.send_key_event("Hello", "type")
        mock_run.assert_called_with(
            ['ssh', 'user@*************', 'xdotool type "Hello"'],
            timeout=1, capture_output=True
        )

class TestWhiteboardCollaborationModule(unittest.TestCase):
    """测试白板协作模块"""
    
    def setUp(self):
        """设置测试环境"""
        # Mock认证管理器
        self.mock_auth_manager = Mock()
        self.mock_auth_manager.is_authenticated.return_value = True
        self.mock_auth_manager.get_current_user.return_value = {
            "id": "teacher1",
            "name": "测试教师",
            "role": "teacher"
        }
        
        # 创建白板协作模块
        self.collaboration = WhiteboardCollaborationModule()
        self.collaboration.auth_manager = self.mock_auth_manager
    
    @patch('modules.whiteboard_collaboration.WhiteboardCollaborationWindow')
    @patch('subprocess.run')
    def test_module_initialization(self, mock_run, mock_window):
        """测试模块初始化"""
        # Mock xdotool检查
        mock_run.return_value = Mock(returncode=0)
        
        # 初始化模块
        result = self.collaboration._initialize_module()
        
        # 验证初始化成功
        self.assertTrue(result)
        self.assertIsNotNone(self.collaboration.collaboration_window)
    
    @patch('subprocess.run')
    def test_module_initialization_without_xdotool(self, mock_run):
        """测试没有xdotool时的模块初始化"""
        # Mock xdotool不可用
        mock_run.return_value = Mock(returncode=1)
        
        # 初始化模块
        result = self.collaboration._initialize_module()
        
        # 验证初始化失败
        self.assertFalse(result)
    
    def test_module_initialization_without_auth(self):
        """测试未认证时的模块初始化"""
        # 设置未认证状态
        self.mock_auth_manager.is_authenticated.return_value = False
        
        # 初始化模块
        result = self.collaboration._initialize_module()
        
        # 验证初始化失败
        self.assertFalse(result)
    
    def test_start_collaboration(self):
        """测试开始协作"""
        # 添加测试设备
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["whiteboard"])
        self.collaboration.available_devices["group1"] = device
        
        # 开始协作
        session_id = self.collaboration.start_collaboration("group1")
        
        # 验证协作开始成功
        self.assertIsNotNone(session_id)
        self.assertIn(session_id, self.collaboration.active_sessions)
        
        session = self.collaboration.active_sessions[session_id]
        self.assertEqual(session.group_device, "group1")
        self.assertTrue(session.is_active)
    
    def test_start_collaboration_device_not_available(self):
        """测试设备不可用时开始协作"""
        # 开始协作（设备不存在）
        session_id = self.collaboration.start_collaboration("nonexistent")
        
        # 验证协作开始失败
        self.assertIsNone(session_id)
        self.assertEqual(len(self.collaboration.active_sessions), 0)
    
    def test_stop_collaboration(self):
        """测试停止协作"""
        # 先开始一个协作会话
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["whiteboard"])
        self.collaboration.available_devices["group1"] = device
        session_id = self.collaboration.start_collaboration("group1")
        
        # 验证会话已创建
        self.assertIsNotNone(session_id)
        self.assertIn(session_id, self.collaboration.active_sessions)
        
        # 停止协作
        self.collaboration.stop_collaboration(session_id)
        
        # 验证会话已停止
        self.assertNotIn(session_id, self.collaboration.active_sessions)
    
    def test_stop_collaboration_nonexistent_session(self):
        """测试停止不存在的协作会话"""
        # 停止不存在的会话
        self.collaboration.stop_collaboration("nonexistent")
        
        # 应该没有异常抛出
        self.assertEqual(len(self.collaboration.active_sessions), 0)
    
    def test_handle_sync_event(self):
        """测试处理同步事件"""
        # 创建协作会话
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["whiteboard"])
        self.collaboration.available_devices["group1"] = device
        session_id = self.collaboration.start_collaboration("group1")
        
        # 创建同步事件
        event_data = {
            "type": "mouse_move",
            "x": 100,
            "y": 200,
            "timestamp": datetime.now().isoformat()
        }
        
        # 处理同步事件
        self.collaboration.handle_sync_event(event_data)
        
        # 验证事件已记录
        session = self.collaboration.active_sessions[session_id]
        self.assertEqual(len(session.sync_events), 1)
        self.assertEqual(session.sync_events[0], event_data)
    
    def test_get_available_groups(self):
        """测试获取可用小组设备"""
        # 添加测试设备
        device1 = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["whiteboard"])
        device2 = DeviceInfo("teacher1", "teacher", "教师设备", "192.168.1.101", 8888, ["whiteboard"])
        device3 = DeviceInfo("group2", "group", "小组设备2", "192.168.1.102", 8888, ["whiteboard"])
        device3.status = "offline"
        
        self.collaboration.available_devices = {
            "group1": device1,
            "teacher1": device2,
            "group2": device3
        }
        
        # 获取可用小组设备
        groups = self.collaboration.get_available_groups()
        
        # 验证结果（只有在线的小组设备）
        self.assertEqual(len(groups), 1)
        self.assertEqual(groups[0].device_id, "group1")
    
    def test_get_collaboration_status(self):
        """测试获取协作状态"""
        # 添加测试设备和会话
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["whiteboard"])
        self.collaboration.available_devices["group1"] = device
        session_id = self.collaboration.start_collaboration("group1")
        
        # 获取协作状态
        status = self.collaboration.get_collaboration_status()
        
        # 验证状态信息
        self.assertEqual(status["active_sessions"], 1)
        self.assertEqual(status["available_devices"], 1)
        self.assertEqual(len(status["sessions"]), 1)
        self.assertEqual(status["sessions"][0]["session_id"], session_id)
    
    def test_on_device_discovered(self):
        """测试设备发现事件"""
        # 创建设备信息
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["whiteboard"])
        
        # 触发设备发现事件
        self.collaboration.on_device_discovered(device)
        
        # 验证设备已添加
        self.assertIn("group1", self.collaboration.available_devices)
        self.assertEqual(self.collaboration.available_devices["group1"], device)
    
    def test_on_device_offline(self):
        """测试设备离线事件"""
        # 先添加设备和协作会话
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["whiteboard"])
        self.collaboration.available_devices["group1"] = device
        session_id = self.collaboration.start_collaboration("group1")
        
        # 验证会话已创建
        self.assertIn(session_id, self.collaboration.active_sessions)
        
        # 触发设备离线事件
        self.collaboration.on_device_offline("group1")
        
        # 验证设备已移除，会话已停止
        self.assertNotIn("group1", self.collaboration.available_devices)
        self.assertNotIn(session_id, self.collaboration.active_sessions)
    
    def test_message_handling(self):
        """测试消息处理"""
        # 添加测试设备
        device = DeviceInfo("group1", "group", "小组设备1", "*************", 8888, ["whiteboard"])
        self.collaboration.available_devices["group1"] = device
        
        # 测试开始协作消息
        self.collaboration._handle_message("test_sender", "start_collaboration", {"group_device_id": "group1"})
        
        # 验证协作已开始
        self.assertEqual(len(self.collaboration.active_sessions), 1)
        
        # 获取会话ID
        session_id = list(self.collaboration.active_sessions.keys())[0]
        
        # 测试停止协作消息
        self.collaboration._handle_message("test_sender", "stop_collaboration", {"session_id": session_id})
        
        # 验证协作已停止
        self.assertEqual(len(self.collaboration.active_sessions), 0)
        
        # 测试获取状态消息
        self.collaboration._handle_message("test_sender", "get_collaboration_status", {})
        
        # 测试显示界面消息
        self.collaboration._handle_message("test_sender", "show_collaboration", {})

if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
