"""
录屏模块单元测试
"""
import unittest
import sys
import os
import tempfile
import json
import time
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.screen_recorder_refactored import ScreenRecorderModule, ScreenRecorderWindow

class TestScreenRecorderModule(unittest.TestCase):
    """录屏模块测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.module = ScreenRecorderModule()
        
        # 模拟通信管理器
        self.module.comm_manager = Mock()
        self.module.message_bus = Mock()
        
        # 使用临时目录作为输出目录
        self.temp_dir = tempfile.mkdtemp()
        self.module.output_directory = self.temp_dir
        
        # 初始化模块
        self.module.initialize()
    
    def tearDown(self):
        """每个测试后的清理"""
        if self.module:
            self.module.cleanup()
        
        # 清理临时目录
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_module_initialization(self):
        """测试模块初始化"""
        self.assertTrue(self.module.is_initialized)
        self.assertEqual(self.module.module_name, "screen_recorder")
        self.assertEqual(self.module.display_name, "屏幕录制")
        self.assertIsNotNone(self.module.recorder_window)
        self.assertTrue(os.path.exists(self.module.output_directory))
    
    def test_ffmpeg_check(self):
        """测试FFmpeg检查"""
        # 模拟FFmpeg可用
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 0
            result = self.module._check_ffmpeg()
            self.assertTrue(result)
        
        # 模拟FFmpeg不可用
        with patch('subprocess.run', side_effect=FileNotFoundError):
            result = self.module._check_ffmpeg()
            self.assertFalse(result)
    
    def test_settings_management(self):
        """测试设置管理"""
        # 测试应用设置
        test_settings = {
            "format": "avi",
            "quality": "ultra",
            "framerate": 60,
            "audio": False,
            "cursor": False
        }
        
        self.module._apply_settings(test_settings)
        
        self.assertEqual(self.module.video_format, "avi")
        self.assertEqual(self.module.video_quality, "ultra")
        self.assertEqual(self.module.frame_rate, 60)
        self.assertFalse(self.module.audio_enabled)
        self.assertFalse(self.module.cursor_enabled)
        
        # 测试获取当前设置
        current_settings = self.module._get_current_settings()
        self.assertEqual(current_settings["format"], "avi")
        self.assertEqual(current_settings["quality"], "ultra")
        self.assertEqual(current_settings["framerate"], 60)
    
    def test_ffmpeg_command_building(self):
        """测试FFmpeg命令构建"""
        # 设置测试参数
        self.module.video_format = "mp4"
        self.module.video_quality = "high"
        self.module.frame_rate = 30
        self.module.audio_enabled = True
        self.module.cursor_enabled = True
        self.module.output_file = os.path.join(self.temp_dir, "test.mp4")
        
        command = self.module._build_ffmpeg_command()
        
        self.assertIsInstance(command, list)
        self.assertIn("ffmpeg", command)
        self.assertIn("-framerate", command)
        self.assertIn("30", command)
        self.assertIn(self.module.output_file, command)
    
    def test_recording_lifecycle_mock(self):
        """测试录制生命周期（模拟）"""
        # 模拟subprocess.Popen
        with patch('subprocess.Popen') as mock_popen:
            mock_process = Mock()
            mock_popen.return_value = mock_process
            
            # 测试开始录制
            result = self.module.start_recording()
            self.assertTrue(result)
            self.assertTrue(self.module.is_recording)
            self.assertFalse(self.module.is_paused)
            self.assertIsNotNone(self.module.start_time)
            self.assertIsNotNone(self.module.output_file)
            
            # 测试暂停录制
            result = self.module.pause_recording()
            self.assertTrue(result)
            self.assertTrue(self.module.is_paused)
            
            # 测试恢复录制
            result = self.module.resume_recording()
            self.assertTrue(result)
            self.assertFalse(self.module.is_paused)
            
            # 测试停止录制
            result = self.module.stop_recording()
            self.assertTrue(result)
            self.assertFalse(self.module.is_recording)
            self.assertFalse(self.module.is_paused)
    
    def test_recording_status(self):
        """测试录制状态"""
        # 初始状态
        status = self.module.get_recording_status()
        self.assertFalse(status["is_recording"])
        self.assertFalse(status["is_paused"])
        self.assertEqual(status["elapsed_time"], 0)
        
        # 模拟录制状态
        self.module.is_recording = True
        self.module.is_paused = False
        self.module.elapsed_time = 120
        self.module.output_file = "test.mp4"
        
        status = self.module.get_recording_status()
        self.assertTrue(status["is_recording"])
        self.assertFalse(status["is_paused"])
        self.assertEqual(status["elapsed_time"], 120)
        self.assertEqual(status["output_file"], "test.mp4")
    
    def test_recording_history(self):
        """测试录制历史"""
        # 测试初始状态
        history = self.module.get_recording_history()
        self.assertIsInstance(history, list)
        
        # 添加测试记录
        self.module.output_file = "test.mp4"
        self.module.start_time = time.time()
        self.module.elapsed_time = 60
        self.module._add_to_history("completed")
        
        history = self.module.get_recording_history()
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]["status"], "completed")
        self.assertEqual(history[0]["duration"], 60)
    
    def test_history_persistence(self):
        """测试历史记录持久化"""
        # 添加测试记录
        test_record = {
            "file_path": "test.mp4",
            "file_name": "test.mp4",
            "status": "completed",
            "start_time": "2024-01-01T12:00:00",
            "duration": 120,
            "settings": {"format": "mp4"},
            "timestamp": "2024-01-01T12:02:00"
        }
        
        self.module.recording_history.append(test_record)
        
        # 保存历史
        self.module._save_recording_history()
        
        # 清空内存中的历史
        self.module.recording_history.clear()
        
        # 重新加载
        self.module._load_recording_history()
        
        # 验证数据
        history = self.module.get_recording_history()
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]["file_name"], "test.mp4")
        self.assertEqual(history[0]["duration"], 120)
    
    def test_message_handling(self):
        """测试消息处理"""
        # 模拟subprocess.Popen
        with patch('subprocess.Popen') as mock_popen:
            mock_process = Mock()
            mock_popen.return_value = mock_process
            
            # 测试开始录制消息
            self.module._handle_message("test_sender", "start_recording", {})
            self.assertTrue(self.module.is_recording)
            
            # 测试暂停录制消息
            self.module._handle_message("test_sender", "pause_recording", {})
            self.assertTrue(self.module.is_paused)
            
            # 测试恢复录制消息
            self.module._handle_message("test_sender", "resume_recording", {})
            self.assertFalse(self.module.is_paused)
            
            # 测试停止录制消息
            self.module._handle_message("test_sender", "stop_recording", {})
            self.assertFalse(self.module.is_recording)
    
    def test_timer_update(self):
        """测试定时器更新"""
        # 设置录制状态
        self.module.is_recording = True
        self.module.is_paused = False
        self.module.start_time = time.time() - 10  # 10秒前开始
        self.module.pause_time = 0
        
        # 模拟定时器更新
        self.module._update_timer()
        
        # 验证时间更新
        self.assertGreaterEqual(self.module.elapsed_time, 9)
        self.assertLessEqual(self.module.elapsed_time, 11)
    
    def test_max_recording_time(self):
        """测试最大录制时间限制"""
        # 设置短的最大录制时间
        self.module.config["max_recording_time"] = 5
        
        # 模拟长时间录制
        self.module.is_recording = True
        self.module.start_time = time.time() - 10  # 10秒前开始
        self.module.pause_time = 0
        
        with patch.object(self.module, 'stop_recording') as mock_stop:
            self.module._update_timer()
            mock_stop.assert_called_once()

class TestScreenRecorderWindow(unittest.TestCase):
    """录屏窗口测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.module = ScreenRecorderModule()
        self.module.comm_manager = Mock()
        self.module.message_bus = Mock()
        
        # 使用临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.module.output_directory = self.temp_dir
        
        self.module.initialize()
        self.window = self.module.recorder_window
    
    def tearDown(self):
        """每个测试后的清理"""
        if self.module:
            self.module.cleanup()
        
        # 清理临时目录
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_window_initialization(self):
        """测试窗口初始化"""
        self.assertIsNotNone(self.window)
        self.assertIsNotNone(self.window.time_label)
        self.assertIsNotNone(self.window.record_btn)
        self.assertIsNotNone(self.window.pause_btn)
        self.assertIsNotNone(self.window.stop_btn)
    
    def test_settings_update(self):
        """测试设置更新"""
        # 设置测试值
        self.window.format_combo.setCurrentText("avi")
        self.window.quality_combo.setCurrentText("ultra")
        self.window.framerate_combo.setCurrentText("60")
        self.window.audio_cb.setChecked(False)
        self.window.cursor_cb.setChecked(False)
        
        # 更新设置
        self.window.update_settings()
        
        # 验证设置已更新
        self.assertEqual(self.module.video_format, "avi")
        self.assertEqual(self.module.video_quality, "ultra")
        self.assertEqual(self.module.frame_rate, 60)
        self.assertFalse(self.module.audio_enabled)
        self.assertFalse(self.module.cursor_enabled)
    
    def test_time_display_update(self):
        """测试时间显示更新"""
        # 测试时间显示
        self.window.update_time_display(3661)  # 1小时1分1秒
        self.assertEqual(self.window.time_label.text(), "01:01:01")
        
        self.window.update_time_display(125)  # 2分5秒
        self.assertEqual(self.window.time_label.text(), "00:02:05")
    
    def test_button_states(self):
        """测试按钮状态"""
        # 初始状态
        self.assertTrue(self.window.record_btn.isEnabled())
        self.assertFalse(self.window.pause_btn.isEnabled())
        self.assertFalse(self.window.stop_btn.isEnabled())
        
        # 模拟开始录制
        with patch.object(self.module, 'start_recording', return_value=True):
            self.window.start_recording()
            
            self.assertTrue(self.window.record_btn.isEnabled())
            self.assertTrue(self.window.pause_btn.isEnabled())
            self.assertTrue(self.window.stop_btn.isEnabled())
    
    def test_history_refresh(self):
        """测试历史刷新"""
        # 添加测试历史
        test_history = [
            {
                "file_name": "test1.mp4",
                "status": "completed",
                "duration": 120
            },
            {
                "file_name": "test2.mp4",
                "status": "completed",
                "duration": 60
            }
        ]
        
        self.module.recording_history = test_history
        
        # 刷新历史
        self.window.refresh_history()
        
        # 验证历史列表
        self.assertEqual(self.window.history_list.count(), 2)
    
    def test_settings_controls_state(self):
        """测试设置控件状态"""
        # 测试启用状态
        self.window._set_settings_enabled(True)
        self.assertTrue(self.window.format_combo.isEnabled())
        self.assertTrue(self.window.quality_combo.isEnabled())
        self.assertTrue(self.window.framerate_combo.isEnabled())
        
        # 测试禁用状态
        self.window._set_settings_enabled(False)
        self.assertFalse(self.window.format_combo.isEnabled())
        self.assertFalse(self.window.quality_combo.isEnabled())
        self.assertFalse(self.window.framerate_combo.isEnabled())

if __name__ == '__main__':
    unittest.main()
