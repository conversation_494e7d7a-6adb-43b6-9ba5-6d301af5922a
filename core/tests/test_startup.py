#!/usr/bin/env python3
"""
应用程序启动测试脚本
用于验证应用程序是否能正常启动
"""
import sys
import os
import warnings

# 过滤PyQt5的弃用警告
warnings.filterwarnings("ignore", category=DeprecationWarning, module="PyQt5")

def test_imports():
    """测试关键模块导入"""
    print("测试模块导入...")
    
    try:
        # 测试PyQt5导入
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QIcon
        print("✓ PyQt5 导入成功")
        
        # 测试核心模块导入
        from auth_manager import AuthManager
        print("✓ AuthManager 导入成功")
        
        from module_manager import ModuleManager
        print("✓ ModuleManager 导入成功")
        
        from communication_manager import CommunicationManager
        print("✓ CommunicationManager 导入成功")
        
        # 测试主要功能模块导入
        from modules.udp_discovery import UDPDiscoveryModule
        print("✓ UDPDiscoveryModule 导入成功")
        
        from modules.device_dashboard import DeviceDashboardModule
        print("✓ DeviceDashboardModule 导入成功")
        
        from modules.random_picker import RandomPickerModule
        print("✓ RandomPickerModule 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    try:
        # 创建QApplication（不显示界面）
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # 设置高DPI支持
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        app = QApplication([])
        print("✓ QApplication 创建成功")
        
        # 测试认证管理器
        from auth_manager import AuthManager
        auth_manager = AuthManager()
        print("✓ AuthManager 创建成功")
        
        # 测试通信管理器
        from communication_manager import CommunicationManager
        comm_manager = CommunicationManager()
        print("✓ CommunicationManager 创建成功")
        
        # 测试模块管理器
        from module_manager import ModuleManager
        module_manager = ModuleManager()
        print("✓ ModuleManager 创建成功")
        
        # 测试模块加载
        available_modules = module_manager.get_available_modules()
        print(f"✓ 发现 {len(available_modules)} 个可用模块")
        
        # 清理
        app.quit()
        return True
        
    except Exception as e:
        print(f"✗ 功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """测试配置系统"""
    print("\n测试配置系统...")
    
    try:
        from deployment_manager import ConfigManager
        
        config_manager = ConfigManager()
        print("✓ ConfigManager 创建成功")
        
        # 测试加载配置
        app_config = config_manager.load_app_config()
        user_config = config_manager.load_user_config()
        
        print("✓ 配置文件加载成功")
        print(f"  - 应用名称: {app_config.get('app', {}).get('name', 'Unknown')}")
        print(f"  - 应用版本: {app_config.get('app', {}).get('version', 'Unknown')}")
        print(f"  - UDP端口: {app_config.get('network', {}).get('udp_port', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_module_architecture():
    """测试模块架构"""
    print("\n测试模块架构...")
    
    try:
        from modules.base_module import BaseModule, TeacherModule, CommonModule
        print("✓ 基础模块类导入成功")
        
        # 测试创建一个简单的模块实例
        from modules.udp_discovery import UDPDiscoveryModule
        
        # 创建模块实例（不启动）
        udp_module = UDPDiscoveryModule()
        print("✓ UDP发现模块实例创建成功")
        
        # 检查模块属性
        print(f"  - 模块名称: {udp_module.module_name}")
        print(f"  - 显示名称: {udp_module.display_name}")
        print(f"  - 模块版本: {udp_module.version}")
        print(f"  - 是否通用模块: {udp_module.is_common}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模块架构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("智慧课堂教学工具 - 启动测试")
    print("=" * 60)
    
    tests = [
        ("模块导入测试", test_imports),
        ("基本功能测试", test_basic_functionality),
        ("配置系统测试", test_configuration),
        ("模块架构测试", test_module_architecture)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用程序可以正常启动。")
        print("\n要启动完整应用程序，请运行:")
        print("  python run_app.py")
        print("或:")
        print("  python main_app.py")
        return 0
    else:
        print("❌ 部分测试失败，请检查错误信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
