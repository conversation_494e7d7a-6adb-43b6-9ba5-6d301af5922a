"""
截屏模块单元测试
"""
import unittest
import sys
import os
import tempfile
import json
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QPixmap, QColor
from PyQt5.QtCore import QRect, QPoint

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.screenshot_module import ScreenshotModule, ScreenshotWindow, RegionSelector

class TestScreenshotModule(unittest.TestCase):
    """截屏模块测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.module = ScreenshotModule()
        
        # 模拟通信管理器
        self.module.comm_manager = Mock()
        self.module.message_bus = Mock()
        
        # 使用临时目录作为保存目录
        self.temp_dir = tempfile.mkdtemp()
        self.module.save_directory = self.temp_dir
        
        # 初始化模块
        self.module.initialize()
    
    def tearDown(self):
        """每个测试后的清理"""
        if self.module:
            self.module.cleanup()
        
        # 清理临时目录
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_module_initialization(self):
        """测试模块初始化"""
        self.assertTrue(self.module.is_initialized)
        self.assertEqual(self.module.module_name, "screenshot")
        self.assertEqual(self.module.display_name, "屏幕截图")
        self.assertIsNotNone(self.module.screenshot_window)
        self.assertTrue(os.path.exists(self.module.save_directory))
    
    def test_screenshot_settings(self):
        """测试截屏设置"""
        # 测试默认设置
        self.assertEqual(self.module.image_format, "png")
        self.assertEqual(self.module.image_quality, 95)
        self.assertTrue(self.module.auto_save)
        self.assertTrue(self.module.show_cursor)
        self.assertEqual(self.module.capture_delay, 0)
        
        # 测试设置修改
        self.module.image_format = "jpg"
        self.module.image_quality = 80
        self.module.auto_save = False
        self.module.capture_delay = 3
        
        self.assertEqual(self.module.image_format, "jpg")
        self.assertEqual(self.module.image_quality, 80)
        self.assertFalse(self.module.auto_save)
        self.assertEqual(self.module.capture_delay, 3)
    
    def test_fullscreen_capture(self):
        """测试全屏截图"""
        with patch.object(QApplication, 'primaryScreen') as mock_screen:
            mock_screen_obj = Mock()
            mock_pixmap = QPixmap(100, 100)
            mock_screen_obj.grabWindow.return_value = mock_pixmap
            mock_screen.return_value = mock_screen_obj
            
            pixmap = self.module._capture_fullscreen()
            self.assertIsNotNone(pixmap)
            self.assertEqual(pixmap.width(), 100)
            self.assertEqual(pixmap.height(), 100)
    
    def test_screenshot_history(self):
        """测试截屏历史"""
        # 测试初始状态
        history = self.module.get_screenshot_history()
        self.assertIsInstance(history, list)
        
        # 添加测试记录
        test_filepath = os.path.join(self.temp_dir, "test.png")
        with open(test_filepath, 'w') as f:
            f.write("test")
        
        self.module._add_to_history(test_filepath, "fullscreen")
        
        history = self.module.get_screenshot_history()
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]["type"], "fullscreen")
        self.assertEqual(history[0]["filename"], "test.png")
    
    def test_history_persistence(self):
        """测试历史记录持久化"""
        # 添加测试记录
        test_record = {
            "filepath": "test.png",
            "filename": "test.png",
            "type": "fullscreen",
            "timestamp": "2024-01-01T12:00:00",
            "size": 1024
        }
        
        self.module.screenshot_history.append(test_record)
        
        # 保存历史
        self.module._save_screenshot_history()
        
        # 清空内存中的历史
        self.module.screenshot_history.clear()
        
        # 重新加载
        self.module._load_screenshot_history()
        
        # 验证数据
        history = self.module.get_screenshot_history()
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]["filename"], "test.png")
        self.assertEqual(history[0]["type"], "fullscreen")
    
    def test_annotation_drawing(self):
        """测试标注绘制"""
        # 创建测试图片
        pixmap = QPixmap(100, 100)
        pixmap.fill(QColor(255, 255, 255))
        
        # 创建测试标注
        pen_annotation = {
            "tool": "pen",
            "color": "#FF0000",
            "width": 3,
            "points": [
                {"x": 10, "y": 10},
                {"x": 20, "y": 20},
                {"x": 30, "y": 30}
            ]
        }
        
        rect_annotation = {
            "tool": "rectangle",
            "color": "#00FF00",
            "width": 2,
            "rect": {"x": 40, "y": 40, "width": 20, "height": 15}
        }
        
        # 测试绘制（这里只测试不会出错）
        from PyQt5.QtGui import QPainter
        painter = QPainter(pixmap)
        
        try:
            self.module._draw_annotation(painter, pen_annotation)
            self.module._draw_annotation(painter, rect_annotation)
        except Exception as e:
            self.fail(f"绘制标注时出错: {e}")
        finally:
            painter.end()
    
    def test_save_screenshot(self):
        """测试保存截图"""
        # 创建测试图片
        pixmap = QPixmap(100, 100)
        pixmap.fill(QColor(255, 255, 255))
        
        # 保存截图
        filepath = self.module.save_screenshot(pixmap)
        
        self.assertIsNotNone(filepath)
        self.assertTrue(os.path.exists(filepath))
        self.assertTrue(filepath.endswith(f".{self.module.image_format}"))
        
        # 验证历史记录
        history = self.module.get_screenshot_history()
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]["type"], "edited")
    
    def test_message_handling(self):
        """测试消息处理"""
        # 模拟截屏
        with patch.object(self.module, 'take_screenshot') as mock_screenshot:
            self.module._handle_message("test_sender", "take_screenshot", {"type": "fullscreen"})
            mock_screenshot.assert_called_once_with("fullscreen")
        
        # 模拟打开编辑器
        with patch.object(self.module, 'open_editor') as mock_editor:
            self.module._handle_message("test_sender", "open_editor", {"image_path": "test.png"})
            mock_editor.assert_called_once_with("test.png")
    
    def test_take_screenshot_with_delay(self):
        """测试延迟截图"""
        with patch.object(self.module, '_do_screenshot') as mock_do_screenshot:
            # 测试无延迟
            self.module.take_screenshot("fullscreen", 0)
            mock_do_screenshot.assert_called_once_with("fullscreen")
            
            # 测试有延迟（这里只测试QTimer.singleShot被调用）
            with patch('PyQt5.QtCore.QTimer.singleShot') as mock_timer:
                self.module.take_screenshot("fullscreen", 2)
                mock_timer.assert_called_once()

class TestScreenshotWindow(unittest.TestCase):
    """截屏窗口测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.module = ScreenshotModule()
        self.module.comm_manager = Mock()
        self.module.message_bus = Mock()
        
        # 使用临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.module.save_directory = self.temp_dir
        
        self.module.initialize()
        self.window = self.module.screenshot_window
    
    def tearDown(self):
        """每个测试后的清理"""
        if self.module:
            self.module.cleanup()
        
        # 清理临时目录
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_window_initialization(self):
        """测试窗口初始化"""
        self.assertIsNotNone(self.window)
        self.assertIsNotNone(self.window.fullscreen_btn)
        self.assertIsNotNone(self.window.region_btn)
        self.assertIsNotNone(self.window.window_btn)
        self.assertIsNotNone(self.window.delay_spin)
        self.assertIsNotNone(self.window.format_combo)
        self.assertIsNotNone(self.window.quality_spin)
    
    def test_settings_update(self):
        """测试设置更新"""
        # 设置测试值
        self.window.delay_spin.setValue(5)
        self.window.format_combo.setCurrentText("jpg")
        self.window.quality_spin.setValue(80)
        self.window.auto_save_cb.setChecked(False)
        
        # 更新设置
        self.window.update_settings()
        
        # 验证设置已更新
        self.assertEqual(self.module.capture_delay, 5)
        self.assertEqual(self.module.image_format, "jpg")
        self.assertEqual(self.module.image_quality, 80)
        self.assertFalse(self.module.auto_save)
    
    def test_screenshot_buttons(self):
        """测试截屏按钮"""
        # 模拟截屏方法
        with patch.object(self.module, 'take_screenshot') as mock_screenshot:
            # 测试全屏截图按钮
            with patch.object(self.window, 'hide'), patch.object(self.window, 'show'):
                self.window.take_screenshot("fullscreen")
                # 由于使用了QTimer.singleShot，这里只测试方法被调用
    
    def test_button_styles(self):
        """测试按钮样式"""
        style = self.window._get_button_style("#FF0000")
        self.assertIn("background-color: #FF0000", style)
        self.assertIn("color: white", style)
        self.assertIn("border-radius: 5px", style)

class TestRegionSelector(unittest.TestCase):
    """区域选择器测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        # 模拟屏幕截图
        with patch.object(QApplication, 'primaryScreen') as mock_screen:
            mock_screen_obj = Mock()
            mock_pixmap = QPixmap(100, 100)
            mock_screen_obj.grabWindow.return_value = mock_pixmap
            mock_screen.return_value = mock_screen_obj
            
            self.selector = RegionSelector()
    
    def test_selector_initialization(self):
        """测试选择器初始化"""
        self.assertIsNotNone(self.selector)
        self.assertEqual(self.selector.selected_rect, QRect())
        self.assertFalse(self.selector.selecting)
    
    def test_mouse_events(self):
        """测试鼠标事件"""
        from PyQt5.QtGui import QMouseEvent
        from PyQt5.QtCore import Qt
        
        # 模拟鼠标按下事件
        press_event = QMouseEvent(
            QMouseEvent.MouseButtonPress,
            QPoint(10, 10),
            Qt.LeftButton,
            Qt.LeftButton,
            Qt.NoModifier
        )
        
        self.selector.mousePressEvent(press_event)
        self.assertTrue(self.selector.selecting)
        self.assertEqual(self.selector.start_point, QPoint(10, 10))
        
        # 模拟鼠标移动事件
        move_event = QMouseEvent(
            QMouseEvent.MouseMove,
            QPoint(50, 50),
            Qt.NoButton,
            Qt.LeftButton,
            Qt.NoModifier
        )
        
        self.selector.mouseMoveEvent(move_event)
        self.assertEqual(self.selector.end_point, QPoint(50, 50))
        expected_rect = QRect(QPoint(10, 10), QPoint(50, 50)).normalized()
        self.assertEqual(self.selector.selected_rect, expected_rect)
    
    def test_keyboard_events(self):
        """测试键盘事件"""
        from PyQt5.QtGui import QKeyEvent
        from PyQt5.QtCore import Qt
        
        # 模拟Escape键事件
        with patch.object(self.selector, 'reject') as mock_reject:
            key_event = QKeyEvent(
                QKeyEvent.KeyPress,
                Qt.Key_Escape,
                Qt.NoModifier
            )
            
            self.selector.keyPressEvent(key_event)
            mock_reject.assert_called_once()

if __name__ == '__main__':
    unittest.main()
