from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db, socketio
from app.models.resource import Question, Exam, ExamQuestion, CoursePlan, Resource
from app.models.course import Course, CourseStudent, Group, GroupMember
from app.models.interaction import QuestionSession, SessionQuestion, TemporaryQuestion, ScreenshotQuestion, StudentAnswer, GroupDiscussion
from app.models.activity import ClassActivity
from app.models.user import User
from app.models.score import Score
from app.models.interaction import StudentInteraction
import json
import base64
import os
import uuid
from datetime import datetime, timedelta

interaction = Blueprint('interaction', __name__)

@interaction.route('/api/send-to-teacher', methods=['POST'])
@login_required
def send_to_teacher():
    """学生向教师发送互动内容"""
    if not current_user.is_student():
        return jsonify({'success': False, 'message': '只有学生可以发送内容'}), 403

    course_id = request.form.get('course_id')
    content = request.form.get('content')
    file = request.files.get('file')

    if not course_id:
        return jsonify({'success': False, 'message': '缺少课程ID'}), 400

    course = Course.query.get_or_404(course_id)

    # 验证学生是否在该课程中
    if not course.has_student(current_user.id):
        return jsonify({'success': False, 'message': '您未加入此课程'}), 403

    resource_id = None
    if file:
        # 保存文件并创建资源记录
        from app.controllers.resource import determine_file_type, get_allowed_extensions, calculate_file_md5
        from werkzeug.utils import secure_filename
        
        filename = secure_filename(file.filename)
        file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
        allowed_extensions = get_allowed_extensions()

        if file_ext not in allowed_extensions:
            return jsonify({'success': False, 'message': f'不支持的文件类型: {filename}'}), 400

        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        file_type = determine_file_type(file_ext)
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)
        file_size = os.path.getsize(file_path)
        md5_checksum = calculate_file_md5(file_path)

        resource = Resource(
            name=filename,
            type=file_type,
            url=unique_filename,
            size=file_size,
            format=file_ext,
            owner_id=current_user.id,
            course_id=course_id,
            file_metadata={
                'upload_date': datetime.utcnow().isoformat(),
                'md5_checksum': md5_checksum,
                'original_name': filename
            }
        )
        db.session.add(resource)
        db.session.flush()
        resource_id = resource.id

    interaction_record = StudentInteraction(
        course_id=course.id,
        student_id=current_user.id,
        teacher_id=course.teacher_id,
        content=content,
        resource_id=resource_id
    )
    db.session.add(interaction_record)
    db.session.commit()

    # 通过WebSocket通知教师
    socketio.emit('student_interaction', interaction_record.to_dict(), room=f'teacher_{course.teacher_id}')

    return jsonify({'success': True, 'message': '发送成功'})

@interaction.route('/api/teacher-send-message', methods=['POST'])
@login_required
def teacher_send_message():
    """教师向学生发送消息"""
    if not current_user.is_teacher():
        return jsonify({'success': False, 'message': '只有教师可以发送消息'}), 403

    data = request.get_json()
    course_id = data.get('course_id')
    content = data.get('content')

    if not all([course_id, content]):
        return jsonify({'success': False, 'message': '缺少课程ID或内容'}), 400

    # 通过WebSocket将消息广播给课程中的所有学生
    socketio.emit('teacher_message', {
        'teacher_name': current_user.username,
        'content': content,
        'created_at': datetime.utcnow().isoformat()
    }, room=f'course_{course_id}')

    return jsonify({'success': True, 'message': '消息已发送'})


def record_activity(course_id, activity_type, content_id, title, description=None, end_time=None, participants_count=0):
    """
    记录课堂活动
    
    Args:
        course_id: 课程ID
        activity_type: 活动类型
        content_id: 关联的内容ID
        title: 活动标题
        description: 活动描述
        end_time: 结束时间
        participants_count: 参与人数
        
    Returns:
        创建的活动记录
    """
    try:
        activity = ClassActivity(
            course_id=course_id,
            type=activity_type,
            content_id=content_id,
            title=title,
            description=description,
            created_by=current_user.id,
            end_time=end_time,
            participants_count=participants_count
        )
        
        db.session.add(activity)
        db.session.commit()
        return activity
    except Exception as e:
        db.session.rollback()
        print(f"记录活动失败: {str(e)}")
        return None

def notify_students_new_discussion(discussion_id, course_id):
    """通知学生新的讨论已发起"""
    discussion = GroupDiscussion.query.get(discussion_id)
    if discussion:
        data = {
            'type': 'new_discussion',
            'discussion_id': discussion_id,
            'title': discussion.title,
            'course_id': course_id,
            'timestamp': datetime.utcnow().isoformat()
        }
        socketio.emit('classroom_event', data, room=f'course_{course_id}')

def notify_students_discussion_ended(discussion_id, course_id):
    """通知学生讨论已结束"""
    data = {
        'type': 'discussion_ended',
        'discussion_id': discussion_id,
        'course_id': course_id,
        'timestamp': datetime.utcnow().isoformat()
    }
    socketio.emit('classroom_event', data, room=f'course_{course_id}')
    
def notify_students_group_discussion(discussion_id, course_id):
    """
    通知学生有新的分组讨论
    
    Args:
        discussion_id: 讨论ID
        course_id: 课程ID
    """
    # 获取讨论信息
    discussion = GroupDiscussion.query.get(discussion_id)
    if not discussion:
        return
    
    # 构建通知数据
    notification_data = {
        'id': discussion.id,
        'course_id': discussion.course_id,
        'title': discussion.title,
        'description': discussion.description,
        'topic_content': discussion.topic_content,
        'image_url': discussion.image_url,
        'created_at': discussion.created_at.isoformat() if discussion.created_at else None,
        'end_time': discussion.end_time.isoformat() if discussion.end_time else None,
        'remaining_time': discussion.remaining_time,
        'status': discussion.status
    }
    
    # 发送给课程所有学生
    socketio.emit('group_discussion', notification_data, room=f'course_{course_id}')

def convert_quill_to_html(content_json):
    try:
        content = json.loads(content_json)
        html = ""
        for op in content.get('ops', []):
            text = op.get('insert', '').replace('\n', '<br>')
            html += f'<p>{text}</p>'
        return html
    except (json.JSONDecodeError, TypeError):
        return content_json

@interaction.route('/questions')
@login_required
def questions():
    """习题列表"""
    # 获取当前用户创建的所有习题
    user_questions = Question.query.filter_by(created_by=current_user.id).order_by(Question.created_at.desc()).all()
    for q in user_questions:
        q.content = convert_quill_to_html(q.content)
    return render_template('interaction/questions.html', questions=user_questions)

@interaction.route('/questions/create', methods=['GET', 'POST'])
@login_required
def create_question():
    """创建习题"""
    if request.method == 'POST':
        try:
            # 获取表单数据
            question_type = request.form.get('type')
            content = request.form.get('content')
            score = float(request.form.get('score', 1.0))
            
            # 根据题型处理选项和答案
            options = None
            answer = None
            
            if question_type in ['single', 'multiple']:
                options_str = request.form.get('options')
                if options_str:
                    options = json.loads(options_str)
                
                answer_str = request.form.get('answer')
                if answer_str:
                    answer = json.loads(answer_str)
            
            elif question_type == 'truefalse':
                answer_str = request.form.get('answer')
                if answer_str:
                    answer = json.loads(answer_str)
            
            elif question_type == 'fillblank':
                answer_str = request.form.get('answer')
                if answer_str:
                    answer = json.loads(answer_str)
            
            elif question_type == 'subjective':
                answer = request.form.get('subjective-answer')
            
            # 创建新习题
            new_question = Question(
                type=question_type,
                content=content,
                options=options,
                answer=answer,
                score=score,
                created_by=current_user.id
            )
            
            db.session.add(new_question)
            db.session.commit()
            
            flash('习题创建成功！', 'success')
            return jsonify({'success': True, 'redirect': url_for('interaction.questions')})
        
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)})
    
    return render_template('interaction/create_question.html')

@interaction.route('/questions/<int:question_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_question(question_id):
    """编辑习题"""
    # 获取要编辑的习题
    question = Question.query.get_or_404(question_id)
    
    # 检查权限
    if question.created_by != current_user.id:
        flash('您没有权限编辑此习题', 'danger')
        return redirect(url_for('interaction.questions'))
    
    if request.method == 'POST':
        try:
            # 获取表单数据
            question_type = request.form.get('type')
            content = request.form.get('content')
            score = float(request.form.get('score', 1.0))
            
            # 根据题型处理选项和答案
            options = None
            answer = None
            
            if question_type in ['single', 'multiple']:
                options_str = request.form.get('options')
                if options_str:
                    options = json.loads(options_str)
                
                answer_str = request.form.get('answer')
                if answer_str:
                    answer = json.loads(answer_str)
            
            elif question_type == 'truefalse':
                answer_str = request.form.get('answer')
                if answer_str:
                    answer = json.loads(answer_str)
            
            elif question_type == 'fillblank':
                answer_str = request.form.get('answer')
                if answer_str:
                    answer = json.loads(answer_str)
            
            elif question_type == 'subjective':
                answer = request.form.get('subjective-answer')
            
            # 更新习题
            question.type = question_type
            question.content = content
            question.options = options
            question.answer = answer
            question.score = score
            
            db.session.commit()
            
            flash('习题更新成功！', 'success')
            return jsonify({'success': True, 'redirect': url_for('interaction.questions')})
        
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)})
    
    return render_template('interaction/edit_question.html', question=question)

@interaction.route('/questions/<int:question_id>/delete')
@login_required
def delete_question(question_id):
    """删除习题"""
    question = Question.query.get_or_404(question_id)
    
    # 检查权限
    if question.created_by != current_user.id:
        flash('您没有权限删除此习题', 'danger')
        return redirect(url_for('interaction.questions'))
    
    try:
        db.session.delete(question)
        db.session.commit()
        flash('习题已删除', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除失败: {str(e)}', 'danger')
    
    return redirect(url_for('interaction.questions'))

@interaction.route('/exams')
@login_required
def exams():
    """试卷列表"""
    # 获取当前用户创建的所有试卷
    user_exams = Exam.query.filter_by(created_by=current_user.id).order_by(Exam.created_at.desc()).all()
    return render_template('interaction/exams.html', exams=user_exams)

@interaction.route('/exams/create', methods=['GET', 'POST'])
@login_required
def create_exam():
    """创建试卷"""
    if request.method == 'POST':
        try:
            # 获取JSON数据
            data = request.get_json()
            
            # 创建新试卷
            new_exam = Exam(
                name=data['name'],
                time_limit=data['time_limit'],
                total_score=data['total_score'],
                created_by=current_user.id
            )
            
            db.session.add(new_exam)
            db.session.flush()  # 获取新试卷ID
            
            # 添加试卷题目关联
            for question_data in data['questions']:
                exam_question = ExamQuestion(
                    exam_id=new_exam.id,
                    question_id=question_data['id'],
                    order=question_data['order']
                )
                db.session.add(exam_question)
            
            db.session.commit()
            
            flash('试卷创建成功！', 'success')
            return jsonify({'success': True, 'redirect': url_for('interaction.exams')})
        
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)})
    
    return render_template('interaction/create_exam.html')

@interaction.route('/exams/<int:exam_id>')
@login_required
def view_exam(exam_id):
    """查看试卷"""
    exam = Exam.query.get_or_404(exam_id)
    
    # 检查权限
    if exam.created_by != current_user.id:
        flash('您没有权限查看此试卷', 'danger')
        return redirect(url_for('interaction.exams'))
    
    # 获取试卷题目，按顺序排列
    exam_questions = ExamQuestion.query.filter_by(exam_id=exam_id).order_by(ExamQuestion.order).all()
    
    # 定义渲染题目内容的函数
    def render_question_content(content_json):
        try:
            content = json.loads(content_json)
            # 简单处理，实际应用中可能需要更复杂的渲染
            return ''.join([op.get('insert', '') for op in content.get('ops', [])])
        except:
            return content_json or ''
    
    return render_template('interaction/view_exam.html', 
                          exam=exam, 
                          exam_questions=exam_questions,
                          render_question_content=render_question_content,
                          chr=chr)

@interaction.route('/exams/<int:exam_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_exam(exam_id):
    """编辑试卷"""
    exam = Exam.query.get_or_404(exam_id)
    
    # 检查权限
    if exam.created_by != current_user.id:
        flash('您没有权限编辑此试卷', 'danger')
        return redirect(url_for('interaction.exams'))
    
    if request.method == 'POST':
        try:
            # 获取JSON数据
            data = request.get_json()
            
            # 更新试卷信息
            exam.name = data['name']
            exam.time_limit = data['time_limit']
            exam.total_score = data['total_score']
            
            # 删除现有的试卷题目关联
            ExamQuestion.query.filter_by(exam_id=exam_id).delete()
            
            # 添加新的试卷题目关联
            for question_data in data['questions']:
                exam_question = ExamQuestion(
                    exam_id=exam_id,
                    question_id=question_data['id'],
                    order=question_data['order']
                )
                db.session.add(exam_question)
            
            db.session.commit()
            
            flash('试卷更新成功！', 'success')
            return jsonify({'success': True, 'redirect': url_for('interaction.exams')})
        
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)})
    
    # 获取试卷题目，按顺序排列
    exam_question_records = ExamQuestion.query.filter_by(exam_id=exam_id).order_by(ExamQuestion.order).all()
    
    # 转换为可序列化的格式
    exam_questions_data = [
        {
            'question_id': eq.question_id,
            'order': eq.order
        }
        for eq in exam_question_records
    ]
    
    return render_template('interaction/edit_exam.html', exam=exam, exam_questions=exam_questions_data)

@interaction.route('/exams/<int:exam_id>/delete')
@login_required
def delete_exam(exam_id):
    """删除试卷"""
    exam = Exam.query.get_or_404(exam_id)
    
    # 检查权限
    if exam.created_by != current_user.id:
        flash('您没有权限删除此试卷', 'danger')
        return redirect(url_for('interaction.exams'))
    
    try:
        # 删除试卷题目关联
        ExamQuestion.query.filter_by(exam_id=exam_id).delete()
        
        # 删除试卷
        db.session.delete(exam)
        db.session.commit()
        
        flash('试卷已删除', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除失败: {str(e)}', 'danger')
    
    return redirect(url_for('interaction.exams'))

@interaction.route('/group-discussion', methods=['GET', 'POST'])
@login_required
def group_discussion():
    """分组讨论功能"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        flash('只有教师可以发起分组讨论', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取教师的课程
    courses = Course.query.filter_by(teacher_id=current_user.id).all()
    
    # 获取当前活跃的讨论
    active_discussions = GroupDiscussion.query.filter_by(
        created_by=current_user.id,
        status='active'
    ).all()
    
    # 过滤掉已经结束的讨论（根据end_time）
    now = datetime.utcnow()
    active_discussions = [d for d in active_discussions if d.end_time is None or d.end_time > now]
    
    # 获取历史讨论
    past_discussions = GroupDiscussion.query.filter_by(
        created_by=current_user.id,
        status='ended'
    ).order_by(GroupDiscussion.created_at.desc()).limit(10).all()
    
    if request.method == 'POST':
        try:
            # 获取表单数据
            course_id = request.form.get('course_id')
            title = request.form.get('title')
            description = request.form.get('description')
            duration = int(request.form.get('duration', 10))  # 默认10分钟
            topic_type = request.form.get('topic_type')  # 'text' 或 'screenshot'
            
            # 验证课程ID
            course = Course.query.get(course_id)
            if not course or course.teacher_id != current_user.id:
                flash('无效的课程', 'danger')
                return redirect(url_for('interaction.group_discussion'))
            
            # 创建讨论
            discussion = GroupDiscussion(
                course_id=course_id,
                title=title,
                description=description,
                created_by=current_user.id,
                end_time=datetime.utcnow() + timedelta(minutes=duration)
            )
            
            # 根据主题类型处理
            if topic_type == 'text':
                # 文本主题
                topic_content = request.form.get('topic_content')
                discussion.topic_content = topic_content
            
            elif topic_type == 'screenshot':
                # 截屏主题
                screenshot_data = request.form.get('screenshot_data')
                
                # 保存截屏图片
                if screenshot_data and screenshot_data.startswith('data:image'):
                    # 从base64数据中提取图片数据
                    image_data = screenshot_data.split(',')[1]
                    image_binary = base64.b64decode(image_data)
                    
                    # 生成唯一文件名
                    filename = f"{uuid.uuid4().hex}_screenshot.png"
                    
                    # 确保上传目录存在
                    upload_folder = current_app.config['UPLOAD_FOLDER']
                    screenshot_folder = os.path.join(upload_folder, 'screenshots')
                    if not os.path.exists(screenshot_folder):
                        os.makedirs(screenshot_folder)
                    
                    # 保存图片
                    file_path = os.path.join(screenshot_folder, filename)
                    with open(file_path, 'wb') as f:
                        f.write(image_binary)
                    
                    # 设置图片URL
                    discussion.image_url = f"/static/uploads/screenshots/{filename}"
            
            # 保存讨论
            db.session.add(discussion)
            db.session.commit()
            
            # 记录活动
            record_activity(
                course_id=course_id,
                activity_type='discussion',
                content_id=discussion.id,
                title=f"分组讨论: {title}",
                description=description,
                end_time=discussion.end_time,
                participants_count=CourseStudent.query.filter_by(course_id=course_id).count()
            )
            
            # 通过WebSocket通知学生
            notify_students_group_discussion(discussion.id, course_id)
            
            flash('讨论已发起！', 'success')
            return redirect(url_for('interaction.group_discussion'))
        
        except Exception as e:
            db.session.rollback()
            flash(f'发起讨论失败: {str(e)}', 'danger')
            return redirect(url_for('interaction.group_discussion'))
    
    return render_template('interaction/group_discussion.html', 
                          courses=courses, 
                          active_discussions=active_discussions,
                          past_discussions=past_discussions)

@interaction.route('/end-discussion/<int:discussion_id>', methods=['POST'])
@login_required
def end_discussion(discussion_id):
    """结束讨论"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        return jsonify({'success': False, 'message': '只有教师可以结束讨论'})
    
    # 获取讨论
    discussion = GroupDiscussion.query.get_or_404(discussion_id)
    
    # 检查权限
    if discussion.created_by != current_user.id:
        return jsonify({'success': False, 'message': '您没有权限结束此讨论'})
    
    try:
        # 更新讨论状态
        discussion.status = 'ended'
        db.session.commit()
        
        # 更新活动记录
        activity = ClassActivity.query.filter_by(
            course_id=discussion.course_id,
            type='discussion',
            content_id=discussion_id
        ).first()
        
        if activity:
            activity.status = 'ended'
            activity.end_time = datetime.utcnow()
            db.session.commit()
        
        # 通过WebSocket通知学生讨论已结束
        notify_students_discussion_ended(discussion_id, discussion.course_id)
        
        return jsonify({'success': True, 'message': '讨论已结束'})
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@interaction.route('/student-discussion')
@login_required
def student_discussion():
    """学生查看讨论"""
    # 检查用户是否为学生
    if not current_user.is_student():
        flash('此页面仅供学生访问', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取学生加入的课程
    enrolled_courses = CourseStudent.query.filter_by(student_id=current_user.id).all()
    course_ids = [cs.course_id for cs in enrolled_courses]
    
    # 获取这些课程中的活跃讨论
    now = datetime.utcnow()
    active_discussions = GroupDiscussion.query.filter(
        GroupDiscussion.course_id.in_(course_ids),
        GroupDiscussion.status == 'active'
    ).all()
    
    # 过滤掉已经结束的讨论（根据end_time）
    active_discussions = [d for d in active_discussions if d.end_time is None or d.end_time > now]
    
    # 获取历史讨论
    past_discussions = GroupDiscussion.query.filter(
        GroupDiscussion.course_id.in_(course_ids),
        GroupDiscussion.status == 'ended'
    ).order_by(GroupDiscussion.created_at.desc()).limit(10).all()
    
    return render_template('interaction/student_discussion.html',
                          active_discussions=active_discussions,
                          past_discussions=past_discussions)

@interaction.route('/student-score', methods=['GET', 'POST'])
@login_required
def student_score():
    """学生评分功能"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        flash('只有教师可以使用评分功能', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取教师的课程
    courses = Course.query.filter_by(teacher_id=current_user.id).all()
    
    if request.method == 'POST':
        try:
            # 获取表单数据
            course_id = request.form.get('course_id')
            student_id = request.form.get('student_id')
            score_value = float(request.form.get('score'))
            reason = request.form.get('reason', '')
            
            # 验证课程ID
            course = Course.query.get(course_id)
            if not course or course.teacher_id != current_user.id:
                flash('无效的课程', 'danger')
                return redirect(url_for('interaction.student_score'))
            
            # 验证学生ID
            student = User.query.get(student_id)
            if not student or not student.is_student():
                flash('无效的学生', 'danger')
                return redirect(url_for('interaction.student_score'))
            
            # 验证学生是否在该课程中
            if not course.has_student(student_id):
                flash('该学生未加入此课程', 'danger')
                return redirect(url_for('interaction.student_score'))
            
            # 创建评分记录
            score = Score(
                course_id=course_id,
                target_type='student',
                target_id=student_id,
                score=score_value,
                reason=reason,
                created_by=current_user.id
            )
            
            db.session.add(score)
            db.session.commit()
            
            # 记录活动
            record_activity(
                course_id=course_id,
                activity_type='score',
                content_id=score.id,
                title=f"学生评分: {student.username}",
                description=f"分数: {score_value}, 原因: {reason}" if reason else f"分数: {score_value}"
            )
            
            flash(f'已成功为学生 {student.username} 评分 {score_value} 分', 'success')
            
            # 如果是AJAX请求，返回JSON响应
            if request.is_json:
                return jsonify({
                    'success': True,
                    'message': f'已成功为学生 {student.username} 评分 {score_value} 分'
                })
            
            return redirect(url_for('interaction.student_score'))
        
        except Exception as e:
            db.session.rollback()
            flash(f'评分失败: {str(e)}', 'danger')
            
            # 如果是AJAX请求，返回JSON响应
            if request.is_json:
                return jsonify({
                    'success': False,
                    'message': f'评分失败: {str(e)}'
                })
            
            return redirect(url_for('interaction.student_score'))
    
    # 获取课程学生
    course_students = {}
    for course in courses:
        students = []
        course_student_records = CourseStudent.query.filter_by(course_id=course.id).all()
        for cs in course_student_records:
            student = User.query.get(cs.student_id)
            if student:
                students.append(student.to_dict())
        course_students[course.id] = students
    
    # 获取最近的评分记录
    recent_scores_query = Score.query.filter_by(
        created_by=current_user.id,
        target_type='student'
    ).order_by(Score.created_at.desc()).limit(10).all()

    recent_scores = []
    for score in recent_scores_query:
        score_dict = score.to_dict()
        student = User.query.get(score.target_id)
        if student:
            score_dict['student'] = student.to_dict()
        recent_scores.append(score_dict)
    
    return render_template('interaction/student_score.html',
                          courses=courses,
                          course_students=course_students,
                          recent_scores=recent_scores)

@interaction.route('/group-score', methods=['GET', 'POST'])
@login_required
def group_score():
    """小组评分功能"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        flash('只有教师可以使用评分功能', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取教师的课程
    courses = Course.query.filter_by(teacher_id=current_user.id).all()
    
    if request.method == 'POST':
        try:
            # 获取表单数据
            course_id = request.form.get('course_id')
            group_id = request.form.get('group_id')
            score_value = float(request.form.get('score'))
            reason = request.form.get('reason', '')
            
            # 验证课程ID
            course = Course.query.get(course_id)
            if not course or course.teacher_id != current_user.id:
                flash('无效的课程', 'danger')
                return redirect(url_for('interaction.group_score'))
            
            # 验证小组ID
            group = Group.query.get(group_id)
            if not group or group.course_id != int(course_id):
                flash('无效的小组', 'danger')
                return redirect(url_for('interaction.group_score'))
            
            # 创建评分记录
            score = Score(
                course_id=course_id,
                target_type='group',
                target_id=group_id,
                score=score_value,
                reason=reason,
                created_by=current_user.id
            )
            
            db.session.add(score)
            db.session.commit()
            
            # 记录活动
            record_activity(
                course_id=course_id,
                activity_type='score',
                content_id=score.id,
                title=f"小组评分: {group.name}",
                description=f"分数: {score_value}, 原因: {reason}" if reason else f"分数: {score_value}"
            )
            
            flash(f'已成功为小组 {group.name} 评分 {score_value} 分', 'success')
            
            # 如果是AJAX请求，返回JSON响应
            if request.is_json:
                return jsonify({
                    'success': True,
                    'message': f'已成功为小组 {group.name} 评分 {score_value} 分'
                })
            
            return redirect(url_for('interaction.group_score'))
        
        except Exception as e:
            db.session.rollback()
            flash(f'评分失败: {str(e)}', 'danger')
            
            # 如果是AJAX请求，返回JSON响应
            if request.is_json:
                return jsonify({
                    'success': False,
                    'message': f'评分失败: {str(e)}'
                })
            
            return redirect(url_for('interaction.group_score'))
    
    # 获取课程小组
    course_groups = {}
    for course in courses:
        groups = Group.query.filter_by(course_id=course.id).all()
        course_groups[course.id] = [group.to_dict() for group in groups]
    
    # 获取最近的评分记录
    recent_scores_query = Score.query.filter_by(
        created_by=current_user.id,
        target_type='group'
    ).order_by(Score.created_at.desc()).limit(10).all()
    
    # 获取小组信息
    recent_scores = []
    for score in recent_scores_query:
        score_dict = score.to_dict()
        score_dict['group'] = Group.query.get(score.target_id).to_dict()
        recent_scores.append(score_dict)
    
    return render_template('interaction/group_score.html',
                          courses=courses,
                          course_groups=course_groups,
                          recent_scores=recent_scores)

@interaction.route('/add-pick-score', methods=['POST'])
@login_required
def add_pick_score():
    """随机点名后评分功能"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        flash('只有教师可以使用评分功能', 'danger')
        return redirect(url_for('main.index'))
    
    try:
        # 获取表单数据
        record_id = request.form.get('record_id')
        course_id = request.form.get('course_id')
        pick_type = request.form.get('pick_type')
        target_id = request.form.get('target_id')
        score_value = float(request.form.get('score'))
        note = request.form.get('note', '')
        
        # 验证点名记录
        from app.models.interaction import RandomPickRecord
        record = RandomPickRecord.query.get(record_id)
        if not record:
            flash('无效的点名记录', 'danger')
            return redirect(url_for('interaction.random_pick'))
        
        # 验证课程ID
        course = Course.query.get(course_id)
        if not course or course.teacher_id != current_user.id:
            flash('无效的课程', 'danger')
            return redirect(url_for('interaction.random_pick'))
        
        # 更新点名记录的评分信息
        record.score = score_value
        record.note = note
        record.scored_at = datetime.utcnow()
        record.scored_by = current_user.id
        
        # 同时创建评分记录
        from app.models.score import Score
        score = Score(
            course_id=course_id,
            target_type=pick_type,
            target_id=target_id,
            score=score_value,
            reason=note,
            created_by=current_user.id
        )
        
        db.session.add(score)
        db.session.commit()
        
        # 获取目标名称
        target_name = ""
        if pick_type == 'student':
            student = User.query.get(target_id)
            if student:
                target_name = student.username
        else:
            group = Group.query.get(target_id)
            if group:
                target_name = group.name
        
        flash(f'已成功为{pick_type == "student" and "学生" or "小组"} {target_name} 评分 {score_value} 分', 'success')
        return redirect(url_for('interaction.random_pick'))
    
    except Exception as e:
        db.session.rollback()
        flash(f'评分失败: {str(e)}', 'danger')
        return redirect(url_for('interaction.random_pick'))

@interaction.route('/api/group-score', methods=['POST'])
@login_required
def api_group_score():
    """小组评分API"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        return jsonify({
            'success': False,
            'message': '只有教师可以使用评分功能'
        })
    
    try:
        # 获取JSON数据
        data = request.get_json()
        
        course_id = data.get('course_id')
        group_id = data.get('group_id')
        score_value = float(data.get('score'))
        reason = data.get('reason', '')
        
        # 验证课程ID
        course = Course.query.get(course_id)
        if not course or course.teacher_id != current_user.id:
            return jsonify({
                'success': False,
                'message': '无效的课程'
            })
        
        # 验证小组ID
        group = Group.query.get(group_id)
        if not group or group.course_id != int(course_id):
            return jsonify({
                'success': False,
                'message': '无效的小组'
            })
        
        # 创建评分记录
        score = Score(
            course_id=course_id,
            target_type='group',
            target_id=group_id,
            score=score_value,
            reason=reason,
            created_by=current_user.id
        )
        
        db.session.add(score)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'已成功为小组 {group.name} 评分 {score_value} 分',
            'score': score.to_dict()
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'评分失败: {str(e)}'
        })

@interaction.route('/api/student-score', methods=['POST'])
@login_required
def api_student_score():
    """学生评分API"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        return jsonify({
            'success': False,
            'message': '只有教师可以使用评分功能'
        })
    
    try:
        # 获取JSON数据
        data = request.get_json()
        
        course_id = data.get('course_id')
        student_id = data.get('student_id')
        score_value = float(data.get('score'))
        reason = data.get('reason', '')
        
        # 验证课程ID
        course = Course.query.get(course_id)
        if not course or course.teacher_id != current_user.id:
            return jsonify({
                'success': False,
                'message': '无效的课程'
            })
        
        # 验证学生ID
        student = User.query.get(student_id)
        if not student or not student.is_student():
            return jsonify({
                'success': False,
                'message': '无效的学生'
            })
        
        # 验证学生是否在该课程中
        if not course.has_student(student_id):
            return jsonify({
                'success': False,
                'message': '该学生未加入此课程'
            })
        
        # 创建评分记录
        score = Score(
            course_id=course_id,
            target_type='student',
            target_id=student_id,
            score=score_value,
            reason=reason,
            created_by=current_user.id
        )
        
        db.session.add(score)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'已成功为学生 {student.username} 评分 {score_value} 分',
            'score': score.to_dict()
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'评分失败: {str(e)}'
        })

@interaction.route('/random-pick', methods=['GET', 'POST'])
@login_required
def random_pick():
    """随机点名功能"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        flash('只有教师可以使用随机点名功能', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取教师的课程
    courses = Course.query.filter_by(teacher_id=current_user.id).all()
    
    if request.method == 'POST':
        try:
            # 获取表单数据
            course_id = request.form.get('course_id')
            pick_type = request.form.get('pick_type', 'student')  # 'student' 或 'group'
            exclude_picked = 'exclude_picked' in request.form
            
            # 验证课程ID
            course = Course.query.get(course_id)
            if not course or course.teacher_id != current_user.id:
                flash('无效的课程', 'danger')
                return redirect(url_for('interaction.random_pick'))
            
            # 根据选择类型进行随机点名
            if pick_type == 'student':
                # 获取课程学生
                course_students = CourseStudent.query.filter_by(course_id=course_id).all()
                
                if not course_students:
                    flash('该课程没有学生', 'warning')
                    return redirect(url_for('interaction.random_pick'))
                
                # 如果需要排除已点过的学生
                if exclude_picked:
                    # 获取已点过的学生ID列表（这里可以根据实际需求实现）
                    # 这里简单实现，实际应用中可能需要更复杂的逻辑
                    from app.models.interaction import RandomPickRecord
                    picked_records = RandomPickRecord.query.filter_by(
                        course_id=course_id,
                        pick_type='student'
                    ).order_by(RandomPickRecord.picked_at.desc()).all()
                    
                    picked_student_ids = [record.target_id for record in picked_records]
                    
                    # 过滤掉已点过的学生
                    available_students = [cs for cs in course_students if cs.student_id not in picked_student_ids]
                    
                    # 如果所有学生都已点过，则重置
                    if not available_students:
                        available_students = course_students
                else:
                    available_students = course_students
                
                # 随机选择一名学生
                import random
                picked_student = random.choice(available_students)
                
                # 记录点名结果
                from app.models.interaction import RandomPickRecord
                record = RandomPickRecord(
                    course_id=course_id,
                    pick_type='student',
                    target_id=picked_student.student_id,
                    picked_at=datetime.utcnow()
                )
                db.session.add(record)
                db.session.commit()
                
                # 获取学生信息
                student = User.query.get(picked_student.student_id)
                
                # 返回点名结果
                return render_template('interaction/random_pick.html',
                                      courses=courses,
                                      picked=True,
                                      pick_type='student',
                                      picked_student=student,
                                      course=course,
                                      record_id=record.id)
            
            elif pick_type == 'group':
                # 获取课程小组
                groups = Group.query.filter_by(course_id=course_id).all()
                
                if not groups:
                    flash('该课程没有小组', 'warning')
                    return redirect(url_for('interaction.random_pick'))
                
                # 如果需要排除已点过的小组
                if exclude_picked:
                    # 获取已点过的小组ID列表
                    from app.models.interaction import RandomPickRecord
                    picked_records = RandomPickRecord.query.filter_by(
                        course_id=course_id,
                        pick_type='group'
                    ).order_by(RandomPickRecord.picked_at.desc()).all()
                    
                    picked_group_ids = [record.target_id for record in picked_records]
                    
                    # 过滤掉已点过的小组
                    available_groups = [g for g in groups if g.id not in picked_group_ids]
                    
                    # 如果所有小组都已点过，则重置
                    if not available_groups:
                        available_groups = groups
                else:
                    available_groups = groups
                
                # 随机选择一个小组
                import random
                picked_group = random.choice(available_groups)
                
                # 记录点名结果
                from app.models.interaction import RandomPickRecord
                record = RandomPickRecord(
                    course_id=course_id,
                    pick_type='group',
                    target_id=picked_group.id,
                    picked_at=datetime.utcnow()
                )
                db.session.add(record)
                db.session.commit()
                
                # 获取小组成员
                group_members = GroupMember.query.filter_by(group_id=picked_group.id).all()
                members = []
                for member in group_members:
                    student = User.query.get(member.student_id)
                    if student:
                        members.append(student)
                
                # 返回点名结果
                return render_template('interaction/random_pick.html',
                                      courses=courses,
                                      picked=True,
                                      pick_type='group',
                                      picked_group=picked_group,
                                      group_members=members,
                                      course=course,
                                      record_id=record.id)
        
        except Exception as e:
            db.session.rollback()
            flash(f'随机点名失败: {str(e)}', 'danger')
            return redirect(url_for('interaction.random_pick'))
    
    # 获取最近的点名记录
    from app.models.interaction import RandomPickRecord
    recent_records = []
    for course in courses:
        records = RandomPickRecord.query.filter_by(course_id=course.id).order_by(RandomPickRecord.picked_at.desc()).limit(5).all()
        for record in records:
            record_data = {
                'course': course,
                'pick_type': record.pick_type,
                'picked_at': record.picked_at
            }
            
            if record.pick_type == 'student':
                student = User.query.get(record.target_id)
                record_data['target'] = student
            else:
                group = Group.query.get(record.target_id)
                record_data['target'] = group
            
            recent_records.append(record_data)
    
    return render_template('interaction/random_pick.html', 
                          courses=courses,
                          picked=False,
                          recent_records=recent_records)

@interaction.route('/api/questions')
@login_required
def api_questions():
    """获取题目列表API"""
    # 获取当前用户创建的所有习题
    questions = Question.query.filter_by(created_by=current_user.id).order_by(Question.created_at.desc()).all()
    
    # 转换为JSON格式
    questions_data = []
    for question in questions:
        questions_data.append({
            'id': question.id,
            'type': question.type,
            'content': question.content,
            'options': question.options,
            'score': question.score,
            'created_at': question.created_at.isoformat() if question.created_at else None
        })
    
    return jsonify({'success': True, 'questions': questions_data})

@interaction.route('/send-question', methods=['GET', 'POST'])
@login_required
def send_question():
    """发送题目"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        flash('只有教师可以发送题目', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取教师的课程
    courses = Course.query.filter_by(teacher_id=current_user.id).all()
    
    # 获取教师创建的习题
    questions = Question.query.filter_by(created_by=current_user.id).all()
    
    # 获取教师创建的试卷
    exams = Exam.query.filter_by(created_by=current_user.id).all()
    
    if request.method == 'POST':
        try:
            # 获取表单数据
            course_id = request.form.get('course_id')
            question_type = request.form.get('question_type')  # 'existing', 'temporary', 'screenshot'
            title = request.form.get('title', '课堂题目')
            end_time_str = request.form.get('end_time')
            
            # 验证课程ID
            course = Course.query.get(course_id)
            if not course or course.teacher_id != current_user.id:
                flash('无效的课程', 'danger')
                return redirect(url_for('interaction.send_question'))
            
            # 创建题目会话
            session = QuestionSession(
                title=title,
                course_id=course_id,
                teacher_id=current_user.id
            )
            
            # 设置答题截止时间（如果有）
            if end_time_str:
                try:
                    # 解析日期时间
                    end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
                    session.end_time = end_time
                except:
                    pass
            
            db.session.add(session)
            db.session.flush()  # 获取会话ID
            
            # 根据题目类型处理
            if question_type == 'existing':
                # 从现有题库选择题目
                question_ids = request.form.getlist('question_ids')
                
                # 添加题目到会话
                for i, question_id in enumerate(question_ids):
                    session_question = SessionQuestion(
                        session_id=session.id,
                        question_id=question_id,
                        order=i + 1
                    )
                    db.session.add(session_question)
            
            elif question_type == 'temporary':
                # 临时创建题目
                temp_question_type = request.form.get('temp_question_type')
                content = request.form.get('content')
                score = float(request.form.get('score', 1.0))
                
                # 根据题型处理选项和答案
                options = None
                answer = None
                
                if temp_question_type in ['single', 'multiple']:
                    options_str = request.form.get('options')
                    if options_str:
                        options = json.loads(options_str)
                    
                    answer_str = request.form.get('answer')
                    if answer_str:
                        answer = json.loads(answer_str)
                
                elif temp_question_type == 'truefalse':
                    answer_str = request.form.get('answer')
                    if answer_str:
                        answer = json.loads(answer_str)
                
                elif temp_question_type == 'fillblank':
                    answer_str = request.form.get('answer')
                    if answer_str:
                        answer = json.loads(answer_str)
                
                elif temp_question_type == 'subjective':
                    answer = request.form.get('subjective-answer')
                
                # 创建临时题目
                temp_question = TemporaryQuestion(
                    session_id=session.id,
                    type=temp_question_type,
                    content=content,
                    options=options,
                    answer=answer,
                    score=score,
                    created_by=current_user.id
                )
                
                db.session.add(temp_question)
            
            elif question_type == 'screenshot':
                # 截屏作为题目
                screenshot_data = request.form.get('screenshot_data')
                description = request.form.get('description')
                answer = request.form.get('answer')
                score = float(request.form.get('score', 1.0))
                
                # 保存截屏图片
                if screenshot_data and screenshot_data.startswith('data:image'):
                    # 从base64数据中提取图片数据
                    image_data = screenshot_data.split(',')[1]
                    image_binary = base64.b64decode(image_data)
                    
                    # 生成唯一文件名
                    import uuid
                    filename = f"{uuid.uuid4().hex}_screenshot.png"
                    
                    # 确保上传目录存在
                    from flask import current_app
                    upload_folder = current_app.config['UPLOAD_FOLDER']
                    screenshot_folder = os.path.join(upload_folder, 'screenshots')
                    if not os.path.exists(screenshot_folder):
                        os.makedirs(screenshot_folder)
                    
                    # 保存图片
                    file_path = os.path.join(screenshot_folder, filename)
                    with open(file_path, 'wb') as f:
                        f.write(image_binary)
                    
                    # 创建截屏题目
                    screenshot_question = ScreenshotQuestion(
                        session_id=session.id,
                        image_url=f"/static/uploads/screenshots/{filename}",
                        description=description,
                        answer=answer,
                        score=score,
                        created_by=current_user.id
                    )
                    
                    db.session.add(screenshot_question)
            
            # 提交数据库事务
            db.session.commit()
            
            # 通过WebSocket通知学生
            notify_students_new_question(session.id, course_id)
            
            flash('题目发送成功！', 'success')
            return redirect(url_for('interaction.question_sessions'))
        
        except Exception as e:
            db.session.rollback()
            flash(f'发送题目失败: {str(e)}', 'danger')
            return redirect(url_for('interaction.send_question'))
    
    return render_template('interaction/send_question.html', 
                          courses=courses, 
                          questions=questions, 
                          exams=exams)

@interaction.route('/question-sessions')
@login_required
def question_sessions():
    """题目会话列表"""
    # 获取当前用户创建的所有题目会话
    if current_user.is_teacher():
        sessions = QuestionSession.query.filter_by(teacher_id=current_user.id).order_by(QuestionSession.created_at.desc()).all()
    else:
        # 学生查看参与的会话
        enrolled_courses = CourseStudent.query.filter_by(student_id=current_user.id).all()
        course_ids = [cs.course_id for cs in enrolled_courses]
        sessions = QuestionSession.query.filter(QuestionSession.course_id.in_(course_ids)).order_by(QuestionSession.created_at.desc()).all()
    
    return render_template('interaction/question_sessions.html', sessions=sessions)

@interaction.route('/question-sessions/<int:session_id>')
@login_required
def view_question_session(session_id):
    """查看题目会话"""
    session = QuestionSession.query.get_or_404(session_id)
    
    # 检查权限
    if current_user.is_teacher():
        if session.teacher_id != current_user.id:
            flash('您没有权限查看此题目会话', 'danger')
            return redirect(url_for('interaction.question_sessions'))
    else:
        # 学生检查是否在课程中
        course_student = CourseStudent.query.filter_by(
            student_id=current_user.id,
            course_id=session.course_id
        ).first()
        
        if not course_student:
            flash('您没有权限查看此题目会话', 'danger')
            return redirect(url_for('interaction.question_sessions'))
    
    # 获取会话中的题目
    session_questions = SessionQuestion.query.filter_by(session_id=session_id).order_by(SessionQuestion.order).all()
    temporary_questions = TemporaryQuestion.query.filter_by(session_id=session_id).all()
    screenshot_questions = ScreenshotQuestion.query.filter_by(session_id=session_id).all()
    
    # 获取学生答案
    if current_user.is_student():
        student_answers = StudentAnswer.query.filter_by(student_id=current_user.id).all()
        student_answers_dict = {}
        
        for answer in student_answers:
            if answer.session_question_id:
                student_answers_dict[f'sq_{answer.session_question_id}'] = answer
            elif answer.temporary_question_id:
                student_answers_dict[f'tq_{answer.temporary_question_id}'] = answer
            elif answer.screenshot_question_id:
                student_answers_dict[f'scq_{answer.screenshot_question_id}'] = answer
    else:
        student_answers_dict = None
    
    return render_template('interaction/view_question_session.html',
                          session=session,
                          session_questions=session_questions,
                          temporary_questions=temporary_questions,
                          screenshot_questions=screenshot_questions,
                          student_answers=student_answers_dict)

@interaction.route('/question-sessions/<int:session_id>/submit-answer', methods=['POST'])
@login_required
def submit_answer(session_id):
    """提交答案"""
    # 检查用户是否为学生
    if not current_user.is_student():
        return jsonify({'success': False, 'message': '只有学生可以提交答案'})
    
    session = QuestionSession.query.get_or_404(session_id)
    
    # 检查会话是否已结束
    if session.status == 'ended' or (session.end_time and session.end_time < datetime.utcnow()):
        return jsonify({'success': False, 'message': '答题已结束'})
    
    # 检查学生是否在课程中
    course_student = CourseStudent.query.filter_by(
        student_id=current_user.id,
        course_id=session.course_id
    ).first()
    
    if not course_student:
        return jsonify({'success': False, 'message': '您不是此课程的学生'})
    
    try:
        # 获取JSON数据
        data = request.get_json()
        
        question_type = data.get('question_type')  # 'session', 'temporary', 'screenshot'
        question_id = data.get('question_id')
        answer_data = data.get('answer')
        
        # 根据题目类型处理
        if question_type == 'session':
            # 从题库选择的题目
            session_question = SessionQuestion.query.get_or_404(question_id)
            
            # 检查题目是否属于当前会话
            if session_question.session_id != session_id:
                return jsonify({'success': False, 'message': '题目不属于当前会话'})
            
            # 获取题目详情
            question = Question.query.get(session_question.question_id)
            
            # 检查是否已提交过答案
            existing_answer = StudentAnswer.query.filter_by(
                student_id=current_user.id,
                session_question_id=question_id
            ).first()
            
            if existing_answer:
                # 更新答案
                existing_answer.answer = answer_data
                
                # 自动评分（客观题）
                if question.type in ['single', 'multiple', 'truefalse', 'fillblank']:
                    is_correct = (answer_data == question.answer)
                    existing_answer.is_correct = is_correct
                    existing_answer.score = question.score if is_correct else 0
                
                db.session.commit()
            else:
                # 创建新答案
                new_answer = StudentAnswer(
                    student_id=current_user.id,
                    session_question_id=question_id,
                    answer=answer_data
                )
                
                # 自动评分（客观题）
                if question.type in ['single', 'multiple', 'truefalse', 'fillblank']:
                    is_correct = (answer_data == question.answer)
                    new_answer.is_correct = is_correct
                    new_answer.score = question.score if is_correct else 0
                
                db.session.add(new_answer)
                db.session.commit()
        
        elif question_type == 'temporary':
            # 临时创建的题目
            temp_question = TemporaryQuestion.query.get_or_404(question_id)
            
            # 检查题目是否属于当前会话
            if temp_question.session_id != session_id:
                return jsonify({'success': False, 'message': '题目不属于当前会话'})
            
            # 检查是否已提交过答案
            existing_answer = StudentAnswer.query.filter_by(
                student_id=current_user.id,
                temporary_question_id=question_id
            ).first()
            
            if existing_answer:
                # 更新答案
                existing_answer.answer = answer_data
                
                # 自动评分（客观题）
                if temp_question.type in ['single', 'multiple', 'truefalse', 'fillblank']:
                    is_correct = (answer_data == temp_question.answer)
                    existing_answer.is_correct = is_correct
                    existing_answer.score = temp_question.score if is_correct else 0
                
                db.session.commit()
            else:
                # 创建新答案
                new_answer = StudentAnswer(
                    student_id=current_user.id,
                    temporary_question_id=question_id,
                    answer=answer_data
                )
                
                # 自动评分（客观题）
                if temp_question.type in ['single', 'multiple', 'truefalse', 'fillblank']:
                    is_correct = (answer_data == temp_question.answer)
                    new_answer.is_correct = is_correct
                    new_answer.score = temp_question.score if is_correct else 0
                
                db.session.add(new_answer)
                db.session.commit()
        
        elif question_type == 'screenshot':
            # 截屏题目
            screenshot_question = ScreenshotQuestion.query.get_or_404(question_id)
            
            # 检查题目是否属于当前会话
            if screenshot_question.session_id != session_id:
                return jsonify({'success': False, 'message': '题目不属于当前会话'})
            
            # 检查是否已提交过答案
            existing_answer = StudentAnswer.query.filter_by(
                student_id=current_user.id,
                screenshot_question_id=question_id
            ).first()
            
            if existing_answer:
                # 更新答案
                existing_answer.answer = answer_data
                db.session.commit()
            else:
                # 创建新答案
                new_answer = StudentAnswer(
                    student_id=current_user.id,
                    screenshot_question_id=question_id,
                    answer=answer_data
                )
                
                db.session.add(new_answer)
                db.session.commit()
        
        return jsonify({'success': True, 'message': '答案提交成功'})
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@interaction.route('/question-sessions/<int:session_id>/end', methods=['POST'])
@login_required
def end_question_session(session_id):
    """结束题目会话"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        return jsonify({'success': False, 'message': '只有教师可以结束题目会话'})
    
    session = QuestionSession.query.get_or_404(session_id)
    
    # 检查权限
    if session.teacher_id != current_user.id:
        return jsonify({'success': False, 'message': '您没有权限结束此题目会话'})
    
    try:
        # 更新会话状态
        session.status = 'ended'
        session.end_time = datetime.utcnow()
        
        db.session.commit()
        
        # 通过WebSocket通知学生
        notify_students_session_ended(session_id, session.course_id)
        
        return jsonify({'success': True, 'message': '题目会话已结束'})
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@interaction.route('/question-sessions/<int:session_id>/results')
@login_required
def question_session_results(session_id):
    """查看题目会话结果"""
    session = QuestionSession.query.get_or_404(session_id)
    
    # 检查权限
    if current_user.is_teacher():
        if session.teacher_id != current_user.id:
            flash('您没有权限查看此题目会话结果', 'danger')
            return redirect(url_for('interaction.question_sessions'))
    else:
        # 学生检查是否在课程中
        course_student = CourseStudent.query.filter_by(
            student_id=current_user.id,
            course_id=session.course_id
        ).first()
        
        if not course_student:
            flash('您没有权限查看此题目会话结果', 'danger')
            return redirect(url_for('interaction.question_sessions'))
    
    # 获取会话中的题目
    session_questions = SessionQuestion.query.filter_by(session_id=session_id).order_by(SessionQuestion.order).all()
    temporary_questions = TemporaryQuestion.query.filter_by(session_id=session_id).all()
    screenshot_questions = ScreenshotQuestion.query.filter_by(session_id=session_id).all()
    
    # 获取课程学生
    course_students = CourseStudent.query.filter_by(course_id=session.course_id).all()
    
    # 获取课程小组
    groups = Group.query.filter_by(course_id=session.course_id).all()
    
    # 获取小组成员
    group_members = {}
    for group in groups:
        members = GroupMember.query.filter_by(group_id=group.id).all()
        group_members[group.id] = [member.student_id for member in members]
    
    # 获取所有答案
    all_answers = StudentAnswer.query.filter(
        (StudentAnswer.session_question_id.in_([sq.id for sq in session_questions]) if session_questions else False) |
        (StudentAnswer.temporary_question_id.in_([tq.id for tq in temporary_questions]) if temporary_questions else False) |
        (StudentAnswer.screenshot_question_id.in_([scq.id for scq in screenshot_questions]) if screenshot_questions else False)
    ).all()
    
    # 组织答案数据
    answers_data = {}
    for answer in all_answers:
        student_id = answer.student_id
        
        if student_id not in answers_data:
            answers_data[student_id] = {}
        
        # 自动评分系统 - 如果答案尚未评分，进行自动评分
        if answer.is_correct is None:
            # 根据题目类型进行评分
            if answer.session_question_id:
                question = answer.session_question.question
                if question.type in ['single', 'multiple', 'truefalse']:
                    # 简单比较答案是否相等
                    is_correct = (answer.answer == question.answer)
                    answer.is_correct = is_correct
                    answer.score = question.score if is_correct else 0
                elif question.type == 'fillblank':
                    # 填空题需要更灵活的比较
                    correct_answers = question.answer
                    student_answers = answer.answer
                    
                    # 如果长度不同，肯定不完全正确
                    if len(correct_answers) != len(student_answers):
                        answer.is_correct = False
                        answer.score = 0
                    else:
                        # 检查每个答案是否正确
                        all_correct = True
                        for i, correct in enumerate(correct_answers):
                            if i >= len(student_answers) or student_answers[i].strip().lower() != correct.strip().lower():
                                all_correct = False
                                break
                        
                        answer.is_correct = all_correct
                        answer.score = question.score if all_correct else 0
            
            elif answer.temporary_question_id:
                question = answer.temporary_question
                if question.type in ['single', 'multiple', 'truefalse']:
                    is_correct = (answer.answer == question.answer)
                    answer.is_correct = is_correct
                    answer.score = question.score if is_correct else 0
                elif question.type == 'fillblank':
                    correct_answers = question.answer
                    student_answers = answer.answer
                    
                    if len(correct_answers) != len(student_answers):
                        answer.is_correct = False
                        answer.score = 0
                    else:
                        all_correct = True
                        for i, correct in enumerate(correct_answers):
                            if i >= len(student_answers) or student_answers[i].strip().lower() != correct.strip().lower():
                                all_correct = False
                                break
                        
                        answer.is_correct = all_correct
                        answer.score = question.score if all_correct else 0
            
            # 保存评分结果
            db.session.add(answer)
        
        if answer.session_question_id:
            answers_data[student_id][f'sq_{answer.session_question_id}'] = answer
        elif answer.temporary_question_id:
            answers_data[student_id][f'tq_{answer.temporary_question_id}'] = answer
        elif answer.screenshot_question_id:
            answers_data[student_id][f'scq_{answer.screenshot_question_id}'] = answer
    
    # 提交数据库更改（自动评分）
    db.session.commit()
    
    # 计算统计数据
    # 1. 全班统计
    class_stats = {
        'total_students': len(course_students),
        'answered_students': len(answers_data),
        'participation_rate': len(answers_data) / len(course_students) if course_students else 0,
        'questions': {}
    }
    
    # 2. 分组统计
    group_stats = {}
    for group in groups:
        group_stats[group.id] = {
            'name': group.name,
            'total_students': len(group_members.get(group.id, [])),
            'answered_students': 0,
            'participation_rate': 0,
            'total_answers': 0,
            'correct_answers': 0,
            'accuracy_rate': 0
        }
    
    # 统计每个题目的数据
    for sq in session_questions:
        question_key = f'sq_{sq.id}'
        class_stats['questions'][question_key] = {
            'type': sq.question.type,
            'title': f'题目{sq.order}',
            'total_answers': 0,
            'correct_answers': 0,
            'accuracy_rate': 0
        }
    
    for tq in temporary_questions:
        question_key = f'tq_{tq.id}'
        class_stats['questions'][question_key] = {
            'type': tq.type,
            'title': f'临时题{tq.id}',
            'total_answers': 0,
            'correct_answers': 0,
            'accuracy_rate': 0
        }
    
    for scq in screenshot_questions:
        question_key = f'scq_{scq.id}'
        class_stats['questions'][question_key] = {
            'type': 'screenshot',
            'title': f'截屏题{scq.id}',
            'total_answers': 0,
            'correct_answers': 0,
            'accuracy_rate': 0
        }
    
    # 统计答案数据
    for student_id, student_answers in answers_data.items():
        # 更新小组统计
        for group_id, members in group_members.items():
            if student_id in members:
                group_stats[group_id]['answered_students'] += 1
                
                for answer_key, answer in student_answers.items():
                    group_stats[group_id]['total_answers'] += 1
                    if answer.is_correct:
                        group_stats[group_id]['correct_answers'] += 1
        
        # 更新题目统计
        for answer_key, answer in student_answers.items():
            if answer_key in class_stats['questions']:
                class_stats['questions'][answer_key]['total_answers'] += 1
                if answer.is_correct:
                    class_stats['questions'][answer_key]['correct_answers'] += 1
    
    # 计算准确率
    for question_key, question_stat in class_stats['questions'].items():
        if question_stat['total_answers'] > 0:
            question_stat['accuracy_rate'] = question_stat['correct_answers'] / question_stat['total_answers']
    
    # 计算小组参与率和准确率
    for group_id, stat in group_stats.items():
        if stat['total_students'] > 0:
            stat['participation_rate'] = stat['answered_students'] / stat['total_students']
        if stat['total_answers'] > 0:
            stat['accuracy_rate'] = stat['correct_answers'] / stat['total_answers']
    
    groups = Group.query.filter_by(course_id=session.course_id).all()
    
    # 组织小组成员数据
    group_members = {}
    for group in groups:
        members = GroupMember.query.filter_by(group_id=group.id).all()
        group_members[group.id] = [member.student_id for member in members]
    
    return render_template('interaction/question_session_results.html',
                          session=session,
                          session_questions=session_questions,
                          temporary_questions=temporary_questions,
                          screenshot_questions=screenshot_questions,
                          course_students=course_students,
                          answers_data=answers_data,
                          groups=groups,
                          group_members=group_members)

@interaction.route('/share-file', methods=['GET', 'POST'])
@login_required
def share_file():
    """文件分享功能"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        flash('只有教师可以分享文件', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取教师的课程
    courses = Course.query.filter_by(teacher_id=current_user.id).all()
    
    # 获取教师的资源
    resources = Resource.query.filter_by(owner_id=current_user.id).all()
    
    if request.method == 'POST':
        try:
            # 获取表单数据
            course_id = request.form.get('course_id')
            share_type = request.form.get('share_type')  # 'resource', 'local'
            target_type = request.form.get('target_type')  # 'all', 'group', 'student'
            target_ids = request.form.getlist('target_ids')
            
            # 验证课程ID
            course = Course.query.get(course_id)
            if not course or course.teacher_id != current_user.id:
                flash('无效的课程', 'danger')
                return redirect(url_for('interaction.share_file'))
            
            # 根据分享类型处理
            if share_type == 'resource':
                # 从资源库选择文件
                resource_ids = request.form.getlist('resource_ids')
                
                if not resource_ids:
                    flash('请选择要分享的资源', 'danger')
                    return redirect(url_for('interaction.share_file'))
                
                # 验证资源权限
                for resource_id in resource_ids:
                    resource = Resource.query.get(resource_id)
                    if not resource or resource.owner_id != current_user.id:
                        flash('您没有权限分享某些资源', 'danger')
                        return redirect(url_for('interaction.share_file'))
                
                # 通过WebSocket通知学生
                notify_students_shared_files(course_id, resource_ids, target_type, target_ids)
                
                flash('资源分享成功！', 'success')
                return redirect(url_for('interaction.share_file'))
            
            elif share_type == 'local':
                # 上传本地文件
                if 'file' not in request.files:
                    flash('没有选择文件', 'danger')
                    return redirect(url_for('interaction.share_file'))
                
                files = request.files.getlist('file')
                
                if not files or files[0].filename == '':
                    flash('没有选择文件', 'danger')
                    return redirect(url_for('interaction.share_file'))
                
                # 上传文件并创建资源
                resource_ids = []
                for file in files:
                    # 处理文件上传逻辑（复用resource控制器的代码）
                    from app.controllers.resource import determine_file_type, get_allowed_extensions, calculate_file_md5
                    from werkzeug.utils import secure_filename
                    import os
                    import uuid
                    from flask import current_app
                    
                    # 确保文件名安全
                    filename = secure_filename(file.filename)
                    
                    # 检查文件类型
                    file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
                    allowed_extensions = get_allowed_extensions()
                    
                    if file_ext not in allowed_extensions:
                        flash(f'不支持的文件类型: {filename}', 'danger')
                        continue
                    
                    # 生成唯一文件名
                    unique_filename = f"{uuid.uuid4().hex}_{filename}"
                    
                    # 确定文件类型
                    file_type = determine_file_type(file_ext)
                    
                    # 保存文件
                    file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
                    file.save(file_path)
                    
                    # 获取文件大小
                    file_size = os.path.getsize(file_path)
                    
                    # 计算MD5校验和
                    md5_checksum = calculate_file_md5(file_path)
                    
                    # 创建资源记录
                    resource = Resource(
                        name=filename,
                        type=file_type,
                        url=unique_filename,
                        size=file_size,
                        format=file_ext,
                        owner_id=current_user.id,
                        course_id=course_id,
                        file_metadata={
                            'upload_date': datetime.utcnow().isoformat(),
                            'md5_checksum': md5_checksum,
                            'original_name': filename,
                            'shared': True
                        }
                    )
                    
                    db.session.add(resource)
                    db.session.flush()  # 获取资源ID
                    resource_ids.append(resource.id)
                
                # 提交数据库事务
                db.session.commit()
                
                # 通过WebSocket通知学生
                notify_students_shared_files(course_id, resource_ids, target_type, target_ids)
                
                flash('文件分享成功！', 'success')
                return redirect(url_for('interaction.share_file'))
        
        except Exception as e:
            db.session.rollback()
            flash(f'分享文件失败: {str(e)}', 'danger')
            return redirect(url_for('interaction.share_file'))
    
    # 获取课程的学生和小组，并转换为JSON兼容格式
    course_students_json = {}
    course_groups_json = {}

    for course in courses:
        # 获取并转换学生
        students = CourseStudent.query.filter_by(course_id=course.id).all()
        course_students_json[course.id] = [
            {'student_id': s.student.id, 'name': s.student.username}
            for s in students
        ]
        
        # 获取并转换小组
        groups = Group.query.filter_by(course_id=course.id).all()
        course_groups_json[course.id] = [
            {'id': g.id, 'name': g.name}
            for g in groups
        ]

    return render_template('interaction/share_file.html', 
                          courses=courses, 
                          resources=resources,
                          course_students=course_students_json,
                          course_groups=course_groups_json)

@interaction.route('/course_plans')
@login_required
def course_plans():
    """课程安排列表"""
    # 获取当前用户的课程
    courses = Course.query.filter_by(teacher_id=current_user.id).all()
    
    # 获取当前用户的资源和试卷
    resources = Resource.query.filter_by(owner_id=current_user.id).all()
    exams = Exam.query.filter_by(created_by=current_user.id).all()
    
    return render_template('interaction/course_plans.html', 
                          courses=courses, 
                          resources=resources, 
                          exams=exams)

@interaction.route('/api/course_plans')
@login_required
def api_course_plans():
    """获取课程安排API"""
    # 获取当前用户创建的所有课程安排
    plans = CoursePlan.query.filter_by(created_by=current_user.id).all()
    
    # 转换为日历事件格式
    events = []
    for plan in plans:
        events.append({
            'id': plan.id,
            'title': plan.title,
            'start': plan.start_time.isoformat() if plan.start_time else None,
            'end': plan.end_time.isoformat() if plan.end_time else None,
            'allDay': False
        })
    
    return jsonify(events)

@interaction.route('/api/course_plans/<int:plan_id>')
@login_required
def api_course_plan_detail(plan_id):
    """获取课程安排详情API"""
    plan = CoursePlan.query.get_or_404(plan_id)
    
    # 检查权限
    if plan.created_by != current_user.id:
        return jsonify({'success': False, 'message': '您没有权限查看此课程安排'})
    
    # 获取课程信息
    course = Course.query.get(plan.course_id) if plan.course_id else None
    
    # 获取资源信息
    resources_data = []
    if plan.resources:
        for resource_info in plan.resources:
            resource_type = resource_info.get('type')
            resource_id = resource_info.get('id')
            
            if resource_type == 'resource':
                resource = Resource.query.get(resource_id)
                if resource:
                    resources_data.append({
                        'id': resource.id,
                        'name': resource.name,
                        'type': 'resource'
                    })
            elif resource_type == 'exam':
                exam = Exam.query.get(resource_id)
                if exam:
                    resources_data.append({
                        'id': exam.id,
                        'name': exam.name,
                        'type': 'exam'
                    })
    
    # 构建响应数据
    plan_data = {
        'id': plan.id,
        'title': plan.title,
        'description': plan.description,
        'start_time': plan.start_time.isoformat() if plan.start_time else None,
        'end_time': plan.end_time.isoformat() if plan.end_time else None,
        'location': plan.location,
        'course_id': plan.course_id,
        'course': {'id': course.id, 'name': course.name} if course else None,
        'resources': resources_data,
        'created_at': plan.created_at.isoformat() if plan.created_at else None
    }
    
    return jsonify({'success': True, 'plan': plan_data})

@interaction.route('/course_plans/create', methods=['POST'])
@login_required
def create_course_plan():
    """创建课程安排"""
    try:
        # 获取JSON数据
        data = request.get_json()
        
        # 解析日期时间
        try:
            # 尝试直接解析ISO格式
            start_time = datetime.fromisoformat(data['start_time'].replace('Z', '+00:00'))
        except:
            # 如果失败，尝试其他格式
            start_time = datetime.strptime(data['start_time'], '%Y-%m-%dT%H:%M:%S')
        
        try:
            # 尝试直接解析ISO格式
            end_time = datetime.fromisoformat(data['end_time'].replace('Z', '+00:00'))
        except:
            # 如果失败，尝试其他格式
            end_time = datetime.strptime(data['end_time'], '%Y-%m-%dT%H:%M:%S')
        
        # 创建新课程安排
        new_plan = CoursePlan(
            title=data['title'],
            description=data['description'],
            start_time=start_time,
            end_time=end_time,
            location=data['location'],
            course_id=int(data['course_id']),
            created_by=current_user.id,
            resources=data['resources']
        )
        
        db.session.add(new_plan)
        db.session.commit()
        
        return jsonify({'success': True, 'message': '课程安排创建成功'})
    
    except Exception as e:
        db.session.rollback()
        print(f"Error creating course plan: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@interaction.route('/course_plans/<int:plan_id>/edit', methods=['POST'])
@login_required
def edit_course_plan(plan_id):
    """编辑课程安排"""
    try:
        # 获取课程安排
        plan = CoursePlan.query.get_or_404(plan_id)
        
        # 检查权限
        if plan.created_by != current_user.id:
            return jsonify({'success': False, 'message': '您没有权限编辑此课程安排'})
        
        # 获取JSON数据
        data = request.get_json()
        
        # 解析日期时间
        try:
            # 尝试直接解析ISO格式
            start_time = datetime.fromisoformat(data['start_time'].replace('Z', '+00:00'))
        except:
            # 如果失败，尝试其他格式
            start_time = datetime.strptime(data['start_time'], '%Y-%m-%dT%H:%M:%S')
        
        try:
            # 尝试直接解析ISO格式
            end_time = datetime.fromisoformat(data['end_time'].replace('Z', '+00:00'))
        except:
            # 如果失败，尝试其他格式
            end_time = datetime.strptime(data['end_time'], '%Y-%m-%dT%H:%M:%S')
        
        # 更新课程安排
        plan.title = data['title']
        plan.description = data['description']
        plan.start_time = start_time
        plan.end_time = end_time
        plan.location = data['location']
        plan.course_id = int(data['course_id'])
        plan.resources = data['resources']
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': '课程安排更新成功'})
    
    except Exception as e:
        db.session.rollback()
        print(f"Error editing course plan: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@interaction.route('/add-pick-score', methods=['POST'])
@login_required
def add_pick_score_():
    """为随机点名添加评分"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        flash('只有教师可以评分', 'danger')
        return redirect(url_for('main.index'))
    
    try:
        # 获取表单数据
        course_id = request.form.get('course_id')
        pick_type = request.form.get('pick_type')
        target_id = request.form.get('target_id')
        score = float(request.form.get('score', 0))
        note = request.form.get('note', '')
        
        # 验证课程ID
        course = Course.query.get(course_id)
        if not course or course.teacher_id != current_user.id:
            flash('无效的课程', 'danger')
            return redirect(url_for('interaction.random_pick'))
        
        # 创建或更新评分记录
        from app.models.interaction import RandomPickRecord
        
        # 查找最近的点名记录
        record = RandomPickRecord.query.filter_by(
            course_id=course_id,
            pick_type=pick_type,
            target_id=target_id
        ).order_by(RandomPickRecord.picked_at.desc()).first()
        
        if record:
            # 更新评分
            record.score = score
            record.note = note
            db.session.commit()
            
            flash('评分已保存', 'success')
        else:
            # 创建新记录
            new_record = RandomPickRecord(
                course_id=course_id,
                pick_type=pick_type,
                target_id=target_id,
                score=score,
                note=note
            )
            db.session.add(new_record)
            db.session.commit()
            
            flash('评分已保存', 'success')
        
        return redirect(url_for('interaction.random_pick'))
    
    except Exception as e:
        db.session.rollback()
        flash(f'保存评分失败: {str(e)}', 'danger')
        return redirect(url_for('interaction.random_pick'))

@interaction.route('/course_plans/<int:plan_id>/delete', methods=['POST'])
@login_required
def delete_course_plan(plan_id):
    """删除课程安排"""
    try:
        # 获取课程安排
        plan = CoursePlan.query.get_or_404(plan_id)
        
        # 检查权限
        if plan.created_by != current_user.id:
            return jsonify({'success': False, 'message': '您没有权限删除此课程安排'})
        
        # 删除课程安排
        db.session.delete(plan)
        db.session.commit()
        
        return jsonify({'success': True, 'message': '课程安排已删除'})
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

# WebSocket通知函数
def notify_students_new_question(session_id, course_id):
    """
    通知学生有新题目
    
    Args:
        session_id: 题目会话ID
        course_id: 课程ID
    """
    session = QuestionSession.query.get(session_id)
    if not session:
        return
    
    # 获取会话信息
    session_data = {
        'id': session.id,
        'title': session.title,
        'course_id': session.course_id,
        'teacher_id': session.teacher_id,
        'created_at': session.created_at.isoformat() if session.created_at else None,
        'end_time': session.end_time.isoformat() if session.end_time else None,
        'status': session.status
    }
    
    # 通过WebSocket发送通知
    socketio.emit('new_question_session', session_data, room=f'course_{course_id}')

def notify_students_session_ended(session_id, course_id):
    """
    通知学生题目会话已结束
    
    Args:
        session_id: 题目会话ID
        course_id: 课程ID
    """
    # 通过WebSocket发送通知
    socketio.emit('question_session_ended', {'session_id': session_id}, room=f'course_{course_id}')

def notify_students_shared_files(course_id, resource_ids, target_type, target_ids):
    """
    通知学生有新的共享文件
    
    Args:
        course_id: 课程ID
        resource_ids: 资源ID列表
        target_type: 目标类型（'all', 'group', 'student'）
        target_ids: 目标ID列表
    """
    # 获取资源信息
    resources = []
    for resource_id in resource_ids:
        resource = Resource.query.get(resource_id)
        if resource:
            resources.append({
                'id': resource.id,
                'name': resource.name,
                'type': resource.type,
                'url': resource.url,
                'format': resource.format,
                'size': resource.size
            })
    
    # 构建通知数据
    notification_data = {
        'course_id': course_id,
        'resources': resources,
        'target_type': target_type,
        'target_ids': target_ids,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    # 根据目标类型发送通知
    if target_type == 'all':
        # 发送给课程所有学生
        socketio.emit('shared_files', notification_data, room=f'course_{course_id}')
    elif target_type == 'group':
        # 发送给指定小组
        for group_id in target_ids:
            socketio.emit('shared_files', notification_data, room=f'group_{group_id}')
    elif target_type == 'student':
        # 发送给指定学生
        for student_id in target_ids:
            socketio.emit('shared_files', notification_data, room=f'user_{student_id}')

def notify_students_group_discussion(discussion_id, course_id):
    """
    通知学生有新的分组讨论
    
    Args:
        discussion_id: 讨论ID
        course_id: 课程ID
    """
    # 获取讨论信息
    from app.models.interaction import GroupDiscussion
    discussion = GroupDiscussion.query.get(discussion_id)
    if not discussion:
        return
    
    # 构建通知数据
    notification_data = {
        'id': discussion.id,
        'course_id': discussion.course_id,
        'title': discussion.title,
        'description': discussion.description,
        'topic_content': discussion.topic_content,
        'image_url': discussion.image_url,
        'created_at': discussion.created_at.isoformat() if discussion.created_at else None,
        'end_time': discussion.end_time.isoformat() if discussion.end_time else None,
        'remaining_time': discussion.remaining_time,
        'status': discussion.status
    }
    
    # 发送给课程所有学生
    socketio.emit('group_discussion', notification_data, room=f'course_{course_id}')

@interaction.route('/receive/<int:course_id>')
@login_required
def receive_interaction(course_id):
    """
    学生接收互动内容页面
    
    Args:
        course_id: 课程ID
    """
    # 检查课程是否存在
    course = Course.query.filter_by(id=course_id).first_or_404()
    
    # 检查学生是否已加入该课程
    if not course.has_student(current_user.id) and not current_user.is_teacher():
        return render_template('errors/403.html'), 403
    
    # 获取该课程的互动内容
    # 这里可以根据实际需求从数据库中获取互动内容
    # 例如：文本消息、图片、文件等
    
    # 示例：获取最近的课堂活动作为互动内容
    interactions = ClassActivity.query.filter_by(
        course_id=course_id,
        type='interaction'
    ).order_by(ClassActivity.created_at.desc()).limit(10).all()
    
    return render_template('interaction/receive_interaction.html', 
                          course=course, 
                          interactions=interactions)

@interaction.route('/api/send-interaction/<int:course_id>', methods=['POST'])
@login_required
def send_interaction(course_id):
    """
    发送互动内容API
    
    Args:
        course_id: 课程ID
    """
    # 检查用户是否为教师
    if not current_user.is_teacher():
        return jsonify({
            'success': False,
            'message': '只有教师可以发送互动内容'
        }), 403
    
    # 检查课程是否存在且属于当前教师
    course = Course.query.filter_by(id=course_id, teacher_id=current_user.id).first_or_404()
    
    try:
        # 获取请求数据
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400
        
        # 获取互动内容
        interaction_type = data.get('type')  # 'text', 'image', 'file'
        title = data.get('title', '互动内容')
        description = data.get('description')
        content = data.get('content')
        
        if not interaction_type or not content:
            return jsonify({
                'success': False,
                'message': '互动类型和内容不能为空'
            }), 400
        
        # 创建活动记录
        activity = ClassActivity(
            course_id=course_id,
            type='interaction',
            title=title,
            description=description,
            content=content,
            content_type=interaction_type,
            created_by=current_user.id
        )
        
        # 如果是文件类型，添加文件信息
        if interaction_type == 'file':
            activity.file_name = data.get('file_name')
            activity.file_size = data.get('file_size')
        
        db.session.add(activity)
        db.session.commit()
        
        # 通过WebSocket通知学生
        socketio.emit('new_interaction', {
            'id': activity.id,
            'type': interaction_type,
            'title': title,
            'description': description,
            'content': content,
            'file_name': data.get('file_name'),
            'file_size': data.get('file_size'),
            'created_at': activity.created_at.isoformat(),
            'created_by': current_user.id
        }, room=f'course_{course_id}')
        
        return jsonify({
            'success': True,
            'message': '互动内容已发送',
            'interaction_id': activity.id
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'发送互动内容失败: {str(e)}'
        }), 500

@interaction.route('/receive-question/<int:session_id>')
@login_required
def receive_question(session_id):
    """
    学生接收题目页面
    
    Args:
        session_id: 题目会话ID
    """
    # 获取题目会话
    session = QuestionSession.query.get_or_404(session_id)
    
    # 检查课程是否存在
    course = Course.query.filter_by(id=session.course_id).first_or_404()
    
    # 检查学生是否已加入该课程
    if not course.has_student(current_user.id) and not current_user.is_teacher():
        return render_template('errors/403.html'), 403
    
    return render_template('interaction/receive_question.html', 
                          session=session,
                          course=course)

@interaction.route('/api/session-questions/<int:session_id>')
@login_required
def get_session_questions(session_id):
    """
    获取题目会话中的题目API
    
    Args:
        session_id: 题目会话ID
    """
    # 获取题目会话
    session = QuestionSession.query.get_or_404(session_id)
    
    # 检查课程是否存在
    course = Course.query.filter_by(id=session.course_id).first_or_404()
    
    # 检查学生是否已加入该课程
    if not course.has_student(current_user.id) and not current_user.is_teacher():
        return jsonify({
            'success': False,
            'message': '您没有权限查看此题目会话'
        }), 403
    
    # 获取题目
    questions = []
    
    # 从题库选择的题目
    session_questions = SessionQuestion.query.filter_by(session_id=session_id).all()
    for sq in session_questions:
        question = Question.query.get(sq.question_id)
        if question:
            questions.append({
                'type': 'session_question',
                'id': sq.id,
                'question': {
                    'id': question.id,
                    'type': question.type,
                    'content': question.content,
                    'options': question.options,
                    'score': question.score
                }
            })
    
    # 临时创建的题目
    temp_questions = TemporaryQuestion.query.filter_by(session_id=session_id).all()
    for tq in temp_questions:
        questions.append({
            'type': 'temporary_question',
            'id': tq.id,
            'question_type': tq.type,
            'content': tq.content,
            'options': tq.options,
            'score': tq.score,
            'blank_count': tq.blank_count if tq.type == 'fillblank' else 1
        })
    
    # 截屏题目
    screenshot_questions = ScreenshotQuestion.query.filter_by(session_id=session_id).all()
    for scq in screenshot_questions:
        questions.append({
            'type': 'screenshot_question',
            'id': scq.id,
            'image_url': scq.image_url,
            'description': scq.description,
            'score': scq.score
        })
    
    return jsonify({
        'success': True,
        'questions': questions
    })

@interaction.route('/api/student-answers/<int:session_id>')
@login_required
def get_student_answers(session_id):
    """
    获取学生在题目会话中的答案API
    
    Args:
        session_id: 题目会话ID
    """
    # 获取题目会话
    session = QuestionSession.query.get_or_404(session_id)
    
    # 检查课程是否存在
    course = Course.query.filter_by(id=session.course_id).first_or_404()
    
    # 检查学生是否已加入该课程
    if not course.has_student(current_user.id) and not current_user.is_teacher():
        return jsonify({
            'success': False,
            'message': '您没有权限查看此题目会话'
        }), 403
    
    # 获取学生答案
    answers = {}
    
    # 从题库选择的题目的答案
    session_questions = SessionQuestion.query.filter_by(session_id=session_id).all()
    for sq in session_questions:
        answer = StudentAnswer.query.filter_by(
            session_id=session_id,
            student_id=current_user.id,
            question_type='session_question',
            question_id=sq.id
        ).first()
        
        if answer:
            answers[f'sq_{sq.id}'] = answer.answer
    
    # 临时创建的题目的答案
    temp_questions = TemporaryQuestion.query.filter_by(session_id=session_id).all()
    for tq in temp_questions:
        answer = StudentAnswer.query.filter_by(
            session_id=session_id,
            student_id=current_user.id,
            question_type='temporary_question',
            question_id=tq.id
        ).first()
        
        if answer:
            answers[f'tq_{tq.id}'] = answer.answer
    
    # 截屏题目的答案
    screenshot_questions = ScreenshotQuestion.query.filter_by(session_id=session_id).all()
    for scq in screenshot_questions:
        answer = StudentAnswer.query.filter_by(
            session_id=session_id,
            student_id=current_user.id,
            question_type='screenshot_question',
            question_id=scq.id
        ).first()
        
        if answer:
            answers[f'scq_{scq.id}'] = answer.answer
    
    return jsonify({
        'success': True,
        'answers': answers
    })

@interaction.route('/api/submit-answer', methods=['POST'])
@login_required
def submit_answer_api():
    """
    提交答案API
    """
    # 检查用户是否为学生
    if not current_user.is_student():
        return jsonify({
            'success': False,
            'message': '只有学生可以提交答案'
        }), 403
    
    try:
        # 获取请求数据
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400
        
        session_id = data.get('session_id')
        question_type = data.get('question_type')
        question_id = data.get('question_id')
        answer_value = data.get('answer')
        
        if not session_id or not question_type or not question_id:
            return jsonify({
                'success': False,
                'message': '会话ID、题目类型和题目ID不能为空'
            }), 400
        
        # 获取题目会话
        session = QuestionSession.query.get_or_404(session_id)
        
        # 检查课程是否存在
        course = Course.query.filter_by(id=session.course_id).first_or_404()
        
        # 检查学生是否已加入该课程
        if not course.has_student(current_user.id):
            return jsonify({
                'success': False,
                'message': '您没有权限提交此题目会话的答案'
            }), 403
        
        # 检查会话是否已结束
        if session.status == 'ended' or (session.end_time and session.end_time < datetime.utcnow()):
            return jsonify({
                'success': False,
                'message': '题目会话已结束，无法提交答案'
            }), 400
        
        # 查找现有答案
        existing_answer = StudentAnswer.query.filter_by(
            session_id=session_id,
            student_id=current_user.id,
            question_type=question_type,
            question_id=question_id
        ).first()
        
        # 计算是否正确
        is_correct = None
        
        if question_type == 'session_question':
            # 从题库选择的题目
            session_question = SessionQuestion.query.get(question_id)
            if session_question:
                question = Question.query.get(session_question.question_id)
                if question:
                    if question.type in ['single', 'multiple', 'truefalse']:
                        is_correct = answer_value == question.answer
                    elif question.type == 'fillblank':
                        # 填空题需要逐个比较答案
                        if isinstance(answer_value, list) and isinstance(question.answer, list):
                            is_correct = all(a.lower().strip() == b.lower().strip() for a, b in zip(answer_value, question.answer))
        
        elif question_type == 'temporary_question':
            # 临时创建的题目
            temp_question = TemporaryQuestion.query.get(question_id)
            if temp_question:
                if temp_question.type in ['single', 'multiple', 'truefalse']:
                    is_correct = answer_value == temp_question.answer
                elif temp_question.type == 'fillblank':
                    # 填空题需要逐个比较答案
                    if isinstance(answer_value, list) and isinstance(temp_question.answer, list):
                        is_correct = all(a.lower().strip() == b.lower().strip() for a, b in zip(answer_value, temp_question.answer))
        
        # 更新或创建答案
        if existing_answer:
            existing_answer.answer = answer_value
            existing_answer.is_correct = is_correct
            existing_answer.updated_at = datetime.utcnow()
        else:
            new_answer = StudentAnswer(
                session_id=session_id,
                student_id=current_user.id,
                question_type=question_type,
                question_id=question_id,
                answer=answer_value,
                is_correct=is_correct
            )
            db.session.add(new_answer)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '答案已保存'
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'保存答案失败: {str(e)}'
        }), 500

@interaction.route('/api/submit-all-answers', methods=['POST'])
@login_required
def submit_all_answers():
    """
    提交所有答案API
    """
    # 检查用户是否为学生
    if not current_user.is_student():
        return jsonify({
            'success': False,
            'message': '只有学生可以提交答案'
        }), 403
    
    try:
        # 获取请求数据
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400
        
        session_id = data.get('session_id')
        
        if not session_id:
            return jsonify({
                'success': False,
                'message': '会话ID不能为空'
            }), 400
        
        # 获取题目会话
        session = QuestionSession.query.get_or_404(session_id)
        
        # 检查课程是否存在
        course = Course.query.filter_by(id=session.course_id).first_or_404()
        
        # 检查学生是否已加入该课程
        if not course.has_student(current_user.id):
            return jsonify({
                'success': False,
                'message': '您没有权限提交此题目会话的答案'
            }), 403
        
        # 标记学生已完成答题
        # 这里可以添加一个标记，表示学生已完成答题
        # 例如，可以在StudentAnswer表中添加一个completed字段
        
        # 更新所有答案的提交状态
        StudentAnswer.query.filter_by(
            session_id=session_id,
            student_id=current_user.id
        ).update({
            'submitted': True,
            'submitted_at': datetime.utcnow()
        })
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '所有答案已提交',
            'redirect_url': url_for('interaction.student_discussion')  # 可以重定向到其他页面
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'提交答案失败: {str(e)}'
        }), 500