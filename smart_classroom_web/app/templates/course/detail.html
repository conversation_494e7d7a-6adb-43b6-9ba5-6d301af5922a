{% extends "base.html" %}

{% block title %}课程详情: {{ course.name }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
    #qrcode img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 0 auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h3>{{ course.name }}</h3>
                </div>
                <div class="card-body">
                    <p class="card-text">{{ course.description or '暂无描述' }}</p>
                    <p class="card-text"><small class="text-muted">状态: {{ course.status }} | 创建于: {{ course.created_at.strftime('%Y-%m-%d %H:%M') }}</small></p>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                        <a href="{{ url_for('broadcast.teacher_broadcast', course_id=course.id) }}" class="btn">广播</a>
                        <a href="{{ url_for('group.manage', course_id=course.id) }}" class="btn">分组</a>
                        <a href="{{ url_for('group.monitor', course_id=course.id) }}" class="btn">设备</a>
                        <a href="{{ url_for('group.attendance', course_id=course.id) }}" class="btn">考勤</a>
                        <a href="{{ url_for('report.reports', course_id=course.id) }}" class="nav-link">课堂报告</a>
                        <a href="{{ url_for('report.activities',course_id=course.id) }}" class="nav-link">课堂活动记录</a>
                        <a href="{{ url_for('interaction.random_pick') }}" class="nav-link">点名</a>
                        <a href="{{ url_for('interaction.send_question') }}" class="nav-link">发送题目</a>
                        <div class="btn-group">
                            <button type="button" class="btn dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                互动
                            </button>
                            <ul class="dropdown-menu">
                                <li></li>
                                <li><a class="dropdown-item" href="{{ url_for('interaction.share_file') }}">发送文件</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('interaction.group_discussion') }}">分组讨论</a></li>
                            </ul>
                        </div>
                        
                        <div class="btn-group">
                            <button type="button" class="btn dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                评分
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('interaction.student_score') }}">学生评分</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('interaction.group_score') }}">小组评分</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h4>课程访问码</h4>
                </div>
                <div class="card-body text-center">
                    <p style="font-size: 2.5rem; font-weight: bold; color: #333; letter-spacing: 4px;">{{ course.access_code }}</p>
                    <button class="btn btn-primary w-100" onclick="generateQRCode()">二维码</button>
                    <div id="qrcode" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/qrcode-generator/qrcode.js"></script>
<script>
function generateQRCode() {
    fetch("{{ url_for('course.generate_qr') }}", {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            course_id: {{ course.id }},
            base_url: window.location.origin
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.qr_code) {
            document.getElementById('qrcode').innerHTML = `<img src="${data.qr_code}" alt="课程二维码">`;
        } else {
            alert('二维码生成失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('二维码生成请求失败:', error);
        alert('二维码生成请求失败');
    });
}

</script>
{% endblock %}