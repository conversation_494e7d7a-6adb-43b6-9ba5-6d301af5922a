{% extends "base.html" %}

{% block title %}WebRTC演示{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .video-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 20px;
    }
    
    .video-item {
        width: 320px;
        margin-bottom: 10px;
    }
    
    .video-item video {
        width: 100%;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    
    .video-item .video-label {
        text-align: center;
        margin-top: 5px;
        font-weight: bold;
    }
    
    .control-panel {
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #f9f9f9;
    }
    
    .btn-group {
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mt-4 mb-4">WebRTC演示</h1>
    
    <div class="control-panel">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="roomId">房间ID</label>
                    <input type="text" class="form-control" id="roomId" value="course_{{ course.id if course else '1' }}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="userType">用户类型</label>
                    <select class="form-control" id="userType">
                        <option value="teacher">教师</option>
                        <option value="student">学生</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="btn-group mt-3">
            <button id="joinBtn" class="btn btn-primary">加入房间</button>
            <button id="leaveBtn" class="btn btn-secondary" disabled>离开房间</button>
        </div>
        
        <div class="btn-group mt-3">
            <button id="cameraBtn" class="btn btn-info" disabled>开启摄像头</button>
            <button id="screenBtn" class="btn btn-info" disabled>共享屏幕</button>
            <button id="stopBtn" class="btn btn-warning" disabled>停止共享</button>
        </div>
        
        <div class="btn-group mt-3">
            <button id="broadcastBtn" class="btn btn-success" disabled>开始广播</button>
            <button id="stopBroadcastBtn" class="btn btn-danger" disabled>停止广播</button>
        </div>
        
        <div class="mt-3">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="autoConnect" checked>
                <label class="form-check-label" for="autoConnect">
                    自动连接新加入的对等端
                </label>
            </div>
        </div>
        
        <div class="mt-3">
            <div id="status" class="alert alert-info">
                未连接
            </div>
        </div>
    </div>
    
    <h3>本地视频</h3>
    <div class="video-container">
        <div class="video-item">
            <video id="localVideo" autoplay muted playsinline></video>
            <div class="video-label">本地视频</div>
        </div>
    </div>
    
    <h3>远程视频</h3>
    <div id="remoteVideos" class="video-container">
        <!-- 远程视频将在这里动态添加 -->
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
<script src="{{ url_for('static', filename='js/webrtc.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const joinBtn = document.getElementById('joinBtn');
        const leaveBtn = document.getElementById('leaveBtn');
        const cameraBtn = document.getElementById('cameraBtn');
        const screenBtn = document.getElementById('screenBtn');
        const stopBtn = document.getElementById('stopBtn');
        const broadcastBtn = document.getElementById('broadcastBtn');
        const stopBroadcastBtn = document.getElementById('stopBroadcastBtn');
        const roomIdInput = document.getElementById('roomId');
        const userTypeSelect = document.getElementById('userType');
        const autoConnectCheckbox = document.getElementById('autoConnect');
        const statusDiv = document.getElementById('status');
        const localVideo = document.getElementById('localVideo');
        const remoteVideos = document.getElementById('remoteVideos');
        
        let webrtcClient = null;
        let isConnected = false;
        
        // 初始化WebRTC客户端
        function initWebRTCClient() {
            if (webrtcClient) {
                webrtcClient.disconnect();
            }
            
            webrtcClient = new WebRTCClient({
                socketUrl: window.location.origin,
                userId: '{{ current_user.id if current_user.is_authenticated else "guest" }}',
                userType: userTypeSelect.value
            });
            
            // 设置事件处理器
            webrtcClient.onConnected = (clientId) => {
                updateStatus(`已连接到信令服务器，客户端ID: ${clientId}`);
            };
            
            webrtcClient.onRoomJoined = (data) => {
                updateStatus(`已加入房间: ${data.room_id}，房间内有 ${data.peers.length} 个对等端`);
                isConnected = true;
                updateButtons();
            };
            
            webrtcClient.onPeerJoined = (data) => {
                updateStatus(`新对等端加入: ${data.client_id}`);
                
                // 如果设置了自动连接，且有本地流，则自动创建offer
                if (autoConnectCheckbox.checked && webrtcClient.localStream && 
                    (webrtcClient.isBroadcasting || userTypeSelect.value === 'teacher')) {
                    // 自动连接逻辑在WebRTCClient中已实现
                }
            };
            
            webrtcClient.onPeerLeft = (data) => {
                updateStatus(`对等端离开: ${data.client_id}`);
                removeRemoteVideo(data.client_id);
            };
            
            webrtcClient.onRemoteStream = (peerId, stream) => {
                updateStatus(`接收到来自 ${peerId} 的远程流`);
                addRemoteVideo(peerId, stream);
            };
            
            webrtcClient.onBroadcastStarted = (data) => {
                updateStatus(`广播开始，广播者ID: ${data.broadcaster_id}`);
            };
            
            webrtcClient.onBroadcastStopped = (data) => {
                updateStatus(`广播停止，广播者ID: ${data.broadcaster_id}`);
            };
            
            webrtcClient.onError = (data) => {
                updateStatus(`错误: ${data.message}`, 'danger');
            };
        }
        
        // 更新状态显示
        function updateStatus(message, type = 'info') {
            statusDiv.className = `alert alert-${type}`;
            statusDiv.textContent = message;
        }
        
        // 更新按钮状态
        function updateButtons() {
            joinBtn.disabled = isConnected;
            leaveBtn.disabled = !isConnected;
            cameraBtn.disabled = !isConnected;
            screenBtn.disabled = !isConnected;
            stopBtn.disabled = !isConnected || !webrtcClient || !webrtcClient.localStream;
            broadcastBtn.disabled = !isConnected || !webrtcClient || !webrtcClient.localStream || 
                                   userTypeSelect.value !== 'teacher' || webrtcClient.isBroadcasting;
            stopBroadcastBtn.disabled = !isConnected || !webrtcClient || !webrtcClient.isBroadcasting;
        }
        
        // 添加远程视频
        function addRemoteVideo(peerId, stream) {
            // 检查是否已存在
            let videoItem = document.getElementById(`video-${peerId}`);
            if (!videoItem) {
                videoItem = document.createElement('div');
                videoItem.className = 'video-item';
                videoItem.id = `video-${peerId}`;
                
                const video = document.createElement('video');
                video.autoplay = true;
                video.playsinline = true;
                video.id = `remote-video-${peerId}`;
                
                const label = document.createElement('div');
                label.className = 'video-label';
                label.textContent = `对等端 ${peerId.substring(0, 8)}...`;
                
                videoItem.appendChild(video);
                videoItem.appendChild(label);
                remoteVideos.appendChild(videoItem);
            }
            
            const video = document.getElementById(`remote-video-${peerId}`);
            video.srcObject = stream;
        }
        
        // 移除远程视频
        function removeRemoteVideo(peerId) {
            const videoItem = document.getElementById(`video-${peerId}`);
            if (videoItem) {
                remoteVideos.removeChild(videoItem);
            }
        }
        
        // 事件监听器
        joinBtn.addEventListener('click', () => {
            initWebRTCClient();
            webrtcClient.joinRoom(roomIdInput.value);
        });
        
        leaveBtn.addEventListener('click', () => {
            if (webrtcClient) {
                webrtcClient.leaveRoom();
                webrtcClient.disconnect();
                webrtcClient = null;
            }
            
            isConnected = false;
            updateButtons();
            updateStatus('已断开连接');
            
            // 清除本地视频
            localVideo.srcObject = null;
            
            // 清除所有远程视频
            remoteVideos.innerHTML = '';
        });
        
        cameraBtn.addEventListener('click', async () => {
            try {
                if (webrtcClient) {
                    const stream = await webrtcClient.startCamera();
                    localVideo.srcObject = stream;
                    updateButtons();
                }
            } catch (error) {
                updateStatus(`开启摄像头失败: ${error.message}`, 'danger');
            }
        });
        
        screenBtn.addEventListener('click', async () => {
            try {
                if (webrtcClient) {
                    const stream = await webrtcClient.startScreenSharing();
                    localVideo.srcObject = stream;
                    updateButtons();
                }
            } catch (error) {
                updateStatus(`共享屏幕失败: ${error.message}`, 'danger');
            }
        });
        
        stopBtn.addEventListener('click', () => {
            if (webrtcClient) {
                webrtcClient.stopLocalStream();
                localVideo.srcObject = null;
                updateButtons();
            }
        });
        
        broadcastBtn.addEventListener('click', () => {
            if (webrtcClient) {
                webrtcClient.startBroadcast();
                updateButtons();
            }
        });
        
        stopBroadcastBtn.addEventListener('click', () => {
            if (webrtcClient) {
                webrtcClient.stopBroadcast();
                updateButtons();
            }
        });
        
        userTypeSelect.addEventListener('change', () => {
            updateButtons();
        });
    });
</script>
{% endblock %}