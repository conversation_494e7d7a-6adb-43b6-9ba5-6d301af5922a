{% extends 'base.html' %}

{% block title %}文件分享{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>文件分享</h2>
    
    <div class="card mb-4">
        <div class="card-body">
            <form id="shareFileForm" method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="course_id" class="form-label">选择课程</label>
                    <select class="form-select" id="course_id" name="course_id" required>
                        <option value="">请选择课程</option>
                        {% for course in courses %}
                        <option value="{{ course.id }}">{{ course.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">分享方式</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="share_type" id="share_type_resource" value="resource" checked>
                        <label class="form-check-label" for="share_type_resource">
                            从资源库选择
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="share_type" id="share_type_local" value="local">
                        <label class="form-check-label" for="share_type_local">
                            上传本地文件
                        </label>
                    </div>
                </div>
                
                <div id="resourceSelection" class="mb-3">
                    <label for="resource_ids" class="form-label">选择资源</label>
                    <select class="form-select" id="resource_ids" name="resource_ids" multiple size="5">
                        {% for resource in resources %}
                        <option value="{{ resource.id }}">{{ resource.name }} ({{ resource.type }})</option>
                        {% endfor %}
                    </select>
                    <div class="form-text">按住Ctrl键可以选择多个资源</div>
                </div>
                
                <div id="fileUpload" class="mb-3" style="display: none;">
                    <label for="file" class="form-label">上传文件</label>
                    <input class="form-control" type="file" id="file" name="file" multiple>
                    <div class="form-text">可以选择多个文件一起上传</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">分享对象</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="target_type" id="target_type_all" value="all" checked>
                        <label class="form-check-label" for="target_type_all">
                            所有学生
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="target_type" id="target_type_group" value="group">
                        <label class="form-check-label" for="target_type_group">
                            指定小组
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="target_type" id="target_type_student" value="student">
                        <label class="form-check-label" for="target_type_student">
                            指定学生
                        </label>
                    </div>
                </div>
                
                <div id="groupSelection" class="mb-3" style="display: none;">
                    <label for="target_ids_group" class="form-label">选择小组</label>
                    <select class="form-select" id="target_ids_group" name="target_ids" multiple size="5">
                        <!-- 小组选项将通过JavaScript动态加载 -->
                    </select>
                    <div class="form-text">按住Ctrl键可以选择多个小组</div>
                </div>
                
                <div id="studentSelection" class="mb-3" style="display: none;">
                    <label for="target_ids_student" class="form-label">选择学生</label>
                    <select class="form-select" id="target_ids_student" name="target_ids" multiple size="5">
                        <!-- 学生选项将通过JavaScript动态加载 -->
                    </select>
                    <div class="form-text">按住Ctrl键可以选择多个学生</div>
                </div>
                
                <button type="submit" class="btn btn-primary">分享文件</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 分享方式切换
        const shareTypeResource = document.getElementById('share_type_resource');
        const shareTypeLocal = document.getElementById('share_type_local');
        const resourceSelection = document.getElementById('resourceSelection');
        const fileUpload = document.getElementById('fileUpload');
        
        shareTypeResource.addEventListener('change', function() {
            if (this.checked) {
                resourceSelection.style.display = 'block';
                fileUpload.style.display = 'none';
            }
        });
        
        shareTypeLocal.addEventListener('change', function() {
            if (this.checked) {
                resourceSelection.style.display = 'none';
                fileUpload.style.display = 'block';
            }
        });
        
        // 分享对象切换
        const targetTypeAll = document.getElementById('target_type_all');
        const targetTypeGroup = document.getElementById('target_type_group');
        const targetTypeStudent = document.getElementById('target_type_student');
        const groupSelection = document.getElementById('groupSelection');
        const studentSelection = document.getElementById('studentSelection');
        
        targetTypeAll.addEventListener('change', function() {
            if (this.checked) {
                groupSelection.style.display = 'none';
                studentSelection.style.display = 'none';
            }
        });
        
        targetTypeGroup.addEventListener('change', function() {
            if (this.checked) {
                groupSelection.style.display = 'block';
                studentSelection.style.display = 'none';
            }
        });
        
        targetTypeStudent.addEventListener('change', function() {
            if (this.checked) {
                groupSelection.style.display = 'none';
                studentSelection.style.display = 'block';
            }
        });
        
        // 课程选择变化时，更新小组和学生列表
        const courseSelect = document.getElementById('course_id');
        const groupSelect = document.getElementById('target_ids_group');
        const studentSelect = document.getElementById('target_ids_student');
        
        // 课程小组和学生数据
        const courseGroups = {{ course_groups|tojson }};
        const courseStudents = {{ course_students|tojson }};
        
        courseSelect.addEventListener('change', function() {
            const courseId = this.value;
            
            // 清空现有选项
            groupSelect.innerHTML = '';
            studentSelect.innerHTML = '';
            
            if (courseId) {
                // 添加小组选项
                if (courseGroups[courseId]) {
                    courseGroups[courseId].forEach(group => {
                        const option = document.createElement('option');
                        option.value = group.id;
                        option.textContent = group.name;
                        groupSelect.appendChild(option);
                    });
                }
                
                // 添加学生选项
                if (courseStudents[courseId]) {
                    courseStudents[courseId].forEach(student => {
                        const option = document.createElement('option');
                        option.value = student.student_id;
                        option.textContent = student.student ? student.student.name : `学生ID: ${student.student_id}`;
                        studentSelect.appendChild(option);
                    });
                }
            }
        });
        
        // 表单提交前验证
        const shareFileForm = document.getElementById('shareFileForm');
        shareFileForm.addEventListener('submit', function(event) {
            const shareType = document.querySelector('input[name="share_type"]:checked').value;
            const targetType = document.querySelector('input[name="target_type"]:checked').value;
            
            if (shareType === 'resource') {
                const resourceIds = document.getElementById('resource_ids');
                if (resourceIds.selectedOptions.length === 0) {
                    event.preventDefault();
                    alert('请选择至少一个资源');
                    return;
                }
            } else if (shareType === 'local') {
                const fileInput = document.getElementById('file');
                if (fileInput.files.length === 0) {
                    event.preventDefault();
                    alert('请选择至少一个文件');
                    return;
                }
            }
            
            if (targetType === 'group') {
                const groupIds = document.getElementById('target_ids_group');
                if (groupIds.selectedOptions.length === 0) {
                    event.preventDefault();
                    alert('请选择至少一个小组');
                    return;
                }
            } else if (targetType === 'student') {
                const studentIds = document.getElementById('target_ids_student');
                if (studentIds.selectedOptions.length === 0) {
                    event.preventDefault();
                    alert('请选择至少一个学生');
                    return;
                }
            }
        });
    });
</script>
{% endblock %}