{% extends 'base.html' %}

{% block title %}题目会话结果{% endblock %}

{% block styles %}
<style>
    .question-card {
        margin-bottom: 20px;
    }
    .question-content {
        margin-bottom: 15px;
    }
    .options-list {
        list-style-type: none;
        padding-left: 0;
    }
    .option-item {
        padding: 8px 12px;
        margin-bottom: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .option-item.correct {
        background-color: #d4edda;
        border-color: #c3e6cb;
    }
    .screenshot-image {
        max-width: 100%;
        margin-bottom: 15px;
        border: 1px solid #ddd;
    }
    .answer-textarea {
        width: 100%;
        min-height: 100px;
    }
    .stats-container {
        margin-top: 20px;
    }
    .chart-container {
        height: 300px;
    }
    .answer-list {
        max-height: 300px;
        overflow-y: auto;
    }
    .answer-item {
        padding: 10px;
        border-bottom: 1px solid #eee;
    }
    .answer-item:last-child {
        border-bottom: none;
    }
    .nav-pills .nav-link.active {
        background-color: #0d6efd;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>{{ session.title }} - 结果</h2>
        <div>
            <a href="{{ url_for('interaction.question_sessions') }}" class="btn btn-outline-secondary">
                返回列表
            </a>
            <a href="{{ url_for('interaction.view_question_session', session_id=session.id) }}" class="btn btn-outline-primary">
                查看题目
            </a>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5>基本信息</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>课程:</strong> {{ session.course.name }}</p>
                    <p><strong>创建时间:</strong> {{ session.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                </div>
                <div class="col-md-6">
                    <p>
                        <strong>状态:</strong>
                        {% if session.status == 'active' %}
                        <span class="badge bg-success">进行中</span>
                        {% else %}
                        <span class="badge bg-secondary">已结束</span>
                        {% endif %}
                    </p>
                    <p>
                        <strong>截止时间:</strong>
                        {% if session.end_time %}
                        {{ session.end_time.strftime('%Y-%m-%d %H:%M') }}
                        {% else %}
                        无截止时间
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5>答题统计</h5>
        </div>
        <div class="card-body">
            <ul class="nav nav-pills mb-3" id="stats-tab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="class-tab" data-bs-toggle="pill" data-bs-target="#class-stats" type="button" role="tab" aria-controls="class-stats" aria-selected="true">全班统计</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="group-tab" data-bs-toggle="pill" data-bs-target="#group-stats" type="button" role="tab" aria-controls="group-stats" aria-selected="false">分组统计</button>
                </li>
            </ul>
            <div class="tab-content" id="stats-tabContent">
                <div class="tab-pane fade show active" id="class-stats" role="tabpanel" aria-labelledby="class-tab">
                    <div class="stats-container">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">参与情况</h6>
                                        <div class="chart-container" id="participation-chart"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">正确率</h6>
                                        <div class="chart-container" id="accuracy-chart"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <h5 class="mt-4">题目详细分析</h5>
                        <div class="row" id="question-details">
                            <!-- 题目详细分析图表将通过JavaScript动态添加 -->
                        </div>
                        
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5>统计摘要</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>总学生数:</strong> {{ class_stats.total_students }}</p>
                                        <p><strong>参与学生数:</strong> {{ class_stats.answered_students }}</p>
                                        <p><strong>参与率:</strong> {{ (class_stats.participation_rate * 100) | round(2) }}%</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>题目数量:</strong> {{ session_questions|length + temporary_questions|length + screenshot_questions|length }}</p>
                                        <p><strong>平均正确率:</strong>
                                            {% set total_correct = 0 %}
                                            {% set total_answers = 0 %}
                                            {% for key, q in class_stats.questions.items() %}
                                                {% set total_correct = total_correct + q.correct_answers %}
                                                {% set total_answers = total_answers + q.total_answers %}
                                            {% endfor %}
                                            {{ (total_correct / total_answers * 100) | round(2) if total_answers > 0 else 0 }}%
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="group-stats" role="tabpanel" aria-labelledby="group-tab">
                    <div class="stats-container">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">小组参与情况</h6>
                                        <div class="chart-container" id="group-participation-chart"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">小组正确率</h6>
                                        <div class="chart-container" id="group-accuracy-chart"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5>小组详细统计</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>小组名称</th>
                                                <th>总人数</th>
                                                <th>参与人数</th>
                                                <th>参与率</th>
                                                <th>答题数</th>
                                                <th>正确数</th>
                                                <th>正确率</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for group in groups %}
                                            <tr>
                                                <td>{{ group.name }}</td>
                                                <td>{{ group_stats[group.id].total_students }}</td>
                                                <td>{{ group_stats[group.id].answered_students }}</td>
                                                <td>{{ (group_stats[group.id].participation_rate * 100) | round(2) }}%</td>
                                                <td>{{ group_stats[group.id].total_answers }}</td>
                                                <td>{{ group_stats[group.id].correct_answers }}</td>
                                                <td>{{ (group_stats[group.id].accuracy_rate * 100) | round(2) }}%</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <h3>题目详情</h3>
    
    {% if session_questions %}
    <h4 class="mt-4">从题库选择的题目</h4>
    {% for sq in session_questions %}
    <div class="card question-card" id="sq-{{ sq.id }}">
        <div class="card-header">
            <h5>
                {{ sq.question.type | replace('single', '单选题') | replace('multiple', '多选题') | replace('truefalse', '判断题') | replace('fillblank', '填空题') | replace('subjective', '主观题') }}
                <span class="badge bg-info float-end">{{ sq.question.score }}分</span>
            </h5>
        </div>
        <div class="card-body">
            <div class="question-content">
                {{ sq.question.content | safe }}
            </div>
            
            {% if sq.question.type == 'single' %}
            <h6>选项</h6>
            <ul class="options-list">
                {% for option in sq.question.options %}
                <li class="option-item {% if loop.index0 == sq.question.answer %}correct{% endif %}">
                    {{ loop.index }}. {{ option }}
                    {% if loop.index0 == sq.question.answer %}
                    <span class="badge bg-success float-end">正确答案</span>
                    {% endif %}
                </li>
                {% endfor %}
            </ul>
            
            {% elif sq.question.type == 'multiple' %}
            <h6>选项</h6>
            <ul class="options-list">
                {% for option in sq.question.options %}
                <li class="option-item {% if loop.index0 in sq.question.answer %}correct{% endif %}">
                    {{ loop.index }}. {{ option }}
                    {% if loop.index0 in sq.question.answer %}
                    <span class="badge bg-success float-end">正确答案</span>
                    {% endif %}
                </li>
                {% endfor %}
            </ul>
            
            {% elif sq.question.type == 'truefalse' %}
            <h6>答案</h6>
            <ul class="options-list">
                <li class="option-item {% if sq.question.answer == true %}correct{% endif %}">
                    正确
                    {% if sq.question.answer == true %}
                    <span class="badge bg-success float-end">正确答案</span>
                    {% endif %}
                </li>
                <li class="option-item {% if sq.question.answer == false %}correct{% endif %}">
                    错误
                    {% if sq.question.answer == false %}
                    <span class="badge bg-success float-end">正确答案</span>
                    {% endif %}
                </li>
            </ul>
            
            {% elif sq.question.type == 'fillblank' %}
            <h6>答案</h6>
            <div class="alert alert-success">
                {{ sq.question.answer | join(', ') }}
            </div>
            
            {% elif sq.question.type == 'subjective' %}
            <h6>参考答案</h6>
            <div class="alert alert-info">
                {{ sq.question.answer }}
            </div>
            {% endif %}
            
            <div class="mt-4">
                <h6>学生答案</h6>
                <div class="answer-list">
                    {% set found_answers = false %}
                    {% for student in course_students %}
                        {% set answer_key = 'sq_' ~ sq.id %}
                        {% if student.student_id in answers_data and answer_key in answers_data[student.student_id] %}
                            {% set found_answers = true %}
                            {% set answer = answers_data[student.student_id][answer_key] %}
                            <div class="answer-item">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>{{ answer.student.username }}</strong>
                                        <span class="text-muted">({{ answer.submitted_at.strftime('%Y-%m-%d %H:%M') }})</span>
                                    </div>
                                    {% if answer.is_correct is not none %}
                                        {% if answer.is_correct %}
                                        <span class="badge bg-success">正确</span>
                                        {% else %}
                                        <span class="badge bg-danger">错误</span>
                                        {% endif %}
                                    {% endif %}
                                </div>
                                
                                {% if sq.question.type == 'single' %}
                                <div>
                                    选择: {{ sq.question.options[answer.answer] if answer.answer < sq.question.options|length else '无效选项' }}
                                </div>
                                
                                {% elif sq.question.type == 'multiple' %}
                                <div>
                                    选择:
                                    {% for index in answer.answer %}
                                    {{ sq.question.options[index] if index < sq.question.options|length else '无效选项' }}{% if not loop.last %}, {% endif %}
                                    {% endfor %}
                                </div>
                                
                                {% elif sq.question.type == 'truefalse' %}
                                <div>
                                    选择: {{ '正确' if answer.answer else '错误' }}
                                </div>
                                
                                {% elif sq.question.type == 'fillblank' %}
                                <div>
                                    答案: {{ answer.answer | join(', ') }}
                                </div>
                                
                                {% elif sq.question.type == 'subjective' %}
                                <div>
                                    答案: {{ answer.answer }}
                                </div>
                                {% endif %}
                            </div>
                        {% endif %}
                    {% endfor %}
                    
                    {% if not found_answers %}
                    <div class="alert alert-warning">
                        暂无学生提交答案
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% endif %}
    
    {% if temporary_questions %}
    <h4 class="mt-4">临时创建的题目</h4>
    {% for tq in temporary_questions %}
    <div class="card question-card" id="tq-{{ tq.id }}">
        <div class="card-header">
            <h5>
                {{ tq.type | replace('single', '单选题') | replace('multiple', '多选题') | replace('truefalse', '判断题') | replace('fillblank', '填空题') | replace('subjective', '主观题') }}
                <span class="badge bg-info float-end">{{ tq.score }}分</span>
            </h5>
        </div>
        <div class="card-body">
            <div class="question-content">
                {{ tq.content | safe }}
            </div>
            
            {% if tq.type == 'single' %}
            <h6>选项</h6>
            <ul class="options-list">
                {% for option in tq.options %}
                <li class="option-item {% if loop.index0 == tq.answer %}correct{% endif %}">
                    {{ loop.index }}. {{ option }}
                    {% if loop.index0 == tq.answer %}
                    <span class="badge bg-success float-end">正确答案</span>
                    {% endif %}
                </li>
                {% endfor %}
            </ul>
            
            {% elif tq.type == 'multiple' %}
            <h6>选项</h6>
            <ul class="options-list">
                {% for option in tq.options %}
                <li class="option-item {% if loop.index0 in tq.answer %}correct{% endif %}">
                    {{ loop.index }}. {{ option }}
                    {% if loop.index0 in tq.answer %}
                    <span class="badge bg-success float-end">正确答案</span>
                    {% endif %}
                </li>
                {% endfor %}
            </ul>
            
            {% elif tq.type == 'truefalse' %}
            <h6>答案</h6>
            <ul class="options-list">
                <li class="option-item {% if tq.answer == true %}correct{% endif %}">
                    正确
                    {% if tq.answer == true %}
                    <span class="badge bg-success float-end">正确答案</span>
                    {% endif %}
                </li>
                <li class="option-item {% if tq.answer == false %}correct{% endif %}">
                    错误
                    {% if tq.answer == false %}
                    <span class="badge bg-success float-end">正确答案</span>
                    {% endif %}
                </li>
            </ul>
            
            {% elif tq.type == 'fillblank' %}
            <h6>答案</h6>
            <div class="alert alert-success">
                {{ tq.answer | join(', ') }}
            </div>
            
            {% elif tq.type == 'subjective' %}
            <h6>参考答案</h6>
            <div class="alert alert-info">
                {{ tq.answer }}
            </div>
            {% endif %}
            
            <div class="mt-4">
                <h6>学生答案</h6>
                <div class="answer-list">
                    {% set found_answers = false %}
                    {% for student in course_students %}
                        {% set answer_key = 'tq_' ~ tq.id %}
                        {% if student.student_id in answers_data and answer_key in answers_data[student.student_id] %}
                            {% set found_answers = true %}
                            {% set answer = answers_data[student.student_id][answer_key] %}
                            <div class="answer-item">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>{{ answer.student.username }}</strong>
                                        <span class="text-muted">({{ answer.submitted_at.strftime('%Y-%m-%d %H:%M') }})</span>
                                    </div>
                                    {% if answer.is_correct is not none %}
                                        {% if answer.is_correct %}
                                        <span class="badge bg-success">正确</span>
                                        {% else %}
                                        <span class="badge bg-danger">错误</span>
                                        {% endif %}
                                    {% endif %}
                                </div>
                                
                                {% if tq.type == 'single' %}
                                <div>
                                    选择: {{ tq.options[answer.answer] if answer.answer < tq.options|length else '无效选项' }}
                                </div>
                                
                                {% elif tq.type == 'multiple' %}
                                <div>
                                    选择:
                                    {% for index in answer.answer %}
                                    {{ tq.options[index] if index < tq.options|length else '无效选项' }}{% if not loop.last %}, {% endif %}
                                    {% endfor %}
                                </div>
                                
                                {% elif tq.type == 'truefalse' %}
                                <div>
                                    选择: {{ '正确' if answer.answer else '错误' }}
                                </div>
                                
                                {% elif tq.type == 'fillblank' %}
                                <div>
                                    答案: {{ answer.answer | join(', ') }}
                                </div>
                                
                                {% elif tq.type == 'subjective' %}
                                <div>
                                    答案: {{ answer.answer }}
                                </div>
                                {% endif %}
                            </div>
                        {% endif %}
                    {% endfor %}
                    
                    {% if not found_answers %}
                    <div class="alert alert-warning">
                        暂无学生提交答案
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% endif %}
    
    {% if screenshot_questions %}
    <h4 class="mt-4">截屏题目</h4>
    {% for scq in screenshot_questions %}
    <div class="card question-card" id="scq-{{ scq.id }}">
        <div class="card-header">
            <h5>
                截屏题目
                <span class="badge bg-info float-end">{{ scq.score }}分</span>
            </h5>
        </div>
        <div class="card-body">
            <img src="{{ scq.image_url }}" alt="截屏题目" class="screenshot-image">
            
            <div class="mb-3">
                <h6>题目描述</h6>
                <p>{{ scq.description }}</p>
            </div>
            
            <div class="mb-3">
                <h6>参考答案</h6>
                <div class="alert alert-info">
                    {{ scq.answer }}
                </div>
            </div>
            
            <div class="mt-4">
                <h6>学生答案</h6>
                <div class="answer-list">
                    {% set found_answers = false %}
                    {% for student in course_students %}
                        {% set answer_key = 'scq_' ~ scq.id %}
                        {% if student.student_id in answers_data and answer_key in answers_data[student.student_id] %}
                            {% set found_answers = true %}
                            {% set answer = answers_data[student.student_id][answer_key] %}
                            <div class="answer-item">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>{{ answer.student.username }}</strong>
                                        <span class="text-muted">({{ answer.submitted_at.strftime('%Y-%m-%d %H:%M') }})</span>
                                    </div>
                                </div>
                                <div>
                                    答案: {{ answer.answer }}
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                    
                    {% if not found_answers %}
                    <div class="alert alert-warning">
                        暂无学生提交答案
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // 使用后端传递的统计数据
    const classStats = {{ class_stats | tojson }};
    const groupStats = {{ group_stats | tojson }};
    
    // 创建参与情况图表
    const participationCtx = document.getElementById('participation-chart').getContext('2d');
    new Chart(participationCtx, {
        type: 'pie',
        data: {
            labels: ['已参与', '未参与'],
            datasets: [{
                data: [
                    classStats.answered_students, 
                    classStats.total_students - classStats.answered_students
                ],
                backgroundColor: ['#4caf50', '#f44336']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const percentage = Math.round((value / classStats.total_students) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
    
    // 创建正确率图表
    const accuracyCtx = document.getElementById('accuracy-chart').getContext('2d');
    const questionKeys = Object.keys(classStats.questions);
    const questionLabels = questionKeys.map(key => classStats.questions[key].title);
    const accuracyData = questionKeys.map(key => {
        const q = classStats.questions[key];
        if (q.total_answers > 0) {
            return Math.round((q.correct_answers / q.total_answers) * 100);
        }
        return 0;
    });
    
    new Chart(accuracyCtx, {
        type: 'bar',
        data: {
            labels: questionLabels,
            datasets: [{
                label: '正确率 (%)',
                data: accuracyData,
                backgroundColor: '#2196f3'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // 创建小组参与情况图表
    const groupParticipationCtx = document.getElementById('group-participation-chart').getContext('2d');
    const groupIds = Object.keys(groupStats);
    const groupNames = groupIds.map(id => groupStats[id].name);
    const groupParticipationData = groupIds.map(id => {
        const g = groupStats[id];
        if (g.total_students > 0) {
            return Math.round((g.answered_students / g.total_students) * 100);
        }
        return 0;
    });
    
    new Chart(groupParticipationCtx, {
        type: 'bar',
        data: {
            labels: groupNames,
            datasets: [{
                label: '参与率 (%)',
                data: groupParticipationData,
                backgroundColor: '#ff9800'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // 创建小组正确率图表
    const groupAccuracyCtx = document.getElementById('group-accuracy-chart').getContext('2d');
    const groupAccuracyData = groupIds.map(id => {
        const g = groupStats[id];
        if (g.total_answers > 0) {
            return Math.round((g.accuracy_rate * 100));
        }
        return 0;
    });
    
    new Chart(groupAccuracyCtx, {
        type: 'bar',
        data: {
            labels: groupNames,
            datasets: [{
                label: '正确率 (%)',
                data: groupAccuracyData,
                backgroundColor: '#9c27b0'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // 添加更多详细的题目分析图表
    const questionDetailsContainer = document.getElementById('question-details');
    if (questionDetailsContainer) {
        questionKeys.forEach(key => {
            const q = classStats.questions[key];
            if (q.total_answers > 0) {
                // 创建图表容器
                const chartDiv = document.createElement('div');
                chartDiv.className = 'col-md-6 mb-4';
                
                const cardDiv = document.createElement('div');
                cardDiv.className = 'card';
                
                const cardBody = document.createElement('div');
                cardBody.className = 'card-body';
                
                const cardTitle = document.createElement('h6');
                cardTitle.className = 'card-title';
                cardTitle.textContent = `${q.title} - 答题分布`;
                
                const chartContainer = document.createElement('div');
                chartContainer.className = 'chart-container';
                chartContainer.style.height = '200px';
                chartContainer.id = `chart-${key}`;
                
                cardBody.appendChild(cardTitle);
                cardBody.appendChild(chartContainer);
                cardDiv.appendChild(cardBody);
                chartDiv.appendChild(cardDiv);
                questionDetailsContainer.appendChild(chartDiv);
                
                // 创建图表
                const ctx = document.getElementById(`chart-${key}`).getContext('2d');
                
                // 根据题目类型创建不同的图表
                if (q.type === 'single' || q.type === 'multiple' || q.type === 'truefalse') {
                    // 创建饼图显示正确/错误比例
                    new Chart(ctx, {
                        type: 'pie',
                        data: {
                            labels: ['正确', '错误'],
                            datasets: [{
                                data: [q.correct_answers, q.total_answers - q.correct_answers],
                                backgroundColor: ['#4caf50', '#f44336']
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            const label = context.label || '';
                                            const value = context.raw || 0;
                                            const percentage = Math.round((value / q.total_answers) * 100);
                                            return `${label}: ${value} (${percentage}%)`;
                                        }
                                    }
                                }
                            }
                        }
                    });
                } else {
                    // 对于其他题型，显示提交数量
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['提交数量'],
                            datasets: [{
                                label: '答案数量',
                                data: [q.total_answers],
                                backgroundColor: '#2196f3'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
            }
        });
    }
</script>
{% endblock %}