{% extends 'base.html' %}

{% block title %}小组文件分享{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>小组文件分享</h2>
    
    <div class="card mb-4">
        <div class="card-body">
            <form id="shareFileForm" method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="group_id" class="form-label">选择小组</label>
                    <select class="form-select" id="group_id" name="group_id" required>
                        <option value="">请选择小组</option>
                        {% for group_info in user_groups %}
                        <option value="{{ group_info.group.id }}">{{ group_info.course.name }} - {{ group_info.group.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">分享方式</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="share_type" id="share_type_resource" value="resource" checked>
                        <label class="form-check-label" for="share_type_resource">
                            从我的资源库选择
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="share_type" id="share_type_local" value="local">
                        <label class="form-check-label" for="share_type_local">
                            上传本地文件
                        </label>
                    </div>
                </div>
                
                <div id="resourceSelection" class="mb-3">
                    <label for="resource_ids" class="form-label">选择资源</label>
                    <select class="form-select" id="resource_ids" name="resource_ids" multiple size="5">
                        {% for resource in user_resources %}
                        <option value="{{ resource.id }}">{{ resource.name }} ({{ resource.type }})</option>
                        {% endfor %}
                    </select>
                    <div class="form-text">按住Ctrl键可以选择多个资源</div>
                </div>
                
                <div id="fileUpload" class="mb-3" style="display: none;">
                    <label for="file" class="form-label">上传文件</label>
                    <input class="form-control" type="file" id="file" name="file" multiple>
                    <div class="form-text">可以选择多个文件一起上传</div>
                </div>
                
                <div class="mb-3">
                    <div id="groupMembers" style="display: none;">
                        <label class="form-label">小组成员</label>
                        <ul class="list-group" id="membersList">
                            <!-- 小组成员将通过JavaScript动态加载 -->
                        </ul>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">分享文件</button>
            </form>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h5>已分享的文件</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>文件名</th>
                            <th>类型</th>
                            <th>大小</th>
                            <th>分享时间</th>
                            <th>分享到</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="sharedFilesList">
                        <!-- 已分享文件将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 分享方式切换
        const shareTypeResource = document.getElementById('share_type_resource');
        const shareTypeLocal = document.getElementById('share_type_local');
        const resourceSelection = document.getElementById('resourceSelection');
        const fileUpload = document.getElementById('fileUpload');
        
        shareTypeResource.addEventListener('change', function() {
            if (this.checked) {
                resourceSelection.style.display = 'block';
                fileUpload.style.display = 'none';
            }
        });
        
        shareTypeLocal.addEventListener('change', function() {
            if (this.checked) {
                resourceSelection.style.display = 'none';
                fileUpload.style.display = 'block';
            }
        });
        
        // 小组选择变化时，更新小组成员列表
        const groupSelect = document.getElementById('group_id');
        const groupMembers = document.getElementById('groupMembers');
        const membersList = document.getElementById('membersList');
        
        // 小组成员数据
        const groupMembersData = {{ group_members|tojson }};
        
        groupSelect.addEventListener('change', function() {
            const groupId = this.value;
            
            // 清空现有选项
            membersList.innerHTML = '';
            
            if (groupId) {
                // 显示小组成员区域
                groupMembers.style.display = 'block';
                
                // 添加小组成员
                if (groupMembersData[groupId]) {
                    groupMembersData[groupId].forEach(member => {
                        const listItem = document.createElement('li');
                        listItem.className = 'list-group-item';
                        listItem.textContent = member.name;
                        membersList.appendChild(listItem);
                    });
                }
            } else {
                // 隐藏小组成员区域
                groupMembers.style.display = 'none';
            }
        });
        
        // 表单提交前验证
        const shareFileForm = document.getElementById('shareFileForm');
        shareFileForm.addEventListener('submit', function(event) {
            const shareType = document.querySelector('input[name="share_type"]:checked').value;
            
            if (shareType === 'resource') {
                const resourceIds = document.getElementById('resource_ids');
                if (resourceIds.selectedOptions.length === 0) {
                    event.preventDefault();
                    alert('请选择至少一个资源');
                    return;
                }
            } else if (shareType === 'local') {
                const fileInput = document.getElementById('file');
                if (fileInput.files.length === 0) {
                    event.preventDefault();
                    alert('请选择至少一个文件');
                    return;
                }
            }
        });
        
        // 加载已分享文件列表
        function loadSharedFiles() {
            const sharedFilesList = document.getElementById('sharedFilesList');
            
            // 这里可以通过AJAX请求获取已分享文件列表
            // 为了简单起见，这里暂时不实现
            
            // 示例代码：
            /*
            fetch('/api/group/shared-files')
                .then(response => response.json())
                .then(data => {
                    sharedFilesList.innerHTML = '';
                    
                    data.forEach(file => {
                        const row = document.createElement('tr');
                        
                        const nameCell = document.createElement('td');
                        nameCell.textContent = file.name;
                        row.appendChild(nameCell);
                        
                        const typeCell = document.createElement('td');
                        typeCell.textContent = file.type;
                        row.appendChild(typeCell);
                        
                        const sizeCell = document.createElement('td');
                        sizeCell.textContent = formatFileSize(file.size);
                        row.appendChild(sizeCell);
                        
                        const timeCell = document.createElement('td');
                        timeCell.textContent = new Date(file.shared_at).toLocaleString();
                        row.appendChild(timeCell);
                        
                        const groupCell = document.createElement('td');
                        groupCell.textContent = file.group_name;
                        row.appendChild(groupCell);
                        
                        const actionCell = document.createElement('td');
                        const downloadLink = document.createElement('a');
                        downloadLink.href = file.download_url;
                        downloadLink.className = 'btn btn-sm btn-primary me-2';
                        downloadLink.textContent = '下载';
                        actionCell.appendChild(downloadLink);
                        
                        row.appendChild(actionCell);
                        
                        sharedFilesList.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('加载已分享文件失败:', error);
                });
            */
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 初始加载已分享文件
        loadSharedFiles();
        
        // 监听WebSocket事件，实时更新已分享文件列表
        // 这里假设已经在其他地方建立了WebSocket连接
        if (typeof socket !== 'undefined') {
            socket.on('group_shared_files', function(data) {
                // 重新加载已分享文件列表
                loadSharedFiles();
                
                // 显示通知
                alert('收到新的文件分享！');
            });
        }
    });
</script>
{% endblock %}