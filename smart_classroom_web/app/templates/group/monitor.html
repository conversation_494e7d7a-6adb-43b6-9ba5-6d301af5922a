{% extends 'base.html' %}

{% block title %}设备状态监控 - {{ course.name }}{% endblock %}

{% block styles %}
<style>
    .device-container {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .device-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .device-title {
        font-size: 1.2rem;
        font-weight: bold;
    }
    
    .device-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }
    
    .device-card {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        background-color: #f9f9f9;
        transition: all 0.3s ease;
    }
    
    .device-card.online {
        border-left: 5px solid #28a745;
    }
    
    .device-card.offline {
        border-left: 5px solid #dc3545;
        opacity: 0.7;
    }
    
    .device-card.broadcasting {
        border-left: 5px solid #007bff;
        background-color: #e6f2ff;
    }
    
    .device-card.receiving {
        border-left: 5px solid #ffc107;
        background-color: #fff9e6;
    }
    
    .device-name {
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .device-status {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        color: white;
        margin-bottom: 10px;
    }
    
    .device-status.online {
        background-color: #28a745;
    }
    
    .device-status.offline {
        background-color: #dc3545;
    }
    
    .device-status.broadcasting {
        background-color: #007bff;
    }
    
    .device-status.receiving {
        background-color: #ffc107;
        color: #212529;
    }
    
    .device-info {
        font-size: 0.9rem;
        color: #666;
    }
    
    .device-info div {
        margin-bottom: 5px;
    }
    
    .status-summary {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .status-item {
        padding: 10px 15px;
        border-radius: 5px;
        text-align: center;
        flex: 1;
    }
    
    .status-item.online {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-item.offline {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .status-item.broadcasting {
        background-color: #cce5ff;
        color: #004085;
    }
    
    .status-item.receiving {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .status-count {
        font-size: 1.5rem;
        font-weight: bold;
    }
    
    .status-label {
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h1>设备状态监控 - {{ course.name }}</h1>
            <p>实时监控课堂中所有设备的连接状态</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('group.manage', course_id=course.id) }}" class="btn btn-primary">
                <i class="fas fa-users"></i> 分组管理
            </a>
        </div>
    </div>
    
    <div class="status-summary">
        <div class="status-item online">
            <div class="status-count" id="online-count">0</div>
            <div class="status-label">在线</div>
        </div>
        <div class="status-item offline">
            <div class="status-count" id="offline-count">0</div>
            <div class="status-label">离线</div>
        </div>
        <div class="status-item broadcasting">
            <div class="status-count" id="broadcasting-count">0</div>
            <div class="status-label">广播中</div>
        </div>
        <div class="status-item receiving">
            <div class="status-count" id="receiving-count">0</div>
            <div class="status-label">接收中</div>
        </div>
    </div>
    
    <div class="device-container">
        <div class="device-header">
            <div class="device-title">教师设备</div>
        </div>
        <div class="device-grid" id="teacher-devices">
            <!-- 教师设备将通过JavaScript动态添加 -->
        </div>
    </div>
    
    <div class="device-container">
        <div class="device-header">
            <div class="device-title">小组设备</div>
        </div>
        <div class="device-grid" id="group-devices">
            <!-- 小组设备将通过JavaScript动态添加 -->
        </div>
    </div>
    
    <div class="device-container">
        <div class="device-header">
            <div class="device-title">学生设备</div>
        </div>
        <div class="device-grid" id="student-devices">
            <!-- 学生设备将通过JavaScript动态添加 -->
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const courseId = {{ course.id }};
        let socket;
        
        // 初始化Socket.IO连接
        function initSocketConnection() {
            socket = io();
            
            // 连接成功后加入设备监控房间
            socket.on('connect', function() {
                socket.emit('join_device_monitor');
            });
            
            // 接收设备状态列表
            socket.on('device_status_list', function(data) {
                updateDeviceList(data.devices);
            });
            
            // 接收设备状态变更
            socket.on('device_status_changed', function(device) {
                updateDeviceStatus(device);
            });
            
            // 断开连接时的处理
            socket.on('disconnect', function() {
                console.log('与服务器的连接已断开');
            });
        }
        
        // 初始化设备列表
        function initDeviceList() {
            fetch(`/group/api/devices/${courseId}`)
                .then(response => response.json())
                .then(data => {
                    updateDeviceList(data.devices);
                })
                .catch(error => {
                    console.error('获取设备状态失败:', error);
                });
        }
        
        // 更新设备列表
        function updateDeviceList(devices) {
            // 清空现有设备列表
            document.getElementById('teacher-devices').innerHTML = '';
            document.getElementById('group-devices').innerHTML = '';
            document.getElementById('student-devices').innerHTML = '';
            
            // 更新设备状态统计
            let statusCounts = {
                online: 0,
                offline: 0,
                broadcasting: 0,
                receiving: 0
            };
            
            // 添加设备到列表
            devices.forEach(device => {
                // 更新状态计数
                statusCounts[device.status]++;
                
                // 创建设备卡片
                const deviceCard = createDeviceCard(device);
                
                // 根据设备角色添加到相应容器
                if (device.role === 'teacher') {
                    document.getElementById('teacher-devices').appendChild(deviceCard);
                } else if (device.role === 'group') {
                    document.getElementById('group-devices').appendChild(deviceCard);
                } else if (device.role === 'student') {
                    document.getElementById('student-devices').appendChild(deviceCard);
                }
            });
            
            // 更新状态计数显示
            document.getElementById('online-count').textContent = statusCounts.online;
            document.getElementById('offline-count').textContent = statusCounts.offline;
            document.getElementById('broadcasting-count').textContent = statusCounts.broadcasting;
            document.getElementById('receiving-count').textContent = statusCounts.receiving;
        }
        
        // 更新单个设备状态
        function updateDeviceStatus(device) {
            // 查找现有设备卡片
            const deviceCard = document.querySelector(`.device-card[data-device-id="${device.device_id}"]`);
            
            if (deviceCard) {
                // 更新现有卡片
                deviceCard.className = `device-card ${device.status}`;
                deviceCard.querySelector('.device-status').className = `device-status ${device.status}`;
                deviceCard.querySelector('.device-status').textContent = getStatusText(device.status);
                deviceCard.querySelector('.device-last-ping').textContent = formatDateTime(device.last_ping);
            } else {
                // 如果卡片不存在，重新获取完整设备列表
                initDeviceList();
            }
        }
        
        // 创建设备卡片
        function createDeviceCard(device) {
            const card = document.createElement('div');
            card.className = `device-card ${device.status}`;
            card.dataset.deviceId = device.device_id;
            
            const statusText = getStatusText(device.status);
            const lastPingTime = formatDateTime(device.last_ping);
            
            card.innerHTML = `
                <div class="device-name">${device.name}</div>
                <div class="device-status ${device.status}">${statusText}</div>
                <div class="device-info">
                    <div><strong>设备ID:</strong> ${device.device_id}</div>
                    <div><strong>IP地址:</strong> ${device.ip_address || '未知'}</div>
                    <div><strong>最后活动:</strong> <span class="device-last-ping">${lastPingTime}</span></div>
                </div>
            `;
            
            return card;
        }
        
        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 'online':
                    return '在线';
                case 'offline':
                    return '离线';
                case 'broadcasting':
                    return '广播中';
                case 'receiving':
                    return '接收中';
                default:
                    return '未知';
            }
        }
        
        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '未知';
            
            try {
                const date = new Date(dateTimeStr);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            } catch (e) {
                return dateTimeStr;
            }
        }
        
        // 初始化
        initSocketConnection();
        initDeviceList();
        
        // 定期刷新设备列表（作为备用机制）
        setInterval(initDeviceList, 30000);
    });
</script>
{% endblock %}