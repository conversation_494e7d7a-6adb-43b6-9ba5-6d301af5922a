from app import db, login_manager
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timezone, timedelta
from flask import current_app
import jwt

class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='student')  # 'teacher' or 'student'
    avatar = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    last_login = db.Column(db.DateTime)
    
    # 关系 - 使用字符串引用避免循环导入
    # courses_teaching = db.relationship('Course', backref='teacher', lazy='dynamic')  # 将在Course模型中定义
    
    @property
    def password(self):
        """密码属性不可读"""
        raise AttributeError('密码不可读')
    
    @password.setter
    def password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def verify_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def generate_auth_token(self, expires_in=3600):
        """生成认证Token"""
        try:
            payload = {
                'id': self.id,
                'exp': datetime.now(timezone.utc) + timedelta(seconds=expires_in)
            }
            return jwt.encode(
                payload,
                current_app.config['SECRET_KEY'],
                algorithm='HS256'
            )
        except Exception as e:
            return None

    @staticmethod
    def verify_auth_token(token):
        """验证认证Token"""
        try:
            payload = jwt.decode(
                token,
                current_app.config['SECRET_KEY'],
                algorithms=['HS256']
            )
            return User.query.get(payload['id'])
        except jwt.ExpiredSignatureError:
            return None  # Token已过期
        except jwt.InvalidTokenError:
            return None  # Token无效
    
    def is_teacher(self):
        """检查是否为教师"""
        return self.role == 'teacher'
    
    def is_student(self):
        """检查是否为学生"""
        return self.role == 'student'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'avatar': self.avatar,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
    
    def __repr__(self):
        return f'<User {self.username}>'

@login_manager.user_loader
def load_user(user_id):
    """加载用户"""
    return User.query.get(int(user_id))