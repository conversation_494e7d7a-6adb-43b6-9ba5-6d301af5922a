# 智慧教室系统设计文档

## 概述

智慧教室系统是一个现代化的教学环境解决方案，旨在通过数字化工具增强课堂教学体验，提高教学效率和学习效果。系统包括教师端、小组端和学生端三个主要组件，支持教师与学生之间的互动教学，以及学生之间的小组协作。

本设计文档详细描述了智慧教室系统的架构、组件、接口、数据模型、错误处理和测试策略，为系统的开发和实现提供指导。

## 架构

系统采用客户端-服务器架构，包括以下主要部分：

### 服务器端

1. **Web服务器**：基于Flask框架，提供RESTful API接口，处理客户端请求。
2. **数据库服务器**：存储用户信息、课程数据、教学资源和互动记录等。
3. **媒体服务器**：处理视频流传输，支持屏幕广播和画面分享功能。
4. **WebSocket服务器**：提供实时通信功能，支持即时消息传递和状态同步。

### 客户端

1. **教师端应用**：
   - Web应用：基于HTML+CSS+JavaScript开发，提供课前准备、课中管理和课后报告功能。
   - 桌面工具：基于PyQt5开发，提供屏幕广播、画笔工具和主副屏控制功能。

2. **小组端应用**：
   - Web应用：提供小组协作、画面分享和文件交换功能。
   - 桌面工具：提供白板协作和视频录制功能。

3. **学生端应用**：
   - Web应用：提供课堂参与、答题和资料查看功能。
   - 移动应用：提供移动设备上的课堂参与功能。

### 系统架构图

```mermaid
graph TD
    A[教师端] --> B[Web服务器]
    C[小组端] --> B
    D[学生端] --> B
    B --> E[数据库服务器]
    B --> F[媒体服务器]
    B --> G[WebSocket服务器]
    A <--> F
    C <--> F
    D <--> F
    A <--> G
    C <--> G
    D <--> G
```

## 组件和接口

### 1. 用户管理与课堂创建组件

#### 接口
- `createCourse(teacherId, courseName, options)` - 创建新课程并生成数字码
- `generateQRCode(courseId)` - 为课程生成二维码
- `joinCourse(studentId, courseCode)` - 学生通过数字码加入课程
- `joinCourseByQR(studentId, qrData)` - 学生通过扫描二维码加入课程

#### 设计决策
- 数字码长度可选择4位、6位或9位，平衡了便捷性和唯一性
- 使用标准QR码库生成二维码，确保兼容性
- 课程码有效期可配置，默认为课程持续时间

### 2. 教师课前准备组件

#### 接口
- `uploadResource(teacherId, courseId, file, metadata)` - 上传课件资源
- `manageResource(teacherId, resourceId, action)` - 管理课件（删除、重命名等）
- `createQuestion(teacherId, questionData)` - 创建习题
- `createExam(teacherId, examData)` - 创建试卷
- `createCoursePlan(teacherId, courseId, planData)` - 创建课程安排

#### 设计决策
- 支持多种文件格式上传，包括PDF、PPT、Word、图片等
- 使用分块上传技术处理大文件
- 习题创建支持富文本编辑器，便于添加公式和图片
- 试卷可从题库导入或手动创建

### 3. 教师课中广播组件

#### 接口
- `startBroadcast(teacherId, sourceType)` - 开始屏幕广播
- `stopBroadcast(teacherId)` - 停止屏幕广播
- `viewStudentScreens(teacherId, filters)` - 查看学生画面
- `selectScreensToDisplay(teacherId, screenIds)` - 选择特定画面展示
- `pauseScreen(teacherId, screenId)` - 暂停特定画面

#### 设计决策
- 使用WebRTC技术实现低延迟视频传输
- 实现自适应比特率，根据网络条件调整视频质量
- 采用分层架构设计，支持75+终端同时连接
- 优化视频编解码，确保1080P画质下平均延时不超过1.5秒

### 4. 教师课中看板组件

#### 接口
- `getGroupStatus(teacherId, courseId)` - 获取当前分组情况
- `getDeviceStatus(teacherId, courseId)` - 获取设备连接状态
- `manageGroups(teacherId, courseId, groupData)` - 管理小组成员
- `randomizeGroups(teacherId, courseId, options)` - 随机分组
- `getAttendance(teacherId, courseId)` - 获取学生考勤情况

#### 设计决策
- 使用拖拽界面实现直观的分组管理
- 设备状态实时更新，使用WebSocket保持连接状态同步
- 随机分组算法支持多种策略（均衡分配、完全随机等）

### 5. 教师与学生互动组件

#### 接口
- `sendQuestion(teacherId, courseId, questionData)` - 发送题目
- `getAnswerStatistics(teacherId, questionId)` - 获取答题统计
- `shareFile(teacherId, courseId, fileId, targetIds)` - 分享文件
- `randomPick(teacherId, courseId, options)` - 随机点名
- `startGroupDiscussion(teacherId, courseId, topicData)` - 设置分组讨论

#### 设计决策
- 支持截屏作为临时题目，减少教师准备工作
- 实时统计答题结果，使用图表直观展示
- 文件分享支持权限控制，可指定接收者
- 随机点名算法考虑历史点名频率，确保公平性

### 6. 教师评分组件

#### 接口
- `scoreStudent(teacherId, studentId, score, reason)` - 对学生评分
- `scoreGroup(teacherId, groupId, score, reason)` - 对小组评分

#### 设计决策
- 评分系统支持正负分，可添加评分理由
- 评分历史可追溯，便于后续分析
- 评分结果实时显示，增强激励效果

### 7. 教师课后报告组件

#### 接口
- `getClassReport(teacherId, courseId)` - 获取课堂报告
- `addReportNote(teacherId, reportId, note)` - 添加报告备注

#### 设计决策
- 报告按时间轴组织，直观展示课堂活动流程
- 支持多维度数据展示（签到、答题、互动等）
- 报告可导出为PDF或Excel格式

### 8. 小组内功能组件

#### 接口
- `startGroupBroadcast(groupId, sourceType)` - 小组开始广播
- `recordGroupDiscussion(groupId, options)` - 记录小组讨论
- `shareFileInGroup(studentId, groupId, fileId)` - 小组内分享文件
- `useWhiteboard(groupId, options)` - 使用白板应用

#### 设计决策
- 使用Excalidraw作为白板应用，提供丰富的绘图功能
- 小组讨论录制采用本地存储与云端备份结合的方式
- 白板协作使用操作转换算法(OT)确保一致性

### 9. 学生课中功能组件

#### 接口
- `shareScreen(studentId, targetType, targetId)` - 学生投屏
- `receiveInteraction(studentId)` - 接收教师互动
- `submitAnswer(studentId, questionId, answerData)` - 提交答案

#### 设计决策
- 学生投屏支持多种来源（摄像头、屏幕）
- 使用队列管理多个投屏请求，避免冲突
- 答题界面根据题型自动调整，提升用户体验

### 10. 学生课后功能组件

#### 接口
- `getStudentReport(studentId, courseId)` - 获取学生课堂报告
- `searchMaterials(studentId, filters)` - 搜索课程资料

#### 设计决策
- 资料搜索支持多种过滤条件和排序方式
- 课堂报告针对学生个人定制，突出个人表现

### 11. 小组间功能组件

#### 接口
- `shareScreenBetweenGroups(sourceGroupId, targetGroupIds)` - 小组间分享画面

#### 设计决策
- 小组间画面分享需经教师授权，确保课堂秩序
- 使用中继服务器优化多小组间的视频传输

### 12. 桌面教学工具改造组件

#### 架构设计
桌面教学工具采用模块化架构，支持登录认证和功能区分：

```mermaid
graph TD
    A[Core应用启动] --> B[通用功能模块]
    A --> C[登录认证模块]
    C --> D[教师专用功能模块]
    B --> E[UDP设备发现]
    B --> F[白板应用]
    B --> G[文件传输]
    B --> H[录屏截屏]
    B --> I[屏幕广播]
    D --> J[设备看板]
    D --> K[随机点名]
    D --> L[弹幕系统]
    D --> M[白板协作]
    D --> N[设备控制]
    D --> O[主副屏模式]
    D --> P[虚拟键盘]
    D --> Q[小组屏幕看板]
```

#### 通用功能接口
- `discoverDevices()` - UDP设备发现
- `useWhiteboard(deviceId, options)` - 使用白板应用
- `transferFile(sourceId, targetId, fileData)` - 文件传输
- `startScreenRecording(options)` - 开始录屏
- `takeScreenshot(options)` - 截屏
- `startScreenBroadcast(sourceId, targetIds, method)` - 屏幕广播

#### 教师专用功能接口
- `getDeviceDashboard()` - 获取设备看板
- `randomPickStudent(courseId)` - 随机点名
- `enableDanmaku(enable)` - 开启/关闭弹幕
- `startWhiteboardCollaboration(groupId)` - 开始白板协作
- `controlGroupDevice(deviceId, action)` - 控制小组设备
- `setDualScreenMode(mode)` - 设置主副屏模式
- `invokeVirtualKeyboard(targetId)` - 调起虚拟键盘
- `viewGroupScreens(groupIds)` - 查看小组屏幕

#### 设计决策
- 采用PyQt5框架开发，确保跨平台兼容性
- 使用UDP协议进行设备发现，支持局域网内自动发现
- 屏幕广播支持两种方式：传统screen_dlan_mod和新的ffmpeg+mediamtx
- 白板协作使用xdotool实现双向实时同步
- 虚拟键盘使用dbus-send命令调起Onboard
- 主副屏模式支持独立内容显示和联动控制
- 弹幕系统与后端WebSocket集成，支持消息持久化

### 13. 教师端设备主副屏模式组件

#### 接口
- `setDualScreenMode(teacherId, mode)` - 设置主副屏模式
- `syncDocumentPages(teacherId, direction)` - 同步课件翻页
- `configureScreenContent(teacherId, screenConfig)` - 配置屏幕内容

#### 设计决策
- 主副屏模式支持多种配置（镜像、扩展、独立）
- 课件翻页可选择同步或独立控制
- 支持屏幕内容动态切换，提高教学灵活性

## 数据模型

### 用户模型
```json
{
  "id": "string",
  "name": "string",
  "role": "enum(teacher, student)",
  "email": "string",
  "password": "string(hashed)",
  "avatar": "string(url)",
  "createdAt": "datetime",
  "lastLogin": "datetime"
}
```

### 课程模型
```json
{
  "id": "string",
  "name": "string",
  "teacherId": "string",
  "description": "string",
  "accessCode": "string",
  "qrCode": "string(url)",
  "startTime": "datetime",
  "endTime": "datetime",
  "status": "enum(scheduled, active, completed)",
  "createdAt": "datetime",
  "updatedAt": "datetime"
}
```

### 小组模型
```json
{
  "id": "string",
  "name": "string",
  "courseId": "string",
  "members": ["string(userId)"],
  "deviceId": "string",
  "createdAt": "datetime"
}
```

### 资源模型
```json
{
  "id": "string",
  "name": "string",
  "type": "enum(document, image, video, audio, other)",
  "url": "string",
  "ownerId": "string",
  "courseId": "string",
  "size": "number",
  "format": "string",
  "uploadedAt": "datetime",
  "metadata": "object"
}
```

### 习题模型
```json
{
  "id": "string",
  "type": "enum(single, multiple, truefalse, fillblank, subjective)",
  "content": "string",
  "options": ["string"],
  "answer": "any",
  "score": "number",
  "createdBy": "string(userId)",
  "createdAt": "datetime"
}
```

### 试卷模型
```json
{
  "id": "string",
  "name": "string",
  "questions": ["string(questionId)"],
  "totalScore": "number",
  "timeLimit": "number(minutes)",
  "createdBy": "string(userId)",
  "createdAt": "datetime"
}
```

### 答题记录模型
```json
{
  "id": "string",
  "questionId": "string",
  "studentId": "string",
  "answer": "any",
  "score": "number",
  "timeSpent": "number(seconds)",
  "submittedAt": "datetime"
}
```

### 课堂活动模型
```json
{
  "id": "string",
  "courseId": "string",
  "type": "enum(question, discussion, file, randomPick, broadcast)",
  "content": "object",
  "startTime": "datetime",
  "endTime": "datetime",
  "participants": ["string(userId)"]
}
```

### 评分记录模型
```json
{
  "id": "string",
  "targetType": "enum(student, group)",
  "targetId": "string",
  "score": "number",
  "reason": "string",
  "teacherId": "string",
  "createdAt": "datetime"
}
```

### 设备状态模型
```json
{
  "id": "string",
  "type": "enum(teacher, group, student)",
  "ownerId": "string",
  "status": "enum(online, offline, broadcasting, receiving)",
  "ipAddress": "string",
  "lastPing": "datetime"
}
```

## 错误处理

系统采用分层错误处理策略，确保错误信息准确传递并适当处理：

### 1. 前端错误处理
- 表单验证错误：在客户端进行初步验证，提供即时反馈
- 网络错误：检测连接问题，提供重试机制和离线模式
- UI错误：捕获渲染异常，提供降级UI

### 2. API错误处理
- 使用标准HTTP状态码表示错误类型
- 返回结构化错误响应，包含错误代码、消息和详情
- 实现幂等性API，确保操作可安全重试

### 3. 服务器错误处理
- 实现全局异常处理中间件
- 记录详细错误日志，包含上下文信息
- 关键错误自动报警通知

### 4. 视频传输错误处理
- 网络波动时自动调整视频质量
- 连接中断时尝试重新建立连接
- 提供备用传输通道

### 错误响应格式
```json
{
  "status": "error",
  "code": "ERROR_CODE",
  "message": "用户友好的错误消息",
  "details": "详细错误信息（仅开发环境）",
  "timestamp": "错误发生时间"
}
```

## 测试策略

系统测试采用多层次测试策略，确保各组件和整体系统的质量：

### 1. 单元测试
- 测试各组件的独立功能
- 使用模拟对象隔离外部依赖
- 覆盖核心业务逻辑和边界条件

### 2. 集成测试
- 测试组件间的交互
- 验证API契约
- 测试数据流和状态管理

### 3. 端到端测试
- 模拟真实用户场景
- 测试完整功能流程
- 验证UI和后端交互

### 4. 性能测试
- 测试系统在高负载下的表现
- 验证视频传输延迟不超过1.5秒
- 测试同时支持75+终端连接的能力

### 5. 安全测试
- 进行身份验证和授权测试
- 检查常见安全漏洞
- 验证数据加密和保护措施

### 6. 兼容性测试
- 测试不同浏览器和设备的兼容性
- 验证响应式设计在不同屏幕尺寸下的表现

### 测试自动化
- 使用CI/CD流程自动运行测试
- 实现关键功能的自动化测试
- 定期进行回归测试

## 技术选型

根据需求14的技术架构要求，系统将采用以下技术栈：

### Web应用
- **后端框架**：Flask
- **前端技术**：HTML + CSS + JavaScript
- **界面风格**：模仿Excalidraw，保持界面简洁干净
- **数据库**：SQLite（开发环境）/ PostgreSQL（生产环境）
- **WebSocket**：Flask-SocketIO
- **认证**：Flask-Login + JWT

### 桌面工具改造
- **框架**：PyQt5
- **视频处理**：OpenCV + FFmpeg
- **网络通信**：WebSockets + RESTful API + UDP
- **设备发现**：UDP广播协议
- **屏幕广播**：FFmpeg + MediaMTX
- **白板协作**：xdotool + 自定义同步协议
- **虚拟键盘**：dbus-send + Onboard
- **文件传输**：LocalSend集成或自定义TCP协议
- **录屏功能**：FFmpeg
- **设备控制**：SSH + 系统命令

### 视频传输
- **技术**：WebRTC + FFmpeg + MediaMTX
- **信令服务器**：基于Flask的自定义实现
- **TURN/STUN服务器**：coturn
- **流媒体服务器**：MediaMTX

### 白板应用
- **基础**：Excalidraw（开源白板工具）
- **协作**：自定义协作层，基于WebSocket + xdotool

## Core教学工具改造详细设计

### 1. 应用启动与认证架构

#### 启动流程
```mermaid
graph TD
    A[Core应用启动] --> B[加载通用功能模块]
    B --> C[显示主界面]
    C --> D[用户选择登录?]
    D -->|是| E[显示登录界面]
    D -->|否| F[仅显示通用功能]
    E --> G[验证教师身份]
    G -->|成功| H[加载教师专用功能]
    G -->|失败| I[显示错误信息]
    H --> J[显示完整功能界面]
```

#### 认证机制
- 使用JWT令牌进行身份验证
- 支持本地缓存登录状态
- 教师登录后获取课程权限
- 小组设备默认匿名模式

### 2. UDP设备发现机制

#### 发现协议设计
```python
# 设备发现消息格式
{
    "type": "device_discovery",
    "device_id": "unique_device_id",
    "device_type": "teacher|group|student",
    "device_name": "设备名称",
    "ip_address": "*************",
    "port": 8080,
    "capabilities": ["whiteboard", "screen_share", "file_transfer"],
    "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 实现策略
- 使用UDP广播在局域网内发现设备
- 定期发送心跳包维持设备状态
- 支持设备上线/下线通知
- 维护设备列表缓存

### 3. 屏幕广播双模式设计

#### 传统模式 (screen_dlan_mod)
- 保留现有实现
- 适用于简单的一对多广播
- 低延迟，适合实时演示

#### 新模式 (FFmpeg + MediaMTX)
```mermaid
graph TD
    A[屏幕捕获] --> B[FFmpeg编码]
    B --> C[MediaMTX流媒体服务器]
    C --> D[设备A接收]
    C --> E[设备B接收]
    C --> F[设备C接收]
```

- 支持任意设备间广播
- 更好的扩展性和画质
- 支持多路并发广播

### 4. 白板协作同步机制

#### 协作架构
```mermaid
graph TD
    A[教师设备白板] --> B[xdotool捕获操作]
    B --> C[操作数据序列化]
    C --> D[WebSocket传输]
    D --> E[小组设备接收]
    E --> F[xdotool重放操作]
    F --> G[小组设备白板更新]
    G --> H[反向同步到教师设备]
```

#### 同步策略
- 使用操作转换算法确保一致性
- 支持冲突检测和解决
- 实现撤销/重做功能同步
- 优化网络传输减少延迟

### 5. 弹幕系统设计

#### 消息流程
```mermaid
graph TD
    A[学生Web端发送消息] --> B[后端WebSocket服务器]
    B --> C[消息持久化到数据库]
    C --> D[广播到Core教学工具]
    D --> E[弹幕显示引擎]
    E --> F[屏幕弹幕显示]
```

#### 持久化机制
- 使用数据库存储聊天消息
- 支持消息历史查询
- 实现消息状态管理
- 支持消息过滤和审核

### 6. 主副屏模式实现

#### 屏幕管理
```python
# 屏幕配置数据结构
{
    "primary_screen": {
        "display_id": 0,
        "content_type": "courseware",
        "current_page": 1
    },
    "secondary_screen": {
        "display_id": 1,
        "content_type": "whiteboard",
        "tools_visible": true
    },
    "sync_mode": "page_linked"  # 页面联动模式
}
```

#### 功能特性
- 支持独立内容显示
- 课件翻页联动控制
- 白板与课件分屏显示
- 动态切换屏幕内容

### 7. 虚拟键盘集成

#### 调用机制
```python
# 虚拟键盘调用命令
cmd = [
    'dbus-send', 
    '--session',
    '--dest=org.onboard.Onboard',
    '--type=method_call',
    '/org/onboard/Onboard/Keyboard',
    'org.onboard.Onboard.Keyboard.ToggleVisible'
]
```

#### 远程控制
- 支持跨设备键盘调用
- 实现输入内容同步
- 提供键盘状态监控
- 支持多语言输入法

### 8. 文件传输系统

#### 传输方式选择
1. **LocalSend集成**
   - 利用现有成熟方案
   - 支持跨平台传输
   - 用户界面友好

2. **自定义实现**
   - 基于TCP协议
   - 支持断点续传
   - 更好的集成度

#### 传输协议
```python
# 文件传输协议
{
    "action": "file_transfer",
    "file_id": "unique_file_id",
    "file_name": "document.pdf",
    "file_size": 1024000,
    "chunk_size": 8192,
    "source_device": "teacher_device",
    "target_devices": ["group_1", "group_2"]
}
```

## 部署架构

系统部署采用模块化架构，便于扩展和维护：

```mermaid
graph TD
    A[负载均衡器] --> B[Web服务器集群]
    A --> C[WebSocket服务器集群]
    A --> D[媒体服务器集群]
    B --> E[数据库服务器]
    C --> E
    D --> F[对象存储]
    B --> F
```

### 扩展性考虑
- 使用容器化技术（Docker）封装各组件
- 支持水平扩展关键服务
- 实现服务发现机制，动态管理服务实例

### 高可用性设计
- 关键服务多实例部署
- 实现故障转移机制
- 定期数据备份和恢复测试

## 安全考虑

系统实现多层次安全防护，保护用户数据和系统资源：

### 1. 身份验证与授权
- 实现强密码策略
- 使用JWT进行API认证
- 基于角色的访问控制（RBAC）

### 2. 数据安全
- 敏感数据加密存储
- 传输数据使用TLS/SSL加密
- 实现数据备份和恢复机制

### 3. 应用安全
- 防止常见Web攻击（XSS、CSRF、SQL注入等）
- 实现请求速率限制
- 定期安全审计和漏洞扫描

### 4. 设备控制安全
- 设备控制操作需要额外认证
- 记录所有控制操作日志
- 实现操作确认机制

## 性能优化

系统针对关键场景进行性能优化，确保流畅的用户体验：

### 1. 视频传输优化
- 使用WebRTC实现P2P连接，减少服务器负载
- 实现自适应比特率，根据网络条件调整视频质量
- 优化编解码器配置，降低延迟

### 2. 前端优化
- 资源懒加载和代码分割
- 使用CDN分发静态资源
- 实现前端缓存策略

### 3. 后端优化
- 数据库索引优化
- 实现查询缓存
- 使用异步处理长时间任务

### 4. 网络优化
- 使用HTTP/2减少连接开销
- 实现内容压缩
- 优化API请求批处理

## 可访问性设计

系统设计考虑不同用户的可访问性需求：

### 1. 界面可访问性
- 符合WCAG 2.1标准
- 支持键盘导航
- 提供高对比度模式

### 2. 内容可访问性
- 图像提供替代文本
- 视频提供字幕选项
- 支持屏幕阅读器

### 3. 操作可访问性
- 提供多种交互方式
- 操作超时可配置
- 错误提示清晰明确

## 国际化与本地化

系统支持多语言和本地化需求：

### 1. 多语言支持
- 实现i18n框架
- 支持动态切换语言
- 分离UI文本和代码

### 2. 本地化考虑
- 支持不同日期和时间格式
- 适应不同文化的用户界面设计
- 考虑文本扩展空间

## 未来扩展

系统设计考虑未来可能的扩展方向：

### 1. 功能扩展
- AI辅助教学分析
- 虚拟现实(VR)教学场景
- 更多第三方教育工具集成

### 2. 技术扩展
- 移动应用原生版本
- 离线模式支持
- 边缘计算优化

### 3. 集成扩展
- 与学校管理系统集成
- 与学习管理系统(LMS)集成
- 开放API生态系统