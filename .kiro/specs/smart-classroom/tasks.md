# 实现计划

## 用户管理与课堂创建

- [-] 1. 设计并实现用户认证系统
  - 创建用户模型和数据库表结构
  - 实现用户注册、登录和权限控制
  - _需求: 1_

- [x] 2. 实现课程创建和加入功能
  - [x] 2.1 实现课程创建API
    - 开发创建课程的后端API
    - 实现数字码生成算法（支持4位、6位或9位）
    - 编写单元测试验证API功能
    - _需求: 1.1_
  
  - [x] 2.2 实现二维码生成功能
    - 集成二维码生成库
    - 开发二维码生成API
    - 编写单元测试验证二维码生成功能
    - _需求: 1.2_
  
  - [x] 2.3 实现课程加入功能
    - 开发通过数字码加入课程的API
    - 开发通过二维码加入课程的API
    - 编写单元测试验证加入功能
    - _需求: 1.3, 1.4_

## 教师课前准备功能

- [x] 3. 实现资源管理系统
  - [x] 3.1 开发文件上传功能
    - 实现多种格式文件上传支持
    - 开发分块上传大文件功能
    - 编写单元测试验证上传功能
    - _需求: 2.1_
  
  - [x] 3.2 实现文件管理功能
    - 开发文件操作API（上传、下载、删除、重命名）
    - 创建文件管理界面
    - 编写单元测试验证文件操作
    - _需求: 2.2_

- [x] 4. 实现习题和试卷系统
  - [x] 4.1 开发习题创建功能
    - 实现多种题型支持（单选、多选、判断、填空、主观题）
    - 开发富文本编辑器集成
    - 编写单元测试验证习题创建
    - _需求: 2.3_
  
  - [x] 4.2 开发试卷创建功能
    - 实现从题库导入习题功能
    - 开发手动创建试卷功能
    - 编写单元测试验证试卷创建
    - _需求: 2.4_
  
  - [x] 4.3 实现课程安排功能
    - 开发课程安排创建API
    - 创建课程安排界面
    - 编写单元测试验证课程安排功能
    - _需求: 2.5_

## 教师课中广播功能

- [-] 5. 实现视频传输系统
  - [x] 5.1 集成WebRTC技术
    - 实现WebRTC信令服务器
    - 开发P2P连接建立功能
    - 编写单元测试验证连接建立
    - _需求: 3.1, 3.5_
  
  - [x] 5.2 实现屏幕广播功能
    - 开发教师端屏幕捕获功能
    - 实现广播流分发机制
    - 编写单元测试验证广播功能
    - _需求: 3.1_
  
  - [x] 5.3 实现学生画面查看功能
    - 开发多画面同时显示功能
    - 实现画面选择和对比功能
    - 编写单元测试验证画面查看功能
    - _需求: 3.2, 3.3_
  
  - [x] 5.4 实现画面控制功能
    - 开发画面暂停功能
    - 实现画面控制界面
    - 编写单元测试验证画面控制功能
    - _需求: 3.4_
  
  - [ ] 5.5 优化视频传输性能
    - 实现自适应比特率调整
    - 优化编解码器配置
    - 进行性能测试验证延迟要求
    - _需求: 3.5_

## 教师课中看板功能

- [x] 6. 实现课堂管理系统
  - [x] 6.1 开发分组管理功能
    - 实现分组情况显示
    - 开发拖拽分组界面
    - 编写单元测试验证分组管理功能
    - _需求: 4.1, 4.3_
  
  - [x] 6.2 实现设备状态监控
    - 开发WebSocket实时状态更新
    - 创建设备状态显示界面
    - 编写单元测试验证状态监控功能
    - _需求: 4.2_
  
  - [x] 6.3 实现随机分组功能
    - 开发多种随机分组算法
    - 创建随机分组界面
    - 编写单元测试验证随机分组功能
    - _需求: 4.3_
  
  - [x] 6.4 实现考勤管理功能
    - 开发考勤记录API
    - 创建考勤显示界面
    - 编写单元测试验证考勤功能
    - _需求: 4.4_

## 教师与学生互动功能

- [x] 7. 实现互动教学系统
  - [x] 7.1 开发题目发送功能
    - 实现从资源库选择习题功能
    - 开发临时创建题目功能
    - 实现屏幕截取作为题目功能
    - 编写单元测试验证题目发送功能
    - _需求: 5.1_
  
  - [x] 7.2 实现答题统计功能
    - 开发自动评分系统
    - 创建答题结果统计图表
    - 实现按全班或分组切换展示功能
    - 编写单元测试验证答题统计功能
    - _需求: 5.2, 5.3_
  
  - [x] 7.3 实现文件分享功能
    - 开发文件分享API
    - 创建文件分享界面
    - 编写单元测试验证文件分享功能
    - _需求: 5.4_
  
  - [x] 7.4 实现随机点名功能
    - 开发随机点名算法
    - 创建随机点名界面
    - 编写单元测试验证随机点名功能
    - _需求: 5.5_
  
  - [x] 7.5 实现分组讨论功能
    - 开发讨论主题发送API
    - 实现讨论倒计时功能
    - 编写单元测试验证分组讨论功能
    - _需求: 5.6_

## 教师评分功能

- [x] 8. 实现评分系统
  - [x] 8.1 开发个人评分功能
    - 实现学生评分API
    - 创建评分界面
    - 编写单元测试验证个人评分功能
    - _需求: 6.1_
  
  - [x] 8.2 开发小组评分功能
    - 实现小组评分API
    - 扩展评分界面支持小组评分
    - 编写单元测试验证小组评分功能
    - _需求: 6.1_
  
  - [x] 8.3 集成随机点名与评分功能
    - 实现点名后评分流程
    - 编写单元测试验证集成功能
    - _需求: 6.2_

## 教师课后报告功能

- [x] 9. 实现课堂报告系统
  - [x] 9.1 开发课堂活动记录功能
    - 实现活动记录数据模型
    - 开发活动记录API
    - 编写单元测试验证活动记录功能
    - _需求: 7.1_
  
  - [x] 9.2 实现报告生成功能
    - 开发多维度数据统计功能
    - 创建报告展示界面
    - 编写单元测试验证报告生成功能
    - _需求: 7.2, 7.4_
  
  - [x] 9.3 实现报告备注功能
    - 开发报告备注API
    - 创建备注添加界面
    - 编写单元测试验证备注功能
    - _需求: 7.3_

## 小组内功能

- [-] 10. 实现小组协作系统
  - [x] 10.1 开发小组广播功能
    - 实现小组端屏幕捕获功能
    - 开发小组广播API
    - 编写单元测试验证小组广播功能
    - _需求: 8.1_
  
  - [x] 10.2 实现讨论录制功能
    - 开发视频录制功能
    - 实现本地存储与云端备份机制
    - 编写单元测试验证录制功能
    - _需求: 8.2_
  
  - [x] 10.3 实现小组内文件分享功能
    - 开发小组内文件分享API
    - 创建文件分享界面
    - 编写单元测试验证文件分享功能
    - _需求: 8.3_
  
  - [x] 10.4 集成Excalidraw白板应用
    - 集成Excalidraw库
    - 实现操作转换算法确保一致性
    - 开发白板协作功能
    - 编写单元测试验证白板功能
    - _需求: 8.4_

## 学生课中功能

- [x] 11. 实现学生参与系统
  - [x] 11.1 开发学生投屏功能
    - 实现摄像头捕获功能
    - 实现屏幕捕获功能
    - 开发多画面来源切换功能
    - 编写单元测试验证投屏功能
    - _需求: 9.1_
  
  - [x] 11.2 实现互动接收功能
    - 开发互动内容接收API
    - 创建互动内容展示界面
    - 编写单元测试验证互动接收功能
    - _需求: 9.2_
  
  - [x] 11.3 实现答题功能
    - 开发题目接收API
    - 创建自适应答题界面
    - 实现答案提交功能
    - 编写单元测试验证答题功能
    - _需求: 9.3_

## 学生课后功能

- [x] 12. 实现学生课后系统
  - [x] 12.1 开发学生报告功能
    - 实现学生个人报告生成API
    - 创建报告展示界面
    - 编写单元测试验证报告功能
    - _需求: 10.1_
  
  - [x] 12.2 实现资料搜索功能
    - 开发多条件资料搜索API
    - 创建资料搜索界面
    - 实现排序和筛选功能
    - 编写单元测试验证搜索功能
    - _需求: 10.2, 10.3_

## 小组间功能

- [ ] 13. 实现小组间交流系统
  - [ ] 13.1 开发小组间画面分享功能
    - 实现小组间视频传输功能
    - 开发教师授权机制
    - 实现中继服务器优化传输
    - 编写单元测试验证画面分享功能
    - _需求: 11.1_

## Core教学工具改造

- [x] 14. 实现Core教学工具基础架构改造
  - [x] 14.1 实现应用启动与认证系统
    - 重构Core应用启动流程，支持通用功能默认显示
    - 实现教师登录认证界面和JWT令牌验证
    - 开发功能模块动态加载机制
    - 编写单元测试验证认证流程
    - _需求: 12_
  
  - [x] 14.2 实现模块化架构重构
    - 将现有功能重构为通用功能模块
    - 创建教师专用功能模块框架
    - 实现模块间通信机制
    - 编写单元测试验证模块加载
    - _需求: 12_

- [-] 15. 实现通用功能模块
  - [x] 15.1 开发UDP设备发现功能
    - 实现UDP广播协议进行设备发现
    - 开发设备列表管理和状态维护
    - 创建设备发现界面显示
    - 编写单元测试验证设备发现功能
    - _需求: 12.1_
  
  - [x] 15.2 重构和增强白板应用模块
    - 基于现有core/modules/whiteboard进行重构
    - 优化白板绘制性能和用户体验
    - 实现白板内容保存和加载功能
    - 编写单元测试验证白板功能
    - _需求: 12.1_
  
  - [x] 15.3 实现文件传输功能
    - 集成LocalSend或开发自定义文件传输协议
    - 实现设备间文件传输界面
    - 支持多种文件格式传输和进度显示
    - 编写单元测试验证文件传输功能
    - _需求: 12.1_
  
  - [x] 15.4 实现录屏功能
    - 集成FFmpeg实现屏幕录制功能
    - 开发录屏控制界面（开始、暂停、停止）
    - 支持录屏文件格式选择和质量设置
    - 编写单元测试验证录屏功能
    - _需求: 12.1_
  
  - [x] 15.5 实现截屏功能
    - 开发屏幕截图功能和快捷键支持
    - 实现截图编辑和标注功能
    - 支持截图保存和分享功能
    - 编写单元测试验证截屏功能
    - _需求: 12.1_
  
  - [x] 15.6 实现双模式屏幕广播功能
    - 保留现有screen_dlan_mod广播方式
    - 新增FFmpeg + MediaMTX广播模式
    - 实现任意设备间屏幕广播功能
    - 编写单元测试验证双模式广播功能
    - _需求: 12.1_

- [ ] 16. 实现教师专用功能模块
  - [ ] 16.1 开发设备看板功能
    - 结合UDP设备发现实现设备状态监控
    - 创建设备连接情况可视化界面
    - 实现设备状态实时更新和告警
    - 编写单元测试验证设备看板功能
    - _需求: 12.2_
  
  - [ ] 16.2 实现随机点名功能
    - 集成后端API获取当前课程学生列表
    - 开发随机点名算法和界面
    - 实现点名历史记录和统计功能
    - 编写单元测试验证随机点名功能
    - _需求: 12.2_
  
  - [ ] 16.3 实现弹幕系统
    - 开发弹幕显示引擎和动画效果
    - 集成后端WebSocket接收学生消息
    - 完善后端消息持久化机制
    - 编写单元测试验证弹幕功能
    - _需求: 12.2_
  
  - [ ] 16.4 扩展白板协作功能
    - 在现有白板基础上增加协作开关
    - 使用xdotool实现双向实时同步
    - 开发小组选择和协作管理界面
    - 编写单元测试验证白板协作功能
    - _需求: 12.2_
  
  - [ ] 16.5 修改设备控制功能
    - 基于现有control_pc功能进行改造
    - 实现教师设备对小组设备的开关机控制
    - 增强安全认证和操作确认机制
    - 编写单元测试验证设备控制功能
    - _需求: 12.2_
  
  - [ ] 16.6 实现主副屏模式功能
    - 开发双屏检测和配置功能
    - 实现主副屏不同内容显示
    - 支持课件上下页联动控制
    - 实现一屏课件一屏白板模式
    - 编写单元测试验证主副屏功能
    - _需求: 12.2_
  
  - [ ] 16.7 实现虚拟键盘功能
    - 使用dbus-send命令调起Onboard虚拟键盘
    - 实现教师与小组设备间互相调用键盘
    - 开发键盘状态监控和输入同步
    - 编写单元测试验证虚拟键盘功能
    - _需求: 12.2_
  
  - [ ] 16.8 开发小组屏幕看板功能
    - 集成MediaMTX接收小组设备广播流
    - 实现多路视频流同时显示
    - 开发屏幕切换和布局管理功能
    - 编写单元测试验证屏幕看板功能
    - _需求: 12.2_

- [ ] 17. Core教学工具集成与优化
  - [ ] 17.1 实现功能模块集成测试
    - 测试通用功能与教师专用功能的协同工作
    - 验证登录认证对功能访问的控制
    - 测试设备间通信和数据同步
    - 编写集成测试验证整体功能
    - _需求: 12, 12.1, 12.2_
  
  - [ ] 17.2 实现性能优化和稳定性改进
    - 优化UDP设备发现的网络性能
    - 改进屏幕广播的延迟和画质
    - 优化白板协作的同步性能
    - 编写性能测试验证优化效果
    - _需求: 12.1, 12.2_
  
  - [ ] 17.3 实现用户界面优化
    - 统一各功能模块的界面风格
    - 优化用户操作流程和体验
    - 实现界面响应式设计和适配
    - 编写用户界面测试验证体验
    - _需求: 12, 12.1, 12.2_
  
  - [ ] 17.4 实现配置管理和部署优化
    - 开发应用配置管理系统
    - 实现一键部署和更新机制
    - 创建用户使用文档和帮助系统
    - 编写部署测试验证安装流程
    - _需求: 12_

## 技术架构实现

- [x] 16. 搭建基础技术架构
  - [x] 16.1 搭建Web应用框架
    - 配置Flask开发环境
    - 实现基础项目结构
    - 规范存放测试文件和文档文件
    - 设计Excalidraw风格的界面
    - 编写单元测试验证基础功能
    - _需求: 14.1_
  
  - [x] 16.2 搭建桌面工具框架
    - 配置PyQt5开发环境
    - 实现基础项目结构
    - 编写单元测试验证基础功能
    - _需求: 14.2_