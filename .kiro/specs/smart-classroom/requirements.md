# 需求文档

## 简介

研讨型智慧教室系统旨在提供一个现代化的教学环境，支持教师与学生之间的互动教学，以及学生之间的小组协作。系统包括教师端、小组端和学生端三个主要组件，通过数字化工具增强课堂教学体验，提高教学效率和学习效果。

## 需求

### 需求1：用户管理与课堂创建

**用户故事:** 作为一名教师，我希望能够创建课程并让学生轻松加入，以便快速组织课堂活动。

#### 验收标准

1. 当教师需要创建课程时，系统应生成4位、6位或9位数字码。
2. 当系统生成数字码后，系统应将其转换为二维码供学生扫描。
3. 当教师选择已创建的课程时，系统应显示该课程的数字码或二维码。
4. 当学生输入课堂数字码或扫描二维码时，系统应允许学生加入相应课堂。

### 需求2：教师课前准备功能

**用户故事:** 作为一名教师，我希望能够在课前准备教学资源，以便在课堂上高效使用。

#### 验收标准

1. 当教师访问资源库时，系统应提供课件上传功能，支持多种格式文件。
2. 当教师管理课件时，系统应支持基本文件操作（上传、下载、删除、重命名等）。
3. 当教师创建习题时，系统应支持单选、多选、判断、填空和主观题型。
4. 当教师创建试卷时，系统应允许导入多道习题或手动创建各种题型。
5. 当教师需要规划课程时，系统应提供创建课程安排的功能。

### 需求3：教师课中广播功能

**用户故事:** 作为一名教师，我希望能够进行屏幕广播和查看学生画面，以便有效监控和指导课堂活动。

#### 验收标准

1. 当教师需要广播时，系统应支持教师端大屏广播至小组端和学生端。
2. 当教师需要查看学生画面时，系统应显示所有小组端和学生端画面。
3. 当教师选择特定画面时，系统应允许展示和对比任意个画面。
4. 当教师需要时，系统应允许暂停画面。
5. 当进行视频流传输时，系统应支持不少于75台终端的画面广播同屏，画质不低于1080P，且平均同屏延时应在1.5秒内。

### 需求4：教师课中看板功能

**用户故事:** 作为一名教师，我希望能够管理课堂分组和学生考勤，以便有效组织课堂活动。

#### 验收标准

1. 当教师查看看板时，系统应显示当前教室分组情况。
2. 当教师查看看板时，系统应显示小组端设备连接状态。
3. 当教师管理小组成员时，系统应支持拖拽分组和随机分组功能。
4. 当教师查看考勤时，系统应显示学生考勤名单。

### 需求5：教师与学生互动功能

**用户故事:** 作为一名教师，我希望能够与学生进行多种形式的互动，以便提高课堂参与度和教学效果。

#### 验收标准

1. 当教师发送题目时，系统应支持从资源库选择习题或试卷，或课上临时创建题目，或截取屏幕作为临时题目。
2. 当学生答题结束后，系统应自动统计客观题分数，显示主观题答案。
3. 当查看答题报告时，系统应实时显示答题结果柱状图，并支持按全班或分组切换展示。
4. 当教师需要分享文件时，系统应允许将资源库或本地文件发送给学生。
5. 当教师需要点名时，系统应提供随机点名功能。
6. 当教师设置分组讨论时，系统应允许截取任意内容作为讨论主题发送给各个小组和学生，并设置讨论倒计时。

### 需求6：教师评分功能

**用户故事:** 作为一名教师，我希望能够对学生的课堂表现进行评分，以便激励学生参与和记录学生表现。

#### 验收标准

1. 当教师需要评分时，系统应允许根据学生的课堂表现对个人或小组进行加减分。
2. 当教师在随机点名互动后，系统应允许对选中的学生进行评分。

### 需求7：教师课后报告功能

**用户故事:** 作为一名教师，我希望能够查看课堂报告，以便分析课堂效果和学生表现。

#### 验收标准

1. 当教师查看课后报告时，系统应以时间顺序展示各个课堂活动。
2. 当教师查看课后报告时，系统应显示签到人数、题目与答题结果、提问记录、学生互动次数。
3. 当教师需要添加备注时，系统应支持在报告中添加备注。
4. 当教师查看课后报告时，系统应显示评分和选人情况。

### 需求8：小组内功能

**用户故事:** 作为一个学习小组，我们希望能够进行小组内协作和与其他小组的交流，以便提高小组学习效率。

#### 验收标准

1. 当小组需要广播时，系统应支持小组大屏广播到教师端大屏和其他小组端大屏。
2. 当小组需要记录讨论过程时，系统应支持对整个小组讨论过程进行视频录制。
3. 当小组成员需要分享文件时，系统应支持小组内成员进行文件分享。
4. 当小组需要使用白板时，系统应提供Excalidraw作为白板应用，并支持小组内成员共同操作一个白板。

### 需求9：学生课中功能

**用户故事:** 作为一名学生，我希望能够参与课堂互动和小组协作，以便提高学习效果。

#### 验收标准

1. 当学生需要投屏时，系统应支持捕获摄像头或当前设备屏幕，投屏到小组或教师端大屏，可以同时显示多个画面来源。
2. 当教师发起互动时，系统应支持学生接收教师发送的文字、图片、文件。
3. 当教师发送题目时，系统应支持学生接收并答题。

### 需求10：学生课后功能

**用户故事:** 作为一名学生，我希望能够查看课堂报告和课中资料，以便复习和巩固所学知识。

#### 验收标准

1. 当学生查看课后报告时，系统应显示课堂活动记录。
2. 当学生需要查找课中过程性资料时，系统应支持查找批注、板书、文件、快照等。
3. 当学生筛选资料时，系统应支持按课程、资料类型、关键字进行筛选，可按时间排序。

### 需求11：小组间功能

**用户故事:** 作为不同的学习小组，我们希望能够相互分享画面，以便促进小组间的交流和学习。

#### 验收标准

1. 当小组需要分享画面时，系统应支持小组的画面分享给其他小组。

### 需求12：桌面教学工具改造

**用户故事:** 作为教师和学生，我们希望能够使用改造后的桌面教学工具进行教学和学习，该工具运行在教师设备和小组设备上，支持登录认证和功能区分，以便提高教学和学习效率。

#### 验收标准

1. 当core应用启动时，系统应默认显示不需要登录认证的通用功能。
2. 当教师需要登录时，系统应提供登录功能，登录后可显示通用功能和教师专属功能。
3. 当小组设备使用时，系统应支持无需登录认证的公用模式。

### 需求12.1：通用功能

**用户故事:** 作为教师和小组设备用户，我们希望能够使用通用功能进行基础教学活动，以便在局域网内进行设备协作。

#### 验收标准

1. 当需要设备发现时，系统应提供UDP发现功能，用于教师和小组设备在局域网内互相发现。
2. 当用户需要使用白板时，系统应提供白板应用（core/modules/whiteboard），用于在各自设备上书写。
3. 当用户需要传输文件时，系统应提供文件传输功能，可依赖localsend或自定义实现，用于各设备间传输文件。
4. 当用户需要录屏时，系统应提供基于ffmpeg的录屏功能。
5. 当用户需要截屏时，系统应提供截屏功能。
6. 当用户需要屏幕广播时，系统应保留screen_dlan_mod方式，并增加使用ffmpeg + mediamtx方式的模块，可实现任意设备间屏幕广播（A到B设备，B到A设备，A到A、B、C设备等）。

### 需求12.2：教师专用功能

**用户故事:** 作为教师，我希望在登录后能够使用专属功能进行高级教学管理，以便更好地控制课堂和与学生互动。

#### 验收标准

1. 当教师登录后，系统应提供设备看板功能，结合UDP监看当前教室小组设备连接情况。
2. 当教师需要点名时，系统应提供随机点名功能，结合后端API实现，可随机选择当前课程中的学生。
3. 当教师开启弹幕功能时，学生通过后端broadcast/student界面的聊天工具发送的消息应在屏幕上以弹幕形式显示，同时完善后端消息持久化机制，确保浏览器刷新页面后消息不会消失。
4. 当教师需要白板协作时，系统应扩展core/modules/whiteboard，增加协作开关，开启后教师可选择与某个小组协作，教师设备与小组设备间进行板书协作，各自显示屏上的书写内容可双向实时同步，依赖xdotool实现。
5. 修改control_pc功能，当教师需要控制小组设备时，教师设备可控制小组设备开关机。
6. 当教师使用主副屏模式时，系统应支持教师端设备连接2个屏幕，主屏用于显示课件内容，副屏用于显示教师工具，主副屏可展示不同的课件内容，支持课件上下页联动，支持一屏展示课件、另一屏启用白板应用进行书写。
7. 当需要虚拟键盘时，系统应支持教师选择并调起某个小组的虚拟键盘并进行输入，小组也可调起教师的虚拟键盘并输入，使用dbus-send命令调起虚拟键盘。
8. 当教师需要查看小组屏幕时，系统应提供小组屏幕看板（group_screen），小组通过mediamtx广播时教师可实时查看，支持多路查看。

### 需求13：教师端设备主副屏模式

**用户故事:** 作为一名教师，我希望能够使用主副屏模式进行教学，以便更灵活地展示教学内容。

#### 验收标准

1. 当教师使用主副屏模式时，系统应支持主副屏展示不同的课件内容。
2. 当教师翻页课件时，系统应支持课件上下页联动。
3. 当教师需要同时展示课件和白板时，系统应支持一屏展示课件，另一屏启用白板应用进行书写。
4. 当教师需要屏幕广播时，系统应支持双屏展示的文档对象进行画面广播，2个屏幕同时显示一个屏幕的内容。

### 需求14：技术架构要求

**用户故事:** 作为系统开发者，我希望使用指定的技术架构开发系统，以便确保系统的兼容性和可维护性。

#### 验收标准

1. 当开发Web应用时，系统应使用Flask+HTML+CSS+JavaScript技术栈。
2. 当开发桌面工具时，系统应使用PyQt5框架。